apiVersion: apps/v1
kind: Deployment
metadata:
  name: relayer
  namespace: "dcbg-ibc"
  labels:
    app: "relayer"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: "relayer"
  template:
    metadata:
      name: "relayer_pod"
      labels:
        app: "relayer"
    spec:
      shareProcessNamespace: true
      initContainers:
        - name: "relayer-handshake"
          command: [ "/opt/relayer/scripts/handshaker" ]
          args: [ "account-sync,balance-sync,token-transfer" ]
          image: "test/local-relayer:0.0.2"
          imagePullPolicy: Never
          env:
            - name: RLY_BIN
              value: "/bin/rly"
            - name: RLY_CONFIG_HOME
              value: "/opt/relayer/.relayer"
            - name: RLY
              value: "/bin/rly --home /opt/relayer/.local"
            - name: SRC_CHAIN_ID
              value: "********"
            - name: DST_CHAIN_ID
              value: "********"
            - name: CHAIN_1_RPC_ADDRESS
              value: "http://host.docker.internal:18451"
            - name: CHAIN_1_ETH_CHAIN_ID
              value: "5151"
            - name: CHAIN_2_RPC_ADDRESS
              value: "http://host.docker.internal:28451"
            - name: CHAIN_2_ETH_CHAIN_ID
              value: "5152"
            - name: CHAIN_1_IBC_ADDRESS
              value: "******************************************"
            - name: CHAIN_2_IBC_ADDRESS
              value: "******************************************"
          resources:
            requests:
              cpu: "1"
              memory: "2Gi"
            limits:
              cpu: "1"
              memory: "2Gi"
      containers:
        - name: relayer-account-sync
          command: [ "/opt/relayer/scripts/starter" ]
          args: [ "account-sync" ]
          image: "test/local-relayer:0.0.2"
          imagePullPolicy: Never
          env:
            - name: RLY_BIN
              value: "/bin/rly"
            - name: RLY_CONFIG_HOME
              value: "/opt/relayer/.relayer"
            - name: RLY
              value: "/bin/rly --home /opt/relayer/.local"
            - name: SRC_CHAIN_ID
              value: "********"
            - name: DST_CHAIN_ID
              value: "********"
            - name: CHAIN_1_RPC_ADDRESS
              value: "http://host.docker.internal:18451"
            - name: CHAIN_1_ETH_CHAIN_ID
              value: "5151"
            - name: CHAIN_2_RPC_ADDRESS
              value: "http://host.docker.internal:28451"
            - name: CHAIN_2_ETH_CHAIN_ID
              value: "5152"
            - name: CHAIN_1_IBC_ADDRESS
              value: "******************************************"
            - name: CHAIN_2_IBC_ADDRESS
              value: "******************************************"
          resources:
            requests:
              cpu: "1"
              memory: "2Gi"
            limits:
              cpu: "1"
              memory: "2Gi"
        - name: relayer-balance-sync
          command: [ "/opt/relayer/scripts/starter" ]
          args: [ "balance-sync" ]
          image: "test/local-relayer:0.0.2"
          imagePullPolicy: Never
          env:
            - name: RLY_BIN
              value: "/bin/rly"
            - name: RLY_CONFIG_HOME
              value: "/opt/relayer/.relayer"
            - name: RLY
              value: "/bin/rly --home /opt/relayer/.local"
            - name: SRC_CHAIN_ID
              value: "********"
            - name: DST_CHAIN_ID
              value: "********"
            - name: CHAIN_1_RPC_ADDRESS
              value: "http://host.docker.internal:18451"
            - name: CHAIN_1_ETH_CHAIN_ID
              value: "5151"
            - name: CHAIN_2_RPC_ADDRESS
              value: "http://host.docker.internal:28451"
            - name: CHAIN_2_ETH_CHAIN_ID
              value: "5152"
            - name: CHAIN_1_IBC_ADDRESS
              value: "******************************************"
            - name: CHAIN_2_IBC_ADDRESS
              value: "******************************************"
          resources:
            requests:
              cpu: "1"
              memory: "2Gi"
            limits:
              cpu: "1"
              memory: "2Gi"
        - name: relayer-token-transfer
          command: [ "/opt/relayer/scripts/starter" ]
          args: [ "token-transfer" ]
          image: "test/local-relayer:0.0.2"
          imagePullPolicy: Never
          env:
            - name: RLY_BIN
              value: "/bin/rly"
            - name: RLY_CONFIG_HOME
              value: "/opt/relayer/.relayer"
            - name: RLY
              value: "/bin/rly --home /opt/relayer/.local"
            - name: SRC_CHAIN_ID
              value: "********"
            - name: DST_CHAIN_ID
              value: "********"
            - name: CHAIN_1_RPC_ADDRESS
              value: "http://host.docker.internal:18451"
            - name: CHAIN_1_ETH_CHAIN_ID
              value: "5151"
            - name: CHAIN_2_RPC_ADDRESS
              value: "http://host.docker.internal:28451"
            - name: CHAIN_2_ETH_CHAIN_ID
              value: "5152"
            - name: CHAIN_1_IBC_ADDRESS
              value: "******************************************"
            - name: CHAIN_2_IBC_ADDRESS
              value: "******************************************"
          resources:
            requests:
              cpu: "1"
              memory: "2Gi"
            limits:
              cpu: "1"
              memory: "2Gi"
# localE2E 

## 概要
ローカル環境下で コントラクト <-> Relayer 間のテストを行うスクリプト群となります。

## 動作条件
1. BESU 上のコンテナに Main Contract 及び IBC Contract がデプロイされている
2. Main Contract 及び IBC Contract 配下の `hardhat.config.ts` に記載されている `localFin` 及び `localBiz`の参照先がローカル上の BESU コンテナとなっている 
3. Relayer コンテナで３つのデーモンが起動している

## スクリプト内容
シェル経由で Hardhat タスクを実行し Relayer 経由するコントラクトを実行します。

## 事前準備 
下記スクリプトを実行し、テストに必要なアカウント作成します。
本スクリプトで作成したアカウントは後続の全てのシナリオで利用します。
```
./tests/localE2E/init/TestSetup.sh <開始アカウントID> <生成するデータ件数>
```

## テスト実施
### Biz アカウント開設申し込み
1. 下記スクリプトを実行し、Biz アカウント開設申込（`syncAccount`） を実行します。
    ```
    ./tests/localE2E/syncAccount/syncAccount.sh <開始アカウントID> < syncAccount するデータ件数>
    ```
2. Relayer の伝播が完了した後、下記スクリプトを実行し Financial Zone アカウントとの紐付けを行います。
    ```
    ./tests/localE2E/syncAccount/afterSyncAccount.sh <開始アカウントID> < 紐付けするデータ件数>
    ```
3. 下記スクリプトで Financial Zone 及び Business Zone アカウントの状態を確認しアカウントが開設されていればOK。
    ```
    ./tests/localE2E/syncAccount/getAccountAll.sh localFin <開始アカウントID> <照会するデータ件数>
    ./tests/localE2E/syncAccount/getAccountAll.sh localBiz <開始アカウントID> <照会するデータ件数>
    ```

### チャージ

1. 下記スクリプトを実行し、Financial Zone から Business Zone へチャージ(`transfer`) を実行します。
    ```
    ./tests/localE2E/transfer/FinToBizTransfer.sh <開始アカウントID> <チャージ件数> <チャージする Amount>
    ```
2. Relayer の伝播が完了した後、下記スクリプトで Financial Zone 及び Business Zone アカウントのアマウントを確認し、増減していれOK。
    ```
    ./tests/localE2E/syncAccount/getAccountAll.sh localFin <開始アカウントID> <照会するデータ件数>
    ./tests/localE2E/syncAccount/getAccountAll.sh localBiz <開始アカウントID> <照会するデータ件数>
    ```

### ディスチャージ

1. 下記スクリプトを実行し、Business Zone から Financial Zone へチャージ(`transfer`) を実行します。
    ```
    ./tests/localE2E/transfer/BizToFinTransfer.sh <開始アカウントID> <ディスチャージ件数> <ディスチャージする Amount>
    ```
2. Relayer の伝播が完了した後、下記スクリプトで Business Zone 及び Financial Zone アカウントのアマウントを確認し、増減していれOK。
    ```
    ./tests/localE2E/syncAccount/getAccountAll.sh localFin <開始アカウントID> <照会するデータ件数>
    ./tests/localE2E/syncAccount/getAccountAll.sh localBiz <開始アカウントID> <照会するデータ件数>
    ```
### Biz 送金
1. 下記スクリプトを実行し、Business Zone 間送金(`syncTransfer`) を実行します。
    ```
    ./tests/localE2E/syncTransfer/syncTransfer.sh
    ```
2. Relayer の伝播が完了した後、下記スクリプトで Business Zone 及び Financial Zone アカウントのアマウントを確認し、増減していれOK。
    ```
    ./tests/localE2E/syncAccount/getAccountAll.sh localFin <開始アカウントID> <照会するデータ件数>
    ./tests/localE2E/syncAccount/getAccountAll.sh localBiz <開始アカウントID> <照会するデータ件数>
    ```









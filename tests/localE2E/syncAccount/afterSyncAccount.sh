#!/bin/bash

WORKSPACE_DIR=$(
  cd $(dirname "$0")/../../../.. || exit
  pwd
)
pushd "${WORKSPACE_DIR}" >/dev/null || exit

RELAYER_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-relayer
MAIN_CONTRACT_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-contract
IBC_CONTRACT_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-contract-ibc

source "${RELAYER_DIR}"/scripts/bin/utils.sh

if [ $# -lt 2 ]; then
  message "warn" "Must specify the starting ID and number of data to setActiveBusinessAccountWithZone." 1>&2
  echo -e "[Usge] bash $(basename ${BASH_SOURCE[0]}) <Start account ID> <setActiveBusinessAccountWithZone data count>" 1>&2
  echo -e "[EXAMPLE] 'bash $(basename ${BASH_SOURCE[0]}) 401 10' will setActiveBusinessAccountWithZone 10 accounts from account ID 401." 1>&2
  exit 1
fi

START_ACCOUNT_ID=$1
DATA_COUNT=$2

pushd "${MAIN_CONTRACT_DIR}" >/dev/null || exit

export NETWORK=localFin
source "${MAIN_CONTRACT_DIR}/bin/local/_load_env.sh" "${NETWORK}"

message "info" "Start setActiveBusinessAccountWithZone..."
for (( i=0; i<"${DATA_COUNT}"; i++ ));do
  account_id=$((START_ACCOUNT_ID+${i}))
  account_name=$(printf "account%d" "${account_id}")

  output=$(npx hardhat getAccountAll --network "${NETWORK}" \
    --account-id "${account_id}" \
    --valid-id "${VALID_ID}")

  # Check the output of getAccountAll
  if [[ $output != *"Not linked from Biz Zone."* ]]; then
    message "info" "[setActiveBusinessAccountWithZone] ${account_id} : ${account_name}"

    npx hardhat setActiveBusinessAccountWithZone --network "${NETWORK}" \
      --validator-id "${VALID_ID}" \
      --account-id "${account_id}" \
      --zone-id "${BIZ_ZONE_ID}" \

    if [ $? -eq 0 ]; then
      sleep 3
    else
      message "err" "[setActiveBusinessAccountWithZone] An error occurred in setActiveBusinessAccountWithZone. : ${account_id}: ${account_name}" 1>&2
      exit 1
    fi
fi
done

popd > /dev/null || exit

message "success" "Finish setActiveBusinessAccountWithZone..."
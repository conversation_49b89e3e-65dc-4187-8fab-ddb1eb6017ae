#!/bin/bash

WORKSPACE_DIR=$(
  cd $(dirname "$0")/../../../.. || exit
  pwd
)
pushd "${WORKSPACE_DIR}" >/dev/null || exit

RELAYER_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-relayer
MAIN_CONTRACT_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-contract
IBC_CONTRACT_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-contract-ibc

source "${RELAYER_DIR}"/scripts/bin/utils.sh

VALID_ID=8888

if [ $# -lt 3 ]; then
  message "warn" "Must specify the target zone, starting ID, and number of data to get." 1>&2
  echo -e "[Usge] bash $(basename ${BASH_SOURCE[0]}) <target zone> <start account ID> <get Account data count>" 1>&2
  echo -e "[EXAMPLE] 'bash $(basename ${BASH_SOURCE[0]}) localFin 400 10' will get 10 accounts from account ID 400." 1>&2
  exit 1
fi

TARGET_ZONE=$1
START_ACCOUNT_ID=$2
DATA_COUNT=$3

pushd "${MAIN_CONTRACT_DIR}" >/dev/null || exit

message "info" "Start ${TARGET_ZONE} getAccountAll..."
for (( i=0; i<"${DATA_COUNT}"; i++ ));do
  account_id=$((START_ACCOUNT_ID+${i}))
  npx hardhat getAccountAll --network "${TARGET_ZONE}" --valid-id "${VALID_ID}" --account-id "${account_id}"
  message "info" "[ACCOUNT]${account_id}"
done
message "success" "Finish ${TARGET_ZONE} getAccountAll..."
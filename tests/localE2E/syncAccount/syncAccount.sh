#!/bin/bash

WORKSPACE_DIR=$(
  cd $(dirname "$0")/../../../.. || exit
  pwd
)
pushd "${WORKSPACE_DIR}" >/dev/null || exit

RELAYER_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-relayer
MAIN_CONTRACT_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-contract
IBC_CONTRACT_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-contract-ibc

source "${RELAYER_DIR}"/scripts/bin/utils.sh

if [ $# -lt 2 ]; then
  message "warn" "Must specify the starting ID and number of data to syncAccount." 1>&2
  echo -e "[Usge] bash $(basename ${BASH_SOURCE[0]}) <Start account ID> <SyncAccount data count>" 1>&2
  echo -e "[EXAMPLE] 'bash $(basename ${BASH_SOURCE[0]}) 401 10' will syncAccount 10 accounts from account ID 401." 1>&2
  exit 1
fi

START_ACCOUNT_ID=$1
DATA_COUNT=$2

pushd "${IBC_CONTRACT_DIR}" >/dev/null || exit

source "${MAIN_CONTRACT_DIR}/bin/local/_load_env.sh" "localBiz"

message "info" "Start syncAccount..."
for (( i=0; i<"${DATA_COUNT}"; i++ ));do
  account_id=$((START_ACCOUNT_ID+${i}))
  account_name=$(printf "account%d" "${account_id}")
  message "info" "[SyncAccount] ${account_id} : ${account_name}"

  message "info" "[SyncAccount] npx hardhat syncAccount --network localBiz --validator-id ${VALID_ID} --account-id ${account_id} --account-name ${account_name} --from-zone-id ${FROM_ZONE_ID} --to-zone-id ${TO_ZONE_ID} --zone-name ${ZONE_NAME} --account-status 'applying' --approval-amount 0 --trace-id '1000' --timeout-height **********"
  npx hardhat syncAccount \
    --network localBiz \
    --validator-id "${VALID_ID}" \
    --account-id "${account_id}" \
    --account-name "${account_name}" \
    --from-zone-id "${ZONE_ID}" \
    --zone-name "${ZONE_NAME}" \
    --account-status "${STATUS_APPLYING}" \
    --approval-amount "${ZERO_AMOUNT}" \
    --trace-id "${TRACE_ID}" \
    --timeout-height ********** \
    --reason-code "${REASON_CODE}"

  if [ $? -eq 0 ]; then
    sleep 3
  else
    message "err" "[SyncAccount] An error occurred in syncAccount. : ${account_id}: ${account_name}" 1>&2
    exit 1
  fi
done
message "success" "Finish syncAccount..."

#!/bin/bash

WORKSPACE_DIR=$(
  cd $(dirname "$0")/../../../.. || exit
  pwd
)
pushd "${WORKSPACE_DIR}" >/dev/null || exit

RELAYER_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-relayer
MAIN_CONTRACT_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-contract
IBC_CONTRACT_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-contract-ibc

source "${RELAYER_DIR}"/scripts/bin/utils.sh

KEY_ACCOUNT=47e179ec197488593b187f80a00eb0da91f1b9d0b13f8733639f19c30a34926a
VALID_ID=8888
ISSUER_ID=2221
KEY_ISSUER=7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6
FLAG="11"

FIN_ZONE_ID=3000
BIZ_ZONE_ID=3001

if [ $# -lt 3 ]; then
  message "warn" "Must specify the start ID, number of data, and amount." 1>&2
  echo -e "[Usge] bash $(basename ${BASH_SOURCE[0]}) <start account ID> <get Account data count> <amount>" 1>&2
  echo -e "[EXAMPLE] 'bash $(basename ${BASH_SOURCE[0]}) 401 10 100' will transfer 100 amounts of 10 accounts from account ID 401." 1>&2
  exit 1
fi

START_ACCOUNT_ID=$1
DATA_COUNT=$2
AMOUNT=$3

pushd "${IBC_CONTRACT_DIR}" >/dev/null || exit

source "${MAIN_CONTRACT_DIR}/bin/local/_load_env.sh" "localFin"

message "info" "Start Fin to Biz transfer..."
for (( i=0; i<"${DATA_COUNT}"; i++ ));do
  account_id=$((START_ACCOUNT_ID+${i}))
  message "info" "[transfer] ${account_id} : ${AMOUNT} amount"
  message "info" "[transfer] npx hardhat transfer --network localFin --account-id ${account_id} --from-zone-id ${FIN_ZONE_ID} --to-zone-id ${BIZ_ZONE_ID} --amount ${AMOUNT} --timeout-height **********"
  npx hardhat transfer \
    --network localFin \
    --account-id "${account_id}" \
    --from-zone-id "${FIN_ZONE_ID}" \
    --to-zone-id "${BIZ_ZONE_ID}" \
    --amount "${AMOUNT}" \
    --timeout-height **********
  if [ $? -eq 0 ]; then
    sleep 3
  else
    message "err" "[transfer] An error occurred in transfer. : ${account_id}" 1>&2
    exit 1
  fi
done
message "success" "Finish Fin to Biz transfer..."

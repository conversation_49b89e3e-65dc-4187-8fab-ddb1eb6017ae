#!/bin/bash
WORKSPACE_DIR=$(
  cd $(dirname "$0")/../../../.. || exit
  pwd
)
pushd "${WORKSPACE_DIR}" >/dev/null || exit

RELAYER_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-relayer
MAIN_CONTRACT_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-contract
IBC_CONTRACT_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-contract-ibc

source "${RELAYER_DIR}"/scripts/bin/utils.sh


if [ $# -lt 2 ]; then
  message "warn" "Must specify the starting ID and number of data to generate." 1>&2
  echo -e "[Usge] bash $(basename ${BASH_SOURCE[0]}) <Start account ID> <Generate Account data count>" 1>&2
  echo -e "[EXAMPLE] 'bash $(basename ${BASH_SOURCE[0]}) 401 10' will generate 10 accounts from account ID 401." 1>&2
  exit 1
fi

START_ACCOUNT_ID=$1
DATA_COUNT=$2

pushd "${MAIN_CONTRACT_DIR}" >/dev/null || exit

export NETWORK=localFin
export FLAG="11"

source "${MAIN_CONTRACT_DIR}/bin/local/_load_env.sh" ${NETWORK}

pushd "${IBC_CONTRACT_DIR}" >/dev/null || exit

message "info" "Setup EscrowAcc Data..."

npx hardhat registerEscrowAcc --network localFin \
  --key-admin "${KEY_ADMIN}" \
  --src-zone-id "${ZONE_ID}" \
  --dst-zone-id "${BIZ_ZONE_ID}" \
  --escrow-account 301

popd > /dev/null || exit

message "success" "Finish Setup EscrowAcc Data..."

message "info" "Start Setup Account Data..."
for (( i=0; i<"${DATA_COUNT}"; i++ ));do
  account_id=$((START_ACCOUNT_ID+${i}))
  account_name=$(printf "account%d" "${account_id}")
  message "info" "[ACCOUNT]${account_id}: ${account_name}"
  message "info" "[registerAcc] npx hardhat registerAcc --network ${NETWORK} --issuer-id ${ISSUER_ID} --issuer-key ${KEY_ISSUER} --valid-id ${VALID_ID} --account-id ${account_id} --account-name ${account_name} --account-key ${KEY_ACCOUNT} --flag ${FLAG}"
  npx hardhat registerAcc \
    --network "${NETWORK}" \
    --issuer-id "${ISSUER_ID}" \
    --issuer-key "${KEY_ISSUER}" \
    --valid-id "${VALID_ID}" \
    --account-id "${account_id}" \
    --account-name "${account_name}" \
    --account-key "${KEY_ACCOUNT}" \
    --flag "${FLAG}"

  if [ $? -eq 0 ]; then
      sleep 2

      message "info" "[mintToken] npx hardhat mintToken --network localFin --issuer-id ${ISSUER_ID} --issuer-key ${KEY_ISSUER} --account-id ${account_id} --amount ${MINT_AMOUNT}"
      npx hardhat mintToken \
        --network "${NETWORK}" \
        --issuer-id "${ISSUER_ID}" \
        --issuer-key "${KEY_ISSUER}" \
        --account-id "${account_id}" \
        --amount "${MINT_AMOUNT}"

      sleep 2
  else
    message "err" "[registerAcc] An error occurred in registerAcc. : ${account_id}: ${account_name}" 1>&2
    exit 1
  fi
done

popd > /dev/null || exit

message "success" "Finish Setup Account Data..."
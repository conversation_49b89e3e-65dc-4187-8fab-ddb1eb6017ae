#!/bin/bash

WORKSPACE_DIR=$(
  cd $(dirname "$0")/../../../.. || exit
  pwd
)
pushd "${WORKSPACE_DIR}" >/dev/null || exit

RELAYER_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-relayer
MAIN_CONTRACT_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-contract
IBC_CONTRACT_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-contract-ibc

source "${RELAYER_DIR}"/scripts/bin/utils.sh

if [ $# -lt 3 ]; then
  message "warn" "Must specify the start ID, number of data, and amount." 1>&2
  echo -e "[Usge] bash $(basename ${BASH_SOURCE[0]}) <start account ID> <get Account data count> <amount>" 1>&2
  echo -e "[EXAMPLE] 'bash $(basename ${BASH_SOURCE[0]}) 400 10 100' will syncTransfer 100 amounts of 10 accounts from account ID 400." 1>&2
  exit 1
fi

START_ACCOUNT_ID=$1
DATA_COUNT=$2
AMOUNT=$3

pushd "${IBC_CONTRACT_DIR}" >/dev/null || exit

source "${MAIN_CONTRACT_DIR}/bin/local/_load_env.sh" "localBiz"

message "info" "Start SyncTransfer..."
for (( i=0; i<"${DATA_COUNT}"; i+=2 )); do
  from_account_id=$((START_ACCOUNT_ID+${i}))
  to_account_id=$((START_ACCOUNT_ID+$((i+1))))
  message "info" "[SyncTransfer] ${from_account_id} to ${to_account_id}: ${AMOUNT} amount"
  npx hardhat syncTransfer \
    --network localBiz \
    --amount "${AMOUNT}" \
    --from-account-id "${from_account_id}" \
    --from-zone-id "${ZONE_ID}" \
    --memo "${MEMO}" \
    --misc-value1 "misc-value1" \
    --misc-value2 "misc-value2" \
    --timeout-height ********** \
    --to-account-id "${to_account_id}" \
    --trace-id "${TRACE_ID}"

  if [ $? -eq 0 ]; then
    sleep 3
  else
    message "err" "[SyncTransfer] An error occurred in syncTransfer. : ${from_account_id} to ${to_account_id}: ${AMOUNT} amount" 1>&2
    exit 1
  fi
done
message "success" "Finish SyncTransfer..."

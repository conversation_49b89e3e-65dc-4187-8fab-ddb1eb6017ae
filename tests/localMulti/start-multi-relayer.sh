#!/bin/bash
REPO_ROOT=$(
  cd $(dirname "$0")/../.. || exit
  pwd
)
pushd "${REPO_ROOT}" >/dev/null || exit
source ./scripts/bin/utils.sh

DOCKER_FILE=${REPO_ROOT}/docker-compose-multi.yml
message "info" "Start Multi Relayer..."
docker compose -f "${DOCKER_FILE}" up -d --build --force-recreate
#docker compose -f "${DOCKER_FILE}" up -d --build --force-recreate relayer-account-sync1 relayer-account-sync2
#docker compose -f "${DOCKER_FILE}" up -d --build --force-recreate relayer-balance-sync1 relayer-balance-sync2
#docker compose -f "${DOCKER_FILE}" up -d --build --force-recreate relayer-token-transfer1 relayer-token-transfer2
if [ $? -eq 0 ]; then
  message "success" "Success Multi Relayer Start..."
else
  message "err" "Failed Multi Relayer Start..."
  exit 1
fi

popd


#!/bin/bash
REPO_ROOT=$(
  cd $(dirname "$0")/../.. || exit
  pwd
)
pushd "${REPO_ROOT}" >/dev/null || exit
source ./scripts/bin/utils.sh

DOCKER_FILE=${REPO_ROOT}/docker-compose-multi.yml
message "info" "Stop Relayer Multi Container..."
docker compose -f "${DOCKER_FILE}" --profile "ibc" down
if [ $? -eq 0 ]; then
  message "success" "Success Stopped Multi Relayer Build..."
else
  message "err" "Failed Multi Relayer Build..."
  exit 1
fi

popd
#!/bin/bash
WORKSPACE_DIR=$(
  cd $(dirname "$0")/../../../.. || exit
  pwd
)
pushd "${WORKSPACE_DIR}" >/dev/null || exit

RELAYER_DIR="${WORKSPACE_DIR}"/dcbg-dcjpy-relayer

pushd "${RELAYER_DIR}" >/dev/null || exit

source "${RELAYER_DIR}"/scripts/bin/utils.sh

SCALE_COUNT=2
LOCAL_MULTI_CONFIG_HOME=./tests/localMulti/.local
LOCAL_MULTI_ENV_HOME=./tests/localMulti/env

# Initialize Config Dir
if [ -d "${LOCAL_MULTI_CONFIG_HOME}" ]; then
  rm -rf "${LOCAL_MULTI_CONFIG_HOME}"
  mkdir -p "${LOCAL_MULTI_CONFIG_HOME}"
fi

# Copy config.json
relayer_paths=("account-sync" "balance-sync" "token-transfer")
for path in "${relayer_paths[@]}"; do
  if [ -f ".local/${path}/config/config.json" ]; then
    for ((i=0; i<SCALE_COUNT; i++))
    do
      if [ ! -d "${LOCAL_MULTI_CONFIG_HOME}/${path}$((i+1))/config/" ]; then
        mkdir -p "${LOCAL_MULTI_CONFIG_HOME}/${path}$((i+1))/config/"
      fi
      cp -f ".local/${path}/config/config.json" "${LOCAL_MULTI_CONFIG_HOME}/${path}$((i+1))/config/"
    done
  else
    message "err" "config file does not exist. : .local/${path}/config/config.json" 1>&2
    exit 1
  fi
done

# Initialize Env Dir
if [ -d "${LOCAL_MULTI_ENV_HOME}" ]; then
  rm -rf "${LOCAL_MULTI_ENV_HOME}"
  mkdir -p "${LOCAL_MULTI_ENV_HOME}"
fi

# Copy .env
if [ -f "./docker/dcjpy/env/common.env" ]; then
  cp -f "./docker/dcjpy/env/common.env" "${LOCAL_MULTI_ENV_HOME}/common.env"
else
  message "err" "common.env file does not exist. : ./docker/dcjpy/env/common.env" 1>&2
  exit 1
fi

if [ -f "./docker/dcjpy/env/ibc-address.env" ]; then
  cp -f "./docker/dcjpy/env/ibc-address.env" "${LOCAL_MULTI_ENV_HOME}/ibc-address.env"
else
  message "err" "ibc-address.env file does not exist. : ./docker/dcjpy/env/ibc-address.env" 1>&2
  exit 1
fi

for path in "${relayer_paths[@]}"; do
  if [ -f "docker/dcjpy/env/${path}.env" ]; then
    for ((i=0; i<SCALE_COUNT; i++))
    do
      cp -f "docker/dcjpy/env/${path}.env" "${LOCAL_MULTI_ENV_HOME}/${path}$((i+1)).env"
    done
  else
    message "err" "env file does not exist. : docker/dcjpy/env/${path}.env" 1>&2
    exit 1
  fi
done

popd >/dev/null || exit

message "info" "Success copied config & env file."
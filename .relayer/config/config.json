{"global": {"timeout": "10s", "light-cache-size": 20, "logger": {"level": "INFO", "format": "text", "output": "stdout"}}, "chains": [{"chain": {"@type": "/relayer.chains.ethereum.config.ChainConfig", "chain_id": "${CHAIN_1_CHAIN_ID}", "eth_chain_id": "${CHAIN_1_ETH_CHAIN_ID}", "rpc_addr": "${CHAIN_1_RPC_ADDRESS}", "signer": {"@type": "/relayer.chains.ethereum.signers.hd.SignerConfig", "mnemonic": "math razor capable expose worth grape metal sunset metal sudden usage scheme", "path": "m/44'/60'/0'/0/0"}, "ibc_address": "${CHAIN_1_IBC_ADDRESS}", "initial_send_checkpoint": 1, "initial_recv_checkpoint": 1, "enable_debug_trace": false, "average_block_time_msec": 2000, "max_retry_for_inclusion": 3, "gas_estimate_rate": {"numerator": 1, "denominator": 1}, "max_gas_limit": *********, "tx_type": "auto"}, "prover": {"@type": "/relayer.provers.ibft2.config.ProverConfig", "trust_level_numerator": 1, "trust_level_denominator": 3, "trusting_period": 1209600}}, {"chain": {"@type": "/relayer.chains.ethereum.config.ChainConfig", "chain_id": "${CHAIN_2_CHAIN_ID}", "eth_chain_id": "${CHAIN_2_ETH_CHAIN_ID}", "rpc_addr": "${CHAIN_2_RPC_ADDRESS}", "signer": {"@type": "/relayer.chains.ethereum.signers.hd.SignerConfig", "mnemonic": "math razor capable expose worth grape metal sunset metal sudden usage scheme", "path": "m/44'/60'/0'/0/0"}, "ibc_address": "${CHAIN_2_IBC_ADDRESS}", "initial_send_checkpoint": 1, "initial_recv_checkpoint": 1, "enable_debug_trace": false, "average_block_time_msec": 2000, "max_retry_for_inclusion": 3, "max_gas_limit": *********, "gas_estimate_rate": {"numerator": 1, "denominator": 1}, "tx_type": "auto"}, "prover": {"@type": "/relayer.provers.ibft2.config.ProverConfig", "trust_level_numerator": 1, "trust_level_denominator": 3, "trusting_period": 1209600}}], "paths": {"account-sync": {"src": {"chain-id": "${CHAIN_1_CHAIN_ID}", "client-id": "${CHAIN_1_AS_CLIENT_ID}", "connection-id": "${CHAIN_1_AS_CONNECTION_ID}", "channel-id": "${CHAIN_1_AS_CHANNEL_ID}", "port-id": "account-sync", "order": "unordered", "version": "account-sync-1"}, "dst": {"chain-id": "${CHAIN_2_CHAIN_ID}", "client-id": "${CHAIN_2_AS_CLIENT_ID}", "connection-id": "${CHAIN_2_AS_CONNECTION_ID}", "channel-id": "${CHAIN_2_AS_CHANNEL_ID}", "port-id": "account-sync", "order": "unordered", "version": "account-sync-1"}, "strategy": {"type": "naive", "src-noack": false, "dst-noack": false}}, "balance-sync": {"src": {"chain-id": "${CHAIN_1_CHAIN_ID}", "client-id": "${CHAIN_1_BS_CLIENT_ID}", "connection-id": "${CHAIN_1_BS_CONNECTION_ID}", "channel-id": "${CHAIN_1_BS_CHANNEL_ID}", "port-id": "balance-sync", "order": "unordered", "version": "balance-sync-1"}, "dst": {"chain-id": "${CHAIN_2_CHAIN_ID}", "client-id": "${CHAIN_2_BS_CLIENT_ID}", "connection-id": "${CHAIN_2_BS_CONNECTION_ID}", "channel-id": "${CHAIN_2_BS_CHANNEL_ID}", "port-id": "balance-sync", "order": "unordered", "version": "balance-sync-1"}, "strategy": {"type": "naive", "src-noack": false, "dst-noack": false}}, "token-transfer": {"src": {"chain-id": "${CHAIN_1_CHAIN_ID}", "client-id": "${CHAIN_1_TT_CLIENT_ID}", "connection-id": "${CHAIN_1_TT_CONNECTION_ID}", "channel-id": "${CHAIN_1_TT_CHANNEL_ID}", "port-id": "token-transfer", "order": "unordered", "version": "token-transfer-1"}, "dst": {"chain-id": "${CHAIN_2_CHAIN_ID}", "client-id": "${CHAIN_2_TT_CLIENT_ID}", "connection-id": "${CHAIN_2_TT_CONNECTION_ID}", "channel-id": "${CHAIN_2_TT_CHANNEL_ID}", "port-id": "token-transfer", "order": "unordered", "version": "token-transfer-1"}, "strategy": {"type": "naive", "src-noack": false, "dst-noack": false}}}}
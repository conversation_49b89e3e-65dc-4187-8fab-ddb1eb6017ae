VERSION := $(shell echo $(shell git describe --tags) | sed 's/^v//')
COMMIT  := $(shell git log -1 --format='%H')
DIRTY := $(shell git status --porcelain | wc -l | xargs)
DOCKER := $(shell which docker)
GOPATH := $(shell go env GOPATH)
GOBIN := $(GOPATH)/bin
RELAYER_VERSION := 1.0.0
GOLANG_VER := 1.21.7
ALPINE_VER := 3.19
BUSYBOX_VER := 1.34.1
INFRA_TOOLKIT_VER := v0.0.6
APP_GID := 1000
APP_UID := 100
APP_GROUP := relayer
APP_USER := relayer
PROTO_VER=0.13.1
PROTO_IMAGE_NAME=ghcr.io/cosmos/proto-builder:$(PROTO_VER)
PROTO_IMAGE=docker run --user 0 --rm -v $(CURDIR):/workspace --workdir /workspace $(PROTO_IMAGE_NAME)
RLY_HOME=./.relayer

all: lint install

###############################################################################
# Build / Install
###############################################################################

ldflags = -X github.com/decurret-lab/dcbg-dcjpy-relayer/cmd.Version=$(VERSION) \
					-X github.com/decurret-lab/dcbg-dcjpy-relayer/cmd.Commit=$(COMMIT) \
					-X github.com/decurret-lab/dcbg-dcjpy-relayer/cmd.Dirty=$(DIRTY)

ldflags += $(LDFLAGS)
ldflags := $(strip $(ldflags))

BUILD_FLAGS := -ldflags '$(ldflags)'

.PHONY: build
build: go.sum
ifeq ($(OS),Windows_NT)
	@echo "building rly binary..."
	@echo "BUILD_FLAGS: ${BUILD_FLAGS}"
	@go build -mod=readonly $(BUILD_FLAGS) -o build/rly.exe main.go
else
	@echo "Building rly binary..."
	@echo "BUILD_FLAGS: ${BUILD_FLAGS}"
	@go build $(BUILD_FLAGS) -o build/rly main.go
endif

.PHONY: debug-build
debug-build: go.sum
ifeq ($(OS),Windows_NT)
	@go mod tidy -v \
    	&& go install github.com/cosmtrek/air@v1.49.0 \
    	&& go install github.com/go-delve/delve/cmd/dlv@v1.22.0 \
    	&& go install github.com/cweill/gotests/gotests@v1.6.0 \
    	&& go install go.uber.org/mock/mockgen@v0.4.0 \
    	&& go install github.com/google/wire/cmd/wire@v0.6.0
	@echo "building rly binary..."
	@echo "BUILD_FLAGS: ${BUILD_FLAGS}"
	@go build -mod=readonly $(BUILD_FLAGS) -gcflags="all=-N -l" -o build/rly.exe main.go
else
	@echo "Delete ethereum Cache..."
	@rm -rf ${RLY_CONFIG_HOME}/ethereum
	@echo "Delete rly binary..."
	@rm -rf build/rly
	@go mod tidy -v \
    	&& go install github.com/cosmtrek/air@v1.49.0 \
    	&& go install github.com/go-delve/delve/cmd/dlv@v1.22.0 \
    	&& go install github.com/cweill/gotests/gotests@v1.6.0 \
    	&& go install go.uber.org/mock/mockgen@v0.4.0 \
    	&& go install github.com/google/wire/cmd/wire@v0.6.0
	@echo "Building rly binary..."
	@echo "BUILD_FLAGS: ${BUILD_FLAGS}"
	@go build $(BUILD_FLAGS) -work -gcflags "all=-N -l" -o build/rly main.go
endif

.PHONY: build-zip
build-zip: go.sum
	@echo "Delete release set..."
	@rm -rf release.tar.gz
	@rm -rf build/*
	@echo "building rly binaries for windows, mac and linux"
	@echo "BUILD_FLAGS: ${BUILD_FLAGS}"
	@GOOS=linux GOARCH=amd64 go build -mod=readonly $(BUILD_FLAGS) -o build/linux-amd64-rly main.go
	@GOOS=darwin GOARCH=amd64 go build -mod=readonly $(BUILD_FLAGS) -o build/darwin-amd64-rly main.go
	@GOOS=windows GOARCH=amd64 go build -mod=readonly $(BUILD_FLAGS) -o build/windows-amd64-rly.exe main.go
	@tar -czvf release.tar.gz ./build

.PHONY: install
install: go.sum
	@echo "Delete rly binary..."
	@rm -rf build/rly
	@echo "Installing rly binary..."
	@go build -mod=readonly $(BUILD_FLAGS) -o $(GOBIN)/rly main.go

###############################################################################
# Setup
###############################################################################
# 依存モジュールダウンロード
.PHONY: download
download:
	go mod download

.PHONY: tools
tools:
	go install github.com/cosmtrek/air@latest \
	&& go install github.com/go-delve/delve/cmd/dlv@latest \
	&& go install github.com/cweill/gotests/gotests@latest \
	&& go install go.uber.org/mock/mockgen@latest \
	&& go install github.com/sanposhiho/gomockhandler@latest \
	&& go install github.com/google/wire/cmd/wire@latest \
	&& go get github.com/google/go-cmp/cmp

.PHONY: run
run:
	docker-compose down && docker-compose up -d

.PHONY: test-adapter
test-adapter:
	ENV=test go test ./pkg/adapter -v

.PHONY: test-usecase
test-usecase:
	ENV=test go test ./pkg/usecase -v

.PHONY: test-infra
test-infra:
	ENV=test go test ./pkg/infra/... -v

.PHONY: mockgen
mockgen:
	go generate ./...


###############################################################################
# Tests / CI
###############################################################################

.PHONY: test
test:
	@go test -mod=readonly -race ./...

.PHONY: coverage
coverage:
	@echo "viewing test coverage..."
	@go tool cover --html=coverage.out

.PHONY: lint
lint:
	@golangci-lint run
	@find . -name '*.go' -type f -not -path "*.git*" | xargs gofmt -d -s
	@go mod verify

######## Protobuf ########
.PHONY: proto-go
proto-go:
	@echo "Delete Generated Protobuf files..."
	@rm -rf ./github.com
	@echo "Generating Protobuf files"
	@$(PROTO_IMAGE) sh ./scripts/protocgen.sh

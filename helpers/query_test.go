//go:generate gotests -w -all $GOFILE
package helpers

import (
	"fmt"
	"testing"

	sdk "github.com/cosmos/cosmos-sdk/types"
	types "github.com/cosmos/ibc-go/v7/modules/apps/transfer/types"
	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
	"go.uber.org/mock/gomock"
)

func TestQuery_QueryBalance(t *testing.T) {
	t.Run("should return coins with denoms", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// create mock instances
		chainMock := core.NewMockChain(ctrl)
		proverMock := core.NewMockProver(ctrl)
		chainMock.EXPECT().QueryBalance(gomock.Any(), gomock.Any()).Return(
			sdk.Coins{
				sdk.NewInt64Coin("Yen", 1000),
				sdk.NewInt64Coin("Yen", 2000),
				sdk.NewInt64Coin("Yen", 3000),
			}, nil).AnyTimes()

		provableChain := core.NewProvableChain(chainMock, proverMock)

		coins, err := QueryBalance(provableChain, clienttypes.ZeroHeight(), sdk.AccAddress{}, true)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		expected := 3
		if len(coins) != expected {
			t.Errorf("got = %v, expected = %v", len(coins), expected)
		}
	})

	t.Run("should return coins when denom traces is empty", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// create mock instances
		chainMock := core.NewMockChain(ctrl)
		proverMock := core.NewMockProver(ctrl)
		chainMock.EXPECT().QueryBalance(gomock.Any(), gomock.Any()).Return(
			sdk.Coins{
				sdk.NewInt64Coin("Yen", 1000),
				sdk.NewInt64Coin("Yen", 2000),
				sdk.NewInt64Coin("Yen", 3000),
			}, nil).AnyTimes()
		chainMock.EXPECT().QueryDenomTraces(gomock.Any(), gomock.Any(), gomock.Any()).Return(
			&types.QueryDenomTracesResponse{
				DenomTraces: types.Traces{},
			}, nil).AnyTimes()

		provableChain := core.NewProvableChain(chainMock, proverMock)

		coins, err := QueryBalance(provableChain, clienttypes.ZeroHeight(), sdk.AccAddress{}, false)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		expected := 3
		if len(coins) != expected {
			t.Errorf("got = %v, expected = %v", len(coins), expected)
		}
	})

	t.Run("should return coins with a full denom path when a IBC denom is found", func(t *testing.T) {
		// types.ParseDenomTrace()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// create mock instances
		chainMock := core.NewMockChain(ctrl)
		proverMock := core.NewMockProver(ctrl)

		ibcDenom := "ibc/8862ea785a368b9a25ecd71ca54d3772cbc21e06180563133e0096929a99db64"
		chainMock.EXPECT().QueryBalance(gomock.Any(), gomock.Any()).Return(
			sdk.Coins{
				sdk.NewInt64Coin("Yen", 0), // will not be in the response
				sdk.NewInt64Coin("Yen", 1000),
				sdk.NewInt64Coin(ibcDenom, 2000),
			}, nil).AnyTimes()
		chainMock.EXPECT().QueryDenomTraces(gomock.Any(), gomock.Any(), gomock.Any()).Return(
			&types.QueryDenomTracesResponse{
				DenomTraces: types.Traces{
					types.ParseDenomTrace(ibcDenom),
					types.ParseDenomTrace("Yen"),
				},
			}, nil).AnyTimes()

		provableChain := core.NewProvableChain(chainMock, proverMock)

		coins, err := QueryBalance(provableChain, clienttypes.ZeroHeight(), sdk.AccAddress{}, false)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		expected := 2
		if len(coins) != expected {
			t.Errorf("got = %v, expected = %v", len(coins), expected)
		}
		for _, v := range coins {
			if v.Amount.Int64() == 1000 && v.Denom != "Yen" {
				t.Errorf("got = %v, expected = %v", v.Denom, "Yen")
			}
			if v.Amount.Int64() == 2000 && v.Denom != ibcDenom {
				t.Errorf("got = %v, expected = %v", v.Denom, ibcDenom)
			}
		}
	})
	t.Run("should return coins with a denom as it is when no IBC denom is found", func(t *testing.T) {
		// types.ParseDenomTrace()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// create mock instances
		chainMock := core.NewMockChain(ctrl)
		proverMock := core.NewMockProver(ctrl)

		chainMock.EXPECT().QueryBalance(gomock.Any(), gomock.Any()).Return(
			sdk.Coins{
				sdk.NewInt64Coin("Yen", 0), // will not be in the response
				sdk.NewInt64Coin("Yen", 1000),
				sdk.NewInt64Coin("Gyen", 2000),
			}, nil).AnyTimes()
		chainMock.EXPECT().QueryDenomTraces(gomock.Any(), gomock.Any(), gomock.Any()).Return(
			&types.QueryDenomTracesResponse{
				DenomTraces: types.Traces{
					types.ParseDenomTrace("Yen"),
				},
			}, nil).AnyTimes()

		provableChain := core.NewProvableChain(chainMock, proverMock)

		coins, err := QueryBalance(provableChain, clienttypes.ZeroHeight(), sdk.AccAddress{}, false)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		expected := 2
		if len(coins) != expected {
			t.Errorf("got = %v, expected = %v", len(coins), expected)
		}
		for _, v := range coins {
			if v.Amount.Int64() == 1000 && v.Denom != "Yen" {
				t.Errorf("got = %v, expected = %v", v.Denom, "Yen")
			}
			if v.Amount.Int64() == 2000 && v.Denom != "Gyen" {
				t.Errorf("got = %v, expected = %v", v.Denom, "GYen")
			}
		}
	})

	t.Run("should return an error when query balance fails", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// create mock instances
		chainMock := core.NewMockChain(ctrl)
		proverMock := core.NewMockProver(ctrl)
		chainMock.EXPECT().QueryBalance(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("test error")).AnyTimes()

		provableChain := core.NewProvableChain(chainMock, proverMock)

		_, err := QueryBalance(provableChain, clienttypes.ZeroHeight(), sdk.AccAddress{}, true)
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error when query denom traces fails", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// create mock instances
		chainMock := core.NewMockChain(ctrl)
		proverMock := core.NewMockProver(ctrl)
		chainMock.EXPECT().QueryBalance(gomock.Any(), gomock.Any()).Return(sdk.Coins{}, nil).AnyTimes()
		chainMock.EXPECT().QueryDenomTraces(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("test error")).AnyTimes()

		provableChain := core.NewProvableChain(chainMock, proverMock)

		_, err := QueryBalance(provableChain, clienttypes.ZeroHeight(), sdk.AccAddress{}, false)
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

//go:generate mockgen -source=$GOFILE -package=metrics -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package metrics

import (
	"context"
	"reflect"
	"testing"

	"go.opentelemetry.io/otel/exporters/prometheus"
	"go.opentelemetry.io/otel/sdk/metric"
)

func TestExporterNull_exporterType(t *testing.T) {
	tests := []struct {
		name string
		e    ExporterNull
		want string
	}{
		{
			name: "should return Null",
			e:    ExporterNull{},
			want: "null",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := ExporterNull{}
			if got := e.exporterType(); got != tt.want {
				t.Errorf("ExporterNull.exporterType() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestExporterProm_exporterType(t *testing.T) {
	type fields struct {
		Addr string
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "should return Prometheus",
			fields: fields{
				Addr: "str",
			},
			want: "prometheus",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := ExporterProm{
				Addr: tt.fields.Addr,
			}
			if got := e.exporterType(); got != tt.want {
				t.Errorf("ExporterProm.exporterType() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInitializeMetrics(t *testing.T) {

	type args struct {
		exporterConf ExporterConfig
	}

	tests := []struct {
		name        string
		prepareMock func()
		args        args
		wantErr     bool
	}{
		{
			name: "should initialize metrics ExporterNull.",
			args: args{
				exporterConf: ExporterNull{},
			},
			wantErr: false,
		},
		{
			name: "should initialize metrics ExporterProm.",
			args: args{
				exporterConf: ExporterProm{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := InitializeMetrics(tt.args.exporterConf); (err != nil) != tt.wantErr {
				t.Errorf("InitializeMetrics() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestShutdownMetrics(t *testing.T) {
	t.Skip("Not possible due to the use of Context.")

	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "should shutdown metrics",
			args: args{
				ctx: context.TODO(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := ShutdownMetrics(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("ShutdownMetrics() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestNewPrometheusExporter(t *testing.T) {
	t.Skip("Test cannot be performed due to pointer address difference.")

	type args struct {
		addr string
	}
	tests := []struct {
		name    string
		args    args
		want    *prometheus.Exporter
		wantErr bool
	}{
		{
			name: "should return relayer.processed_block_height instrument",
			args: args{
				addr: "relayer.processed_block_height",
			},
			want: &prometheus.Exporter{
				Reader: metric.NewManualReader(),
			},
			wantErr: false,
		},
		{
			name: "should return relayer.backlog_size instrument",
			args: args{
				addr: "relayer.backlog_size",
			},
			want: &prometheus.Exporter{
				Reader: metric.NewManualReader(),
			},
			wantErr: false,
		},
		{
			name: "should return relayer.backlog_oldest_timestamp instrument",
			args: args{
				addr: "relayer.backlog_oldest_timestamp",
			},
			want: &prometheus.Exporter{
				Reader: metric.NewManualReader(),
			},
			wantErr: false,
		},
		{
			name: "should return relayer.receive_packets_finalized instrument",
			args: args{
				addr: "relayer.receive_packets_finalized",
			},
			want: &prometheus.Exporter{
				Reader: metric.NewManualReader(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewPrometheusExporter(tt.args.addr)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewPrometheusExporter() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewPrometheusExporter() = %v, want %v", got, tt.want)
			}
		})
	}
}

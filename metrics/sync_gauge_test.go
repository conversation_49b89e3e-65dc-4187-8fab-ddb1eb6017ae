//go:generate mockgen -source=$GOFILE -package=metrics -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package metrics

import (
	"reflect"
	"sync"
	"testing"

	"go.opentelemetry.io/otel/attribute"
	api "go.opentelemetry.io/otel/metric"
	"go.opentelemetry.io/otel/sdk/metric"
)

func TestNewInt64SyncGauge(t *testing.T) {
	t.Skip("Test cannot be performed due to pointer address difference.")

	stubMeterProvider := metric.NewMeterProvider()

	type args struct {
		meter   api.Meter
		name    string
		options []api.Int64ObservableGaugeOption
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		want        *Int64SyncGauge
		wantErr     bool
	}{
		{
			name: "NewInt64SyncGauge relayer.processed_block_height",
			args: args{
				meter: stubMeterProvider.Meter("relayer.processed_block_height"),
				name:  "relayer.processed_block_height",
				options: []api.Int64ObservableGaugeOption{
					api.WithUnit("1"),
					api.WithDescription("latest finalized height"),
				},
			},
			want: &Int64SyncGauge{
				gauge:         nil,
				mutex:         &sync.RWMutex{},
				attrsValueMap: nil,
			},
			wantErr: false,
		},
		{
			name: "NewInt64SyncGauge relayer.backlog_size",
			args: args{
				meter: stubMeterProvider.Meter("relayer.backlog_size"),
				name:  "relayer.backlog_size",
				options: []api.Int64ObservableGaugeOption{
					api.WithUnit("1"),
					api.WithDescription("number of packets that are unreceived or received but unfinalized"),
				},
			},
			want: &Int64SyncGauge{
				gauge:         nil,
				mutex:         &sync.RWMutex{},
				attrsValueMap: nil,
			},
			wantErr: false,
		},
		{
			name: "NewInt64SyncGauge relayer.backlog_oldest_timestamp",
			args: args{
				meter: stubMeterProvider.Meter("relayer.backlog_oldest_timestamp"),
				name:  "relayer.backlog_oldest_timestamp",
				options: []api.Int64ObservableGaugeOption{
					api.WithUnit("1"),
					api.WithDescription("timestamp when the oldest packet in backlog was sent"),
				},
			},
			want: &Int64SyncGauge{
				gauge:         nil,
				mutex:         &sync.RWMutex{},
				attrsValueMap: nil,
			},
			wantErr: false,
		},
		{
			name: "NewInt64SyncGauge relayer.receive_packets_finalized",
			args: args{
				meter: stubMeterProvider.Meter("relayer.receive_packets_finalized"),
				name:  "relayer.receive_packets_finalized",
				options: []api.Int64ObservableGaugeOption{
					api.WithUnit("1"),
					api.WithDescription("number of packets that are received and finalized"),
				},
			},
			want: &Int64SyncGauge{
				gauge:         nil,
				mutex:         &sync.RWMutex{},
				attrsValueMap: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			got, err := NewInt64SyncGauge(tt.args.meter, tt.args.name, tt.args.options...)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewInt64SyncGauge() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewInt64SyncGauge() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInt64SyncGauge_Set(t *testing.T) {

	stubMeterName := "relayer.processed_block_height"
	stubMeterProvider := metric.NewMeterProvider()
	stubMeter := stubMeterProvider.Meter(stubMeterName)
	stubOption := []api.Int64ObservableGaugeOption{
		api.WithUnit("1"),
		api.WithDescription("latest finalized height"),
	}
	stubGauge, _ := stubMeter.Int64ObservableGauge(stubMeterName, stubOption...)

	stubSrcAttrs := []attribute.KeyValue{
		attribute.Key("chain_id").String("51500001"),
		attribute.Key("direction").String("src"),
	}

	type fields struct {
		gauge         api.Int64ObservableGauge
		mutex         *sync.RWMutex
		attrsValueMap map[string]*Int64WithAttributes
	}
	type args struct {
		value int64
		attr  []attribute.KeyValue
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "should set value on gauge",
			fields: fields{
				gauge:         stubGauge,
				mutex:         &sync.RWMutex{},
				attrsValueMap: make(map[string]*Int64WithAttributes),
			},
			args: args{
				value: 1,
				attr:  stubSrcAttrs,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := &Int64SyncGauge{
				gauge:         tt.fields.gauge,
				mutex:         tt.fields.mutex,
				attrsValueMap: tt.fields.attrsValueMap,
			}
			g.Set(tt.args.value, tt.args.attr...)
		})
	}
}

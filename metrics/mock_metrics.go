// Code generated by MockGen. DO NOT EDIT.
// Source: metrics.go
//
// Generated by this command:
//
//	mockgen -source=metrics.go -package=metrics -destination=./mock_metrics.go
//

// Package metrics is a generated GoMock package.
package metrics

import (
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockExporterConfig is a mock of ExporterConfig interface.
type MockExporterConfig struct {
	ctrl     *gomock.Controller
	recorder *MockExporterConfigMockRecorder
}

// MockExporterConfigMockRecorder is the mock recorder for MockExporterConfig.
type MockExporterConfigMockRecorder struct {
	mock *MockExporterConfig
}

// NewMockExporterConfig creates a new mock instance.
func NewMockExporterConfig(ctrl *gomock.Controller) *MockExporterConfig {
	mock := &MockExporterConfig{ctrl: ctrl}
	mock.recorder = &MockExporterConfigMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockExporterConfig) EXPECT() *MockExporterConfigMockRecorder {
	return m.recorder
}

// exporterType mocks base method.
func (m *MockExporterConfig) exporterType() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "exporterType")
	ret0, _ := ret[0].(string)
	return ret0
}

// exporterType indicates an expected call of exporterType.
func (mr *MockExporterConfigMockRecorder) exporterType() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "exporterType", reflect.TypeOf((*MockExporterConfig)(nil).exporterType))
}

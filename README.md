# dcbg-dcjpy-relayer

## 概要

本リポジトリは IBC の Relayer のソースコードを管理するリポジトリとなります。

## rly

本アプリケーションはビルド後、CLI コマンドとして`rly`　バイナリーファイルが生成されます。

## 手順書

ローカル環境構築
https://decurret.atlassian.net/wiki/spaces/DIG/pages/2389639211

リリース手順
https://decurret.atlassian.net/wiki/spaces/DIG/pages/2432238156

## 開発ツール類

ローカル開発用の Docker コンテナには開発効率を上げるため下記ツールを組み込んでいます。

| 名前                                       | 用途                         |
| ------------------------------------------ | ---------------------------- |
| [delve](https://github.com/go-delve/delve) | コンテナリモートデバッガー   |
| [air](https://github.com/cosmtrek/air)     | ライブリロードコマンドライン |

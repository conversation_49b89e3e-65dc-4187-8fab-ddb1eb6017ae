ARG GOLANG_VER=1.21.7
ARG ALPINE_VER=3.19

FROM golang:${GOLANG_VER:-1.21.7}-alpine${ALPINE_VER:-3.19} AS build-env

RUN apk add --update --no-cache curl make git libc-dev bash gcc linux-headers eudev-dev

RUN if [ "${TARGETARCH}" = "arm64" ] && [ "${BUILDARCH}" != "arm64" ]; then \
    wget -c https://musl.cc/aarch64-linux-musl-cross.tgz -O - | tar -xzvv --strip-components 1 -C /usr; \
    elif [ "${TARGETARCH}" = "amd64" ] && [ "${BUILDARCH}" != "amd64" ]; then \
    wget -c https://musl.cc/x86_64-linux-musl-cross.tgz -O - | tar -xzvv --strip-components 1 -C /usr; \
    fi

WORKDIR /opt/relayer

COPY . .

RUN if [ "${TARGETARCH}" = "arm64" ] && [ "${BUI<PERSON>AR<PERSON>}" != "arm64" ]; then \
    export CC=aarch64-linux-musl-gcc CXX=aarch64-linux-musl-g++;\
    elif [ "${TARGETARCH}" = "amd64" ] && [ "${BUILDARCH}" != "amd64" ]; then \
    export CC=x86_64-linux-musl-gcc CXX=x86_64-linux-musl-g++; \
    fi; \
    GOOS=linux GOARCH=$TARGETARCH CGO_ENABLED=1 LDFLAGS='-linkmode external -extldflags "-static"' make install

RUN if [ -d "/go/bin/linux_${TARGETARCH}" ]; then mv /go/bin/linux_${TARGETARCH}/* /go/bin/; fi

# Build relayer binary
RUN make debug-build

# Build final image from scratch
FROM golang:${GOLANG_VER}-alpine${ALPINE_VER} AS final-image
RUN apk add --update --no-cache bash

ENV RLY_BIN=/bin/rly RLY_CONFIG_HOME=/opt/relayer/.relayer

WORKDIR /opt/relayer

# Install chain binaries
COPY --from=build-env /opt/relayer/build/rly /bin

# Relayer Config
COPY --from=build-env /opt/relayer/.relayer ./.relayer
COPY --from=build-env /opt/relayer/.default ./.default

# Handshake and Relayer Daemon Script
COPY --from=build-env /opt/relayer/scripts/ ./scripts

# Handshake and Relayer Daemon Script
ENTRYPOINT [ "/opt/relayer/scripts/handshake/ibc-handshake" ]

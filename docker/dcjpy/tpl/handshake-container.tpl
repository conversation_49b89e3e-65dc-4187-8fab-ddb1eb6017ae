
  relayer-handshake-_RLY_CONTAINER_SEQ:
    image: relayer-base
    profiles: [ "ibc" ]
    pull_policy: never
    build:
      context: .
      dockerfile: ./docker/dcjpy/Dockerfile.local
      args:
        GOLANG_VER: 1.21.7
        ALPINE_VER: 3.19
    volumes:
      - .default:/opt/relayer/.default
      - .local/relayer-handshake-_RLY_CONTAINER_SEQ:/opt/relayer/.relayer
    env_file:
      - ./docker/dcjpy/env/handshake-_RLY_CONTAINER_SEQ.env
      - ./docker/dcjpy/env/ibc-address-_RLY_CONTAINER_SEQ.env
    tty: true
    entrypoint: "/opt/relayer/scripts/handshake/ibc-handshake account-sync,balance-sync,token-transfer _IS_MULTI_TENANT"
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE

  relayer-account-sync-_RLY_CONTAINER_SEQ:
    image: relayer-base
    profiles: [ "ibc" ]
    pull_policy: never
    build:
      context: .
      dockerfile: ./docker/dcjpy/Dockerfile.local
      args:
        GOLANG_VER: 1.21.7
        ALPINE_VER: 3.19
    volumes:
      - .local/relayer-account-sync-_RLY_CONTAINER_SEQ:/opt/relayer/.relayer
    env_file:
      - ./docker/dcjpy/env/account-sync.env
      - ./docker/dcjpy/env/common-_RLY_CONTAINER_SEQ.env
      - ./docker/dcjpy/env/ibc-address-_RLY_CONTAINER_SEQ.env
    tty: true
    entrypoint: "/opt/relayer/scripts/relay/starter account-sync"
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    healthcheck:
      test: [ "CMD", "/opt/relayer/scripts/healthcheck", "account-sync" ]
      interval: 10s
      timeout: 10s
      retries: 10
      start_period: 30s

  relayer-balance-sync-_RLY_CONTAINER_SEQ:
    image: relayer-base
    profiles: [ "ibc" ]
    pull_policy: never
    volumes:
      - .local/relayer-balance-sync-_RLY_CONTAINER_SEQ:/opt/relayer/.relayer
    env_file:
      - ./docker/dcjpy/env/balance-sync.env
      - ./docker/dcjpy/env/common-_RLY_CONTAINER_SEQ.env
      - ./docker/dcjpy/env/ibc-address-_RLY_CONTAINER_SEQ.env
    tty: true
    entrypoint: "/opt/relayer/scripts/relay/starter balance-sync"
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    healthcheck:
      test: [ "CMD", "/opt/relayer/scripts/healthcheck", "balance-sync" ]
      interval: 10s
      timeout: 10s
      retries: 10
      start_period: 30s

  relayer-token-transfer-_RLY_CONTAINER_SEQ:
    image: relayer-base
    profiles: [ "ibc" ]
    pull_policy: never
    volumes:
      - .local/relayer-token-transfer-_RLY_CONTAINER_SEQ:/opt/relayer/.relayer
    env_file:
      - ./docker/dcjpy/env/token-transfer.env
      - ./docker/dcjpy/env/common-_RLY_CONTAINER_SEQ.env
      - ./docker/dcjpy/env/ibc-address-_RLY_CONTAINER_SEQ.env
    tty: true
    entrypoint: "/opt/relayer/scripts/relay/starter token-transfer"
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    healthcheck:
      test: [ "CMD", "/opt/relayer/scripts/healthcheck", "token-transfer" ]
      interval: 10s
      timeout: 10s
      retries: 3
      start_period: 30s


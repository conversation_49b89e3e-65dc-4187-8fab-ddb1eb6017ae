# Global ARG
ARG GOLANG_VER=1.21.7
ARG ALPINE_VER=3.19
ARG BUSYBOX_VER=1.34.1
ARG INFRA_TOOLKIT_VER=v0.0.6
ARG APP_GID="1000"
ARG APP_UID="100"
ARG APP_GROUP=relayer
ARG APP_USER=relayer

FROM golang:${GOLANG_VER}-alpine${ALPINE_VER} AS build-env

RUN apk add --update --no-cache curl make git libc-dev bash gcc linux-headers eudev-dev

RUN if [ "${TARGETARCH}" = "arm64" ] && [ "${BUILDARCH}" != "arm64" ]; then \
    wget -c https://musl.cc/aarch64-linux-musl-cross.tgz -O - | tar -xzvv --strip-components 1 -C /usr; \
    elif [ "${TARGETARCH}" = "amd64" ] && [ "${BUILDARCH}" != "amd64" ]; then \
    wget -c https://musl.cc/x86_64-linux-musl-cross.tgz -O - | tar -xzvv --strip-components 1 -C /usr; \
    fi

WORKDIR /opt/relayer

COPY . .

RUN if [ "${TARGETARCH}" = "arm64" ] && [ "${BUILDARCH}" != "arm64" ]; then \
    export CC=aarch64-linux-musl-gcc CXX=aarch64-linux-musl-g++;\
    elif [ "${TARGETARCH}" = "amd64" ] && [ "${BUILDARCH}" != "amd64" ]; then \
    export CC=x86_64-linux-musl-gcc CXX=x86_64-linux-musl-g++; \
    fi; \
    GOOS=linux GOARCH=$TARGETARCH CGO_ENABLED=1 LDFLAGS='-linkmode external -extldflags "-static"' make install

RUN if [ -d "/go/bin/linux_${TARGETARCH}" ]; then mv /go/bin/linux_${TARGETARCH}/* /go/bin/; fi

# Build relayer binary
RUN make build

# Use minimal busybox from infra-toolkit image for final scratch image
FROM ghcr.io/strangelove-ventures/infra-toolkit:${INFRA_TOOLKIT_VER} AS busybox-min

ARG APP_GID
ARG APP_UID
ARG APP_GROUP
ARG APP_USER

RUN addgroup --gid ${APP_GID} -S ${APP_GROUP} && adduser --uid ${APP_UID} -S ${APP_USER} -G ${APP_GROUP}

# Build final image from scratch
FROM golang:${GOLANG_VER}-alpine${ALPINE_VER} AS final-image
RUN apk add --update --no-cache bash

ARG APP_GID
ARG APP_UID
ARG APP_GROUP
ARG APP_USER

ENV RLY_BIN=/bin/rly RLY_CONFIG_HOME=/opt/relayer/.relayer

WORKDIR /opt/relayer

# Install chain binaries
COPY --from=build-env /opt/relayer/build/rly /bin

# Relayer Config
COPY --from=build-env /opt/relayer/.relayer ./.relayer
COPY --from=build-env /opt/relayer/.default ./.default


# Handshake and Relayer Daemon Script
COPY --from=build-env /opt/relayer/scripts/ ./scripts

# Handshake and Relayer Daemon Script
ENTRYPOINT [ "/opt/relayer/scripts/relay/starter" ]

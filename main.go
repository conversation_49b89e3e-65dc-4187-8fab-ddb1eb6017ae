package main

import (
	"log"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/cmd"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/relay/ethereum"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/relay/ethereum/signers/hd"

	ibft2 "github.com/decurret-lab/dcbg-dcjpy-relayer/provers/ibft2"
)

func main() {
	if err := cmd.Execute(
		ethereum.Module{},
		hd.Module{},
		ibft2.Module{},
	); err != nil {
		log.Fatal(err)
	}
}

services:

  relayer-handshake-1:
    image: relayer-base
    profiles: [ "ibc" ]
    pull_policy: never
    build:
      context: .
      dockerfile: ./docker/dcjpy/Dockerfile.local
      args:
        GOLANG_VER: 1.21.7
        ALPINE_VER: 3.19
    volumes:
      - .default:/opt/relayer/.default
      - .local/relayer-handshake-1:/opt/relayer/.relayer
    env_file:
      - ./docker/dcjpy/env/handshake-1.env
      - ./docker/dcjpy/env/ibc-address-1.env
    tty: true
    entrypoint: "/opt/relayer/scripts/handshake/ibc-handshake account-sync,balance-sync,token-transfer false"
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    healthcheck:
      test: [ "CMD", "/opt/relayer/scripts/healthcheck", "token-transfer" ]
      interval: 10s
      timeout: 10s
      retries: 10
      start_period: 30s

  relayer-account-sync-1:
    image: relayer-base
    profiles: [ "ibc" ]
    pull_policy: never
    build:
      context: .
      dockerfile: ./docker/dcjpy/Dockerfile.local
      args:
        GOLANG_VER: 1.21.7
        ALPINE_VER: 3.19
    volumes:
      - .local/relayer-account-sync-1:/opt/relayer/.relayer
    env_file:
      - ./docker/dcjpy/env/account-sync.env
      - ./docker/dcjpy/env/common-1.env
      - ./docker/dcjpy/env/ibc-address-1.env
    tty: true
    entrypoint: "/opt/relayer/scripts/relay/starter account-sync"
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    healthcheck:
      test: [ "CMD", "/opt/relayer/scripts/healthcheck", "account-sync" ]
      interval: 10s
      timeout: 10s
      retries: 10
      start_period: 30s

  relayer-balance-sync-1:
    image: relayer-base
    profiles: [ "ibc" ]
    pull_policy: never
    volumes:
      - .local/relayer-balance-sync-1:/opt/relayer/.relayer
    env_file:
      - ./docker/dcjpy/env/balance-sync.env
      - ./docker/dcjpy/env/common-1.env
      - ./docker/dcjpy/env/ibc-address-1.env
    tty: true
    entrypoint: "/opt/relayer/scripts/relay/starter balance-sync"
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    healthcheck:
      test: [ "CMD", "/opt/relayer/scripts/healthcheck", "balance-sync" ]
      interval: 10s
      timeout: 10s
      retries: 10
      start_period: 30s

  relayer-token-transfer-1:
    image: relayer-base
    profiles: [ "ibc" ]
    pull_policy: never
    volumes:
      - .local/relayer-token-transfer-1:/opt/relayer/.relayer
    env_file:
      - ./docker/dcjpy/env/token-transfer.env
      - ./docker/dcjpy/env/common-1.env
      - ./docker/dcjpy/env/ibc-address-1.env
    tty: true
    entrypoint: "/opt/relayer/scripts/relay/starter token-transfer"
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    healthcheck:
      test: [ "CMD", "/opt/relayer/scripts/healthcheck", "token-transfer" ]
      interval: 10s
      timeout: 10s
      retries: 3
      start_period: 30s


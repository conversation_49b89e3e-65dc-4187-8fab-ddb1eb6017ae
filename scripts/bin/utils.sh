#!/bin/bash
## common function define
countdown() {
  sec=$2
  echo -e "\xe2\x9c\xa8 \033[0;32m$1\033[0;39m"
  while [ "$sec" -ge 0 ]; do
    echo -ne "Waiting for $sec seconds...\033[0K\r"
    let "sec = sec - 1"
    sleep 1
  done
  echo
}
message() {
  case $1 in
  "info")
    echo -e "\xF0\x9F\x9A\x80 \033[0;32m$2\033[0;39m"
    ;;
  "success")
    echo -e "\xF0\x9F\x8D\xBB \033[0;32m$2\033[0;39m"
    ;;
  "warn")
    echo -e "\xF0\x9F\x9A\xA7 \033[1;33m$2\033[0;39m"
    ;;
  "err")
    echo -e "\xf0\x9f\x94\xa5 \033[1;31m$2\033[0;39m"
    ;;
  esac
}
# $1:呼び出される関数名 $2:表示するタイトル $3:配列変数の列挙（${array[@]}）
# 配列変数から選択された文字列を、$1で指定した関数の引数にセットして呼び出す。
menu() {
  choice=0
  local func=$1
  local TITLE=$2
  shift 2
  local menu=($@)
  tail=$(expr ${#menu[@]} - 1)
  printf "\e[32m$TITLE\e[m\n" >&2
  for _ in $(seq 0 "$tail"); do echo ""; done
  while true; do
    printf "\e[${#menu[@]}A\e[m" >&2
    for i in $(seq 0 "$tail"); do
      if [ $choice = "$i" ]; then
        printf "\e[1;31m>\e[m \e[1;4m" >&2
      else
        printf "  " >&2
      fi
      printf "${menu[$i]}\e[m\n" >&2
    done

    read -srn1 key
    if [ "$key" == $'\x1b' ]; then read -srn2 key; fi

    case $key in
    "j" | $'\x5b\x42')
      if [ "$choice" -lt "$tail" ]; then choice=$(expr $choice + 1); fi
      ;;
    "k" | $'\x5b\x41')
      if [ "$choice" -gt 0 ]; then choice=$(expr "$choice" - 1); fi
      ;;
    "")
      $func "${menu[$choice]}"
      return
      ;;
    esac
  done
}

choice() {
  read -p "$(message "info" "$1 (y/N):")" yn
  case "$yn" in [yY]*) ;; *)
    message "err" "canceled."
    exit
    ;;
  esac
}
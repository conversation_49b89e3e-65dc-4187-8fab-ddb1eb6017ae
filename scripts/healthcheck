#!/bin/bash

# root dir
ROOT=$(cd $(dirname $BASH_SOURCE)/; pwd)

# read utils
pushd "${ROOT}" >/dev/null || exit
source ./bin/utils.sh

#===============================================
# Query Client
#===============================================
query_client() {
  PROCESS_PATHS=$1
  CHAIN_1_CHAIN_ID=$2
  CHAIN_2_CHAIN_ID=$3

  clientHandshakeStatus=false
  srcClientHandshakeStatus=false
  dstClientHandshakeStatus=false

  ${RLY} query client "${PROCESS_PATHS}" "${CHAIN_1_CHAIN_ID}"
  srcClientResult=$?
  if [[ ${srcClientResult} -ne 1 ]] && ! [[ "${srcClientResult}" =~ "ERROR" ]]; then
    srcClientHandshakeStatus=true
  fi
  message "info" "[${PROCESS_PATHS}] SRC Chain(${CHAIN_1_CHAIN_ID}) client handshake: ${srcClientHandshakeStatus}"

  ${RLY} query client "${PROCESS_PATHS}" "${CHAIN_2_CHAIN_ID}"
  dstClientResult=$?
  if [[ "${dstClientResult}" -ne 1 ]] && ! [[ "${dstClientResult}" =~ "ERROR" ]]; then
    dstClientHandshakeStatus=true
  fi
  message "info" "[${PROCESS_PATHS}] DST Chain(${CHAIN_2_CHAIN_ID}) client handshake: ${dstClientHandshakeStatus}"

  if [ "${srcClientHandshakeStatus}" == true ] && [ "${dstClientHandshakeStatus}" == true ]; then
    clientHandshakeStatus=true
  fi
  message "success" "[${PROCESS_PATHS}] Client handshake status: ${clientHandshakeStatus}"
}

#===============================================
# Query Connection
#===============================================
query_connection() {
  PROCESS_PATHS=$1
  CHAIN_1_CHAIN_ID=$2
  CHAIN_2_CHAIN_ID=$3

  connectionHandshakeStatus=false
  srcConnectionHandshakeStatus=false
  dstConnectionHandshakeStatus=false
  connectionErrStr='client_id:"client" counterparty:<client_id:"client" connection_id:"connection" prefix:<> > '

  srcConnectionResult=$(${RLY} query connection "${PROCESS_PATHS}" "${CHAIN_1_CHAIN_ID}")
  if [[ "${srcConnectionResult}" != "${connectionErrStr}" ]]; then
    srcConnectionHandshakeStatus=true
  fi
  message "info" "[${PROCESS_PATHS}] SRC Chain(${CHAIN_1_CHAIN_ID}) connection handshake: ${srcConnectionHandshakeStatus}"

  dstConnectionResult=$(${RLY} query connection "${PROCESS_PATHS}" "${CHAIN_2_CHAIN_ID}")
  if [[ "${dstConnectionResult}" != "${connectionErrStr}" ]]; then
    dstConnectionHandshakeStatus=true
  fi
  message "info" "[${PROCESS_PATHS}] DST Chain(${CHAIN_2_CHAIN_ID}) connection handshake: ${dstConnectionHandshakeStatus}"

  if [ "${srcConnectionHandshakeStatus}" == true ] && [ "${dstConnectionHandshakeStatus}" == true ]; then
    connectionHandshakeStatus=true
  fi
  message "success" "[${PROCESS_PATHS}] Connection handshake status: ${connectionHandshakeStatus}"
}

#===============================================
# Query Channel
#===============================================
query_channel() {
  channelHandshakeStatus=false
  srcChannelHandshakeStatus=false
  dstChannelHandshakeStatus=false
  channelErrStr='ordering:ORDER_UNORDERED counterparty:<port_id:"port" channel_id:"channel" > version:"version" '

  srcChannelResult=$(${RLY} query channel "${PROCESS_PATHS}" "${CHAIN_1_CHAIN_ID}")
  if [[ "${srcChannelResult}" != "${channelErrStr}" ]]; then
    srcChannelHandshakeStatus=true
  fi
  message "info" "[${PROCESS_PATHS}] SRC Chain(${CHAIN_1_CHAIN_ID}) channel handshake: ${srcChannelHandshakeStatus}"

  dstChannelResult=$(${RLY} query channel "${PROCESS_PATHS}" "${CHAIN_2_CHAIN_ID}")
  if [[ "${dstChannelResult}" != "${channelErrStr}" ]]; then
    dstChannelHandshakeStatus=true
  fi
  message "info" "[${PROCESS_PATHS}] DST Chain(${CHAIN_2_CHAIN_ID}) channel handshake: ${dstChannelHandshakeStatus}"

  if [ "${srcChannelHandshakeStatus}" == true ] && [ "${dstChannelHandshakeStatus}" == true ]; then
    channelHandshakeStatus=true
  fi
  message "success" "[${PROCESS_PATHS}] Channel handshake status: ${channelHandshakeStatus}"
}

#===============================================
# Main
#===============================================
if [[ $# -eq 0 ]]; then
  message "warn" "A path must be specified to launch the script." 1>&2
  echo -e "[Usge] bash $(basename ${BASH_SOURCE[0]}) <Path Name> " 1>&2
  echo -e "[EXAMPLE] 'bash $(basename ${BASH_SOURCE[0]}) account-sync' will check handshake status." 1>&2
  exit 1
fi
PROCESS_PATHS=$1

if [ ! -f "${RLY_BIN}" ]; then
  message "err" "[${PROCESS_PATHS}] ${RLY_BIN} not generated yet."
  exit 1
fi

#===============================================
message "info" "[${PROCESS_PATHS}] Checking the handshake status..."
#===============================================

#===============================================
# Validaion Client
#===============================================
query_client "${PROCESS_PATHS}" "${CHAIN_1_CHAIN_ID}" "${CHAIN_2_CHAIN_ID}"

#===============================================
# Validaion Connection
#===============================================
query_connection "${PROCESS_PATHS}" "${CHAIN_1_CHAIN_ID}" "${CHAIN_2_CHAIN_ID}"

#===============================================
# Validaion Channel
#===============================================
query_channel "${PROCESS_PATHS}" "${CHAIN_1_CHAIN_ID}" "${CHAIN_2_CHAIN_ID}"

if [ "${clientHandshakeStatus}" = "true" ] && [ "${connectionHandshakeStatus}" = "true" ] && [ "${channelHandshakeStatus}" = "true" ]; then
  message "success" "[${PROCESS_PATHS}] Handshake completed."
  exit 0
else
  message "err" "[${PROCESS_PATHS}] Handshake incomplete."
  exit 1
fi


#!/bin/bash

# root dir
ROOT=$(cd $(dirname $BASH_SOURCE)/..; pwd)

# read utils
pushd "${ROOT}" >/dev/null || exit
source ./bin/utils.sh

#===============================================
# Query Client
#===============================================
query_client() {
  PROCESS_PATH=$1
  CHAIN_1_CHAIN_ID=$2
  CHAIN_2_CHAIN_ID=$3

  clientHandshakeStatus=false
  srcClientHandshakeStatus=false
  dstClientHandshakeStatus=false

  srcClientResult=$(${RLY} query client "${PROCESS_PATH}" "${CHAIN_1_CHAIN_ID}")

  if [[ ${srcClientResult} -ne 1 ]] && ! [[ "${srcClientResult}" =~ "ERROR" ]]; then
    srcClientHandshakeStatus=true
  fi
  message "info" "[${PROCESS_PATH}] SRC Chain(${CHAIN_1_CHAIN_ID}) client handshake: ${srcClientHandshakeStatus}"

  dstClientResult=$(${RLY} query client "${PROCESS_PATH}" "${CHAIN_2_CHAIN_ID}")
  if [[ "${dstClientResult}" -ne 1 ]] && ! [[ "${dstClientResult}" =~ "ERROR" ]]; then
    dstClientHandshakeStatus=true
  fi
  message "info" "[${PROCESS_PATH}] DST Chain(${CHAIN_2_CHAIN_ID}) client handshake: ${dstClientHandshakeStatus}"

  message "info" "[${PROCESS_PATH}] srcClientResult: ${srcClientResult}"
  message "info" "[${PROCESS_PATH}] dstClientResult: ${dstClientResult}"

  if [ "${srcClientHandshakeStatus}" == true ] && [ "${dstClientHandshakeStatus}" == true ]; then
    clientHandshakeStatus=true
  fi
  message "success" "[${PROCESS_PATH}] Client handshake status: ${clientHandshakeStatus}"
}

#===============================================
# Query Connection
#===============================================
query_connection() {
  PROCESS_PATH=$1
  CHAIN_1_CHAIN_ID=$2
  CHAIN_2_CHAIN_ID=$3

  connectionHandshakeStatus=false
  srcConnectionHandshakeStatus=false
  dstConnectionHandshakeStatus=false
  connectionErrStr='client_id:"client" counterparty:<client_id:"client" connection_id:"connection" prefix:<> > '

  srcConnectionResult=$(${RLY} query connection "${PROCESS_PATH}" "${CHAIN_1_CHAIN_ID}")
  if [[ "${srcConnectionResult}" != "${connectionErrStr}" ]]; then
    srcConnectionHandshakeStatus=true
  fi
  message "info" "[${PROCESS_PATH}] SRC Chain(${CHAIN_1_CHAIN_ID}) connection handshake: ${srcConnectionHandshakeStatus}"

  dstConnectionResult=$(${RLY} query connection "${PROCESS_PATH}" "${CHAIN_2_CHAIN_ID}")
  if [[ "${dstConnectionResult}" != "${connectionErrStr}" ]]; then
    dstConnectionHandshakeStatus=true
  fi
  message "info" "[${PROCESS_PATH}] DST Chain(${CHAIN_2_CHAIN_ID}) connection handshake: ${dstConnectionHandshakeStatus}"

  message "info" "[${PROCESS_PATH}] srcConnectionResult: ${srcConnectionResult}"
  message "info" "[${PROCESS_PATH}] dstConnectionResult: ${dstConnectionResult}"

  if [ "${srcConnectionHandshakeStatus}" == true ] && [ "${dstConnectionHandshakeStatus}" == true ]; then
    connectionHandshakeStatus=true
  fi
  message "success" "[${PROCESS_PATH}] Connection handshake status: ${connectionHandshakeStatus}"
}

#===============================================
# Query Channel
#===============================================
query_channel() {

  PROCESS_PATH=$1
  CHAIN_1_CHAIN_ID=$2
  CHAIN_2_CHAIN_ID=$3

  channelHandshakeStatus=false
  srcChannelHandshakeStatus=false
  dstChannelHandshakeStatus=false
  channelErrStr='ordering:ORDER_UNORDERED counterparty:<port_id:"port" channel_id:"channel" > version:"version" '

  srcChannelResult=$(${RLY} query channel "${PROCESS_PATH}" "${CHAIN_1_CHAIN_ID}")
  if [[ "${srcChannelResult}" != "${channelErrStr}" ]]; then
    srcChannelHandshakeStatus=true
  fi
  message "info" "[${PROCESS_PATH}] SRC Chain(${CHAIN_1_CHAIN_ID}) channel handshake: ${srcChannelHandshakeStatus}"

  dstChannelResult=$(${RLY} query channel "${PROCESS_PATH}" "${CHAIN_2_CHAIN_ID}")
  if [[ "${dstChannelResult}" != "${channelErrStr}" ]]; then
    dstChannelHandshakeStatus=true
  fi
  message "info" "[${PROCESS_PATH}] DST Chain(${CHAIN_2_CHAIN_ID}) channel handshake: ${dstChannelHandshakeStatus}"

  message "info" "[${PROCESS_PATH}] srcChannelResult: ${srcChannelResult}"
  message "info" "[${PROCESS_PATH}] dstChannelResult: ${dstChannelResult}"

  if [ "${srcChannelHandshakeStatus}" == true ] && [ "${dstChannelHandshakeStatus}" == true ]; then
    channelHandshakeStatus=true
  fi
  message "success" "[${PROCESS_PATH}] Channel handshake status: ${channelHandshakeStatus}"
}

#===============================================
# Main
#===============================================
if [[ $# -eq 0 ]]; then
  message "warn" "A path must be specified to launch the script." 1>&2
  echo -e "[Usge] bash $(basename ${BASH_SOURCE[0]}) <Path Name>" 1>&2
  echo -e "[EXAMPLE] 'bash $(basename ${BASH_SOURCE[0]}) account-sync' will start the account-sync handshake." 1>&2
  exit 1
fi

#===============================================
## INIT
#===============================================
RLY="${RLY_BIN} --home ${RLY_CONFIG_HOME}"
PROCESS_PATH=$1
IS_MULTI_TENANT=$2
LOOP_COUNT=1

#===============================================
# Handshake
#===============================================
if [ "${IS_MULTI_TENANT}" = "true" ] && [ "${PROCESS_PATH}" == "account-sync" ]; then
  LOOP_COUNT=4
fi

for ((i=0; i<$LOOP_COUNT; i++))
do
  if [ "${IS_MULTI_TENANT}" = "true" ] && [ "${PROCESS_PATH}" == "account-sync" ]; then
    cp -p /opt/relayer/.default/config/config.json /opt/relayer/.relayer/config/config.json
  fi

  #===============================================
  # Client
  #===============================================
  message "info" "[${PROCESS_PATH}] Client Handshake"
  txClientResult=$(${RLY} tx clients "${PROCESS_PATH}")
  message "success" "[${PROCESS_PATH}] Clients created: ${txClientResult}"

  ##===============================================
  ## Connection
  ##===============================================
  message "info" "[${PROCESS_PATH}] Connection Handshake"
  txConnectionResult=$(${RLY} tx connection "${PROCESS_PATH}")
  message "success" "[${PROCESS_PATH}] Connection created: ${txConnectionResult}"

  ##===============================================
  ## Channel
  ##===============================================
  message "info" "[${PROCESS_PATH}] Channel Handshake"
  txChannelResult=$(${RLY} tx channel "${PROCESS_PATH}")
  message "success" "[${PROCESS_PATH}] Channel created: ${txChannelResult}"

  ##===============================================
  ## Update Clients
  ##===============================================
  message "info" "[${PROCESS_PATH}] Update Clients"
  updateClientsResult=$(${RLY} tx update-clients "${PROCESS_PATH}")
  message "success" "[${PROCESS_PATH}] Update Clients: ${updateClientsResult}"
done

# Relayer config confirm
message "info" "[${PROCESS_PATH}] Relayer config confirm..."
${RLY} config show

exit 0
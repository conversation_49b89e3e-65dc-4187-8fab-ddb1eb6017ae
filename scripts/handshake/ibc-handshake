#!/bin/bash

# root dir
ROOT=$(cd $(dirname $BASH_SOURCE)/..; pwd)

# read utils
pushd "${ROOT}" >/dev/null || exit
source ./bin/utils.sh

#===============================================
# Main
#===============================================
if [[ $# -eq 0 ]]; then
  message "warn" "A path must be specified to launch the script." 1>&2
  echo -e "[Usge] bash $(basename ${BASH_SOURCE[0]}) <Path Names>" 1>&2
  echo -e "[EXAMPLE] 'bash $(basename ${BASH_SOURCE[0]}) account-sync,balance-sync,token-transfer' will start the account-sync balance-sync token-transfer handshake." 1>&2
  exit 1
fi

#===============================================
## INIT
#===============================================
RLY="${RLY_BIN} --home ${RLY_CONFIG_HOME}"
PROCESS_PATHS=$1
IS_MULTI_TENANT=${2:-false}

#===============================================
# Handshake
#===============================================
paths=${PROCESS_PATHS//,/ }
sorted_ibc_paths=( $( printf "%s\n" "${paths[@]}" | sort ) )

for ibc_path in "${sorted_ibc_paths[@]}" ; do
  message "info" "[${ibc_path}] Start Handshake. IS_MULTI_TENANT: ${IS_MULTI_TENANT}"
  bash ./handshake/handshaker "${ibc_path}" "${IS_MULTI_TENANT}"
  message "info" "[${ibc_path}] End Handshake."
done

exit 0
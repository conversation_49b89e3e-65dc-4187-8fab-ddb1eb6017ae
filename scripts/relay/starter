#!/bin/bash

# root dir
ROOT=$(cd $(dirname $BASH_SOURCE)/..; pwd)

# read utils
pushd "${ROOT}" >/dev/null || exit
source ./bin/utils.sh

#===============================================
# Main
#===============================================
if [[ $# -eq 0 ]]; then
  message "warn" "A path must be specified to launch the script." 1>&2
  echo -e "[Usge] bash $(basename ${BASH_SOURCE[0]}) <Path Name>" 1>&2
  echo -e "[EXAMPLE] 'bash $(basename ${BASH_SOURCE[0]}) account-sync' will start the account-sync daemon." 1>&2
  exit 1
fi

#===============================================
## INIT
#===============================================
RLY="${RLY_BIN} --home ${RLY_CONFIG_HOME}"
MAIN_PATH="account-sync"

PROCESS_PATH=$1

#===============================================
# START RLY DAEMON SERVICE
#===============================================
RELAY_INTERVAL="2s"

message "info" "[${PROCESS_PATH}] Starting the Relayer daemon with the path '${PROCESS_PATH}'"

exec ${RLY} service start "${PROCESS_PATH}" --relay-interval "${RELAY_INTERVAL}"

message "success" "[${PROCESS_PATH}] Started the Relayer daemon with the path '${PROCESS_PATH}'"

#!/usr/bin/env bash

set -eo pipefail
echo "Generating gogo proto code"
cd proto

buf mod update
buf generate --template buf.gen.gogo.yaml

cd ..

SRC_ROOT="github.com/decurret-lab/dcbg-dcjpy-relayer"

echo "Copy ethereum proto code"
# ethereum
cp -p $SRC_ROOT/pkg/relay/ethereum/*.pb.go pkg/relay/ethereum

echo "Copy signers proto code"
# signers
cp -p $SRC_ROOT/pkg/relay/ethereum/signers/hd/config.pb.go pkg/relay/ethereum/signers/hd

#echo "Copy IBC Client proto code"
## Client
#cp -p $SRC_ROOT/provers/ibft2/module/ibft2.pb.go pkg/ibc/clients/ibft2
#
#echo "Copy IBC proto code"
## IBC
#cp -p $SRC_ROOT/pkg/ibc/core/client/Client.pb.go pkg/ibc/core/client
#cp -p $SRC_ROOT/pkg/ibc/core/commitment/Commitment.pb.go pkg/ibc/core/commitment
#cp -p $SRC_ROOT/pkg/ibc/core/connection/Connection.pb.go pkg/ibc/core/connection

#echo "Copy solidity proto code"
## solidity
#cp -p $SRC_ROOT/pkg/solidity/*.pb.go pkg/solidity

echo "Copy prover proto code"
# prover
cp -p $SRC_ROOT/provers/ibft2/module/*.pb.go provers/ibft2

rm -rf github.com

# System Patterns: DCBG-DCJPY Relayer

## Architecture Overview
The relayer follows a modular architecture with clear separation of concerns:

```mermaid
graph TD
    A[Main Service] --> B[Chain Management]
    A --> C[Packet Relay]
    A --> D[Configuration]
    
    B --> E[DCBG Chain]
    B --> F[DCJPY Chain]
    
    C --> G[Message Processing]
    C --> H[Transaction Handling]
    
    D --> I[Chain Config]
    D --> J[Prover Config]
```

## Core Components

### 1. Chain Management
- Chain client interfaces
- Connection management
- Block header verification
- State tracking

### 2. Packet Relay
- Packet processing
- Transaction creation
- Message sequencing
- Acknowledgment handling

### 3. Configuration
- Chain-specific settings
- Prover configurations
- Path management
- Connection parameters

### 4. Provers
- IBFT2 light client implementation
- Header verification
- Consensus validation
- State proof verification

## Design Patterns

### 1. Interfaces
- Chain interfaces for blockchain interaction
- Prover interfaces for header verification
- Metrics collection interfaces
- Configuration interfaces

### 2. Strategy Pattern
- Configurable relay strategies
- Pluggable prover implementations
- Flexible message handling

### 3. Factory Pattern
- Chain client creation
- Configuration generation
- Prover instantiation

### 4. Observer Pattern
- Event monitoring
- Metrics collection
- State updates

## Critical Paths

### 1. Packet Relay Flow
```mermaid
sequenceDiagram
    participant Source
    participant Relayer
    participant Target
    
    Source->>Relayer: Packet Event
    Relayer->>Relayer: Verify Header
    Relayer->>Target: Submit Packet
    Target->>Relayer: Acknowledgment
    Relayer->>Source: Submit Ack
```

### 2. Configuration Flow
```mermaid
graph LR
    A[Config File] --> B[Parse Config]
    B --> C[Initialize Chains]
    C --> D[Setup Connections]
    D --> E[Start Relay]

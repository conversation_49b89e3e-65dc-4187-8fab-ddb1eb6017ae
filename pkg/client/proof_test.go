//go:generate mockgen -source=$GOFILE -package=client -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE

package client

import (
	"reflect"
	"testing"

	"github.com/google/go-cmp/cmp"
)

//// TODO ethclient.Client が HTTP接続してしまうのでテスト不可
//func TestETHClient_GetProof(t *testing.T) {
//
//	type fields struct {
//		Client *ethclient.Client
//		option option
//	}
//	type args struct {
//		address     common.Address
//		storageKeys [][]byte
//		blockNumber *big.Int
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *StateProof
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//		{
//			name: "should return StateProof.",
//			fields: fields{
//				Client: &ethclient.Client{},
//				option: option{
//					retryOpts: []retry.Option{
//						retry.Delay(1 * time.Second),
//						retry.Attempts(10),
//					},
//				},
//			},
//			args: args{
//				address:     common.Address{},
//				storageKeys: [][]byte{},
//				blockNumber: &big.Int{},
//			},
//			want: &StateProof{
//				Balance:         big.Int{},
//				CodeHash:        [32]byte{},
//				Nonce:           0,
//				StorageHash:     [32]byte{},
//				AccountProofRLP: nil,
//				StorageProofRLP: nil,
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			cl := ETHClient{
//				Client: tt.fields.Client,
//				option: tt.fields.option,
//			}
//			got, err := cl.GetProof(tt.args.address, tt.args.storageKeys, tt.args.blockNumber)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("ETHClient.GetProof() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				if diff := cmp.Diff(got, tt.want); diff != "" {
//					t.Errorf("ETHClient.GetProof() differs: (-got +want)n%s", diff)
//				}
//				//t.Errorf("ETHClient.GetProof() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
//// TODO ethclient.Client が HTTP接続してしまうのでテスト不可
//func TestETHClient_getProof(t *testing.T) {
//	type fields struct {
//		Client *ethclient.Client
//		option option
//	}
//	type args struct {
//		address     common.Address
//		storageKeys [][]byte
//		blockNumber string
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    []byte
//		wantErr bool
//	}{
//		{
//			name: "should return StateProof.",
//			fields: fields{
//				Client: &ethclient.Client{},
//				option: option{
//					retryOpts: []retry.Option{
//						retry.Delay(1 * time.Second),
//						retry.Attempts(10),
//					},
//				},
//			},
//			args: args{
//				address:     common.Address{},
//				storageKeys: [][]byte{},
//				blockNumber: "",
//			},
//			want:    []byte{},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			cl := ETHClient{
//				Client: tt.fields.Client,
//				option: tt.fields.option,
//			}
//			got, err := cl.getProof(tt.args.address, tt.args.storageKeys, tt.args.blockNumber)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("ETHClient.getProof() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("ETHClient.getProof() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func Test_encodeRLP(t *testing.T) {
	type args struct {
		proof []string
	}
	tests := []struct {
		name    string
		args    args
		want    []byte
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "should returns error Bytes not correctly encoded.",
			args: args{
				proof: []string{"0x84deadbeef"},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should returns error short integer not correctly encoded.",
			args: args{
				proof: []string{"0x0f"},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should returns error long integer not correctly encoded.",
			args: args{
				proof: []string{"0x0400"},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should returns error blank string not correctly encoded.",
			args: args{
				proof: []string{""},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should returns error Short string not correctly encoded.",
			args: args{
				proof: []string{"dog"},
			},
			want:    nil,
			wantErr: true,
		},
		// TODO AccountProof
		//{
		//	name: "should returns encodedRLP.",
		//	args: args{
		//		proof: []string{""},
		//	},
		//	want:    []uint8{},
		//	wantErr: false,
		//},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := encodeRLP(tt.args.proof)
			if (err != nil) != tt.wantErr {
				t.Errorf("encodeRLP() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("encodeRLP() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

func Test_decodeHexString(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name    string
		args    args
		want    []byte
		wantErr bool
	}{
		{
			name: "shoud return missing prefix error.",
			args: args{
				s: "56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "shoud return decodeed string.",
			args: args{
				s: "0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421",
			},
			want:    []uint8{0x56, 0xe8, 0x1f, 0x17, 0x1b, 0xcc, 0x55, 0xa6, 0xff, 0x83, 0x45, 0xe6, 0x92, 0xc0, 0xf8, 0x6e, 0x5b, 0x48, 0xe0, 0x1b, 0x99, 0x6c, 0xad, 0xc0, 0x01, 0x62, 0x2f, 0xb5, 0xe3, 0x63, 0xb4, 0x21},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := decodeHexString(tt.args.s)
			if (err != nil) != tt.wantErr {
				t.Errorf("decodeHexString() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("decodeHexString() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

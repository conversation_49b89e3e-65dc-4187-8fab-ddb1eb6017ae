package client

import (
	"encoding/hex"
	"math/big"
	"strings"
	"testing"

	"github.com/ethereum/go-ethereum/common"
	gethtypes "github.com/ethereum/go-ethereum/core/types"
	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/require"
)

func TestRevertReasonParser(t *testing.T) {
	// 1. Valid format
	s, err := parseRevertReason(
		hexToBytes("0x08c379a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000001a4e6f7420656e6f7567682045746865722070726f76696465642e000000000000"),
	)
	require.NoError(t, err)
	require.Equal(t, "Not enough Ether provided.", s)

	// 2. Empty bytes
	s, err = parseRevertReason(nil)
	require.NoError(t, err)
	require.Equal(t, "", s)

	// 3. Invalid format
	_, err = parseRevertReason([]byte{0})
	require.Error(t, err)
}

func hexToBytes(s string) []byte {
	reason, err := hex.DecodeString(strings.TrimPrefix(s, "0x"))
	if err != nil {
		panic(err)
	}
	return reason
}

// TODO ポインタ差異
//func TestDefaultOption(t *testing.T) {
//
//	tests := []struct {
//		name string
//		want *option
//	}{
//		{
//			name: "should return DefaultOption",
//			want: &option{
//				retryOpts: []retry.Option{
//					retry.Delay(1 * time.Second),
//					retry.Attempts(10),
//				},
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			if got := DefaultOption(); !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("DefaultOption() = %+v, want %+v", got, tt.want)
//			}
//		})
//	}
//}
//
//// TODO ポインタ差異
//func TestWithRetryOption(t *testing.T) {
//	type args struct {
//		rops []retry.Option
//	}
//	tests := []struct {
//		name string
//		args args
//		want Option
//	}{
//		{
//			name: "should return WithRetryOption",
//			args: args{
//				rops: []retry.Option{
//					retry.Delay(10 * time.Second),
//					retry.Attempts(20),
//				},
//			},
//			want: func(opt *option) {
//				opt.retryOpts = []retry.Option{
//					retry.Delay(10 * time.Second),
//					retry.Attempts(20),
//				}
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			if got := WithRetryOption(tt.args.rops...); !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("WithRetryOption() = %+v, want %+v", got, tt.want)
//			}
//		})
//	}
//}
//
//// TODO ethclient.Client が HTTP接続してしまうのでテスト不可
//func TestNewETHClient(t *testing.T) {
//
//	//mockEndpoint := "http://localhost:8451"
//	//mockRpcClient, _ := rpc.DialHTTP(mockEndpoint)
//
//	type args struct {
//		endpoint string
//		opts     []Option
//	}
//	tests := []struct {
//		name        string
//		prepareMock func()
//		args        args
//		want        *ETHClient
//		wantErr     bool
//	}{
//		{
//			name: "should creates a client that uses the given RPC client.",
//			prepareMock: func() {
//				//mockChainConfig.EXPECT().Build().Return(mockChainConfig, nil).AnyTimes()
//			},
//			args: args{
//				endpoint: "http://localhost:8451",
//				opts:     []Option{},
//			},
//			want: &ETHClient{
//				Client: &ethclient.Client{},
//				option: option{
//					retryOpts: []retry.Option{
//						retry.Delay(1 * time.Second),
//						retry.Attempts(10),
//					},
//				},
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := NewETHClient(tt.args.endpoint, tt.args.opts...)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("NewETHClient() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("NewETHClient() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
//// TODO ethclient.Client が HTTP接続してしまうのでテスト不可
//func TestETHClient_Raw(t *testing.T) {
//	type fields struct {
//		Client *ethclient.Client
//		option option
//	}
//	tests := []struct {
//		name   string
//		fields fields
//		want   *rpc.Client
//	}{
//		{
//			name: "should return nil.",
//			fields: fields{
//				Client: &ethclient.Client{},
//				option: option{
//					retryOpts: nil,
//				},
//			},
//			want: nil,
//		},
//		// TODO ポインタ
//		{
//			name: "should return RPC client.",
//			fields: fields{
//				Client: &ethclient.Client{},
//				option: option{
//					retryOpts: []retry.Option{
//						retry.Delay(1 * time.Second),
//						retry.Attempts(10),
//					},
//				},
//			},
//			want: &rpc.Client{},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			cl := &ETHClient{
//				Client: tt.fields.Client,
//				option: tt.fields.option,
//			}
//			if got := cl.Raw(); !reflect.DeepEqual(got, tt.want) {
//				if diff := cmp.Diff(got, tt.want); diff != "" {
//					t.Errorf("ETHClient.Raw() differs: (-got +want)n%s", diff)
//				}
//				t.Errorf("ETHClient.Raw() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

// TODO ethclient.Client が HTTP接続してしまうのでテスト不可
//func TestETHClient_GetTransactionReceipt(t *testing.T) {
//	type fields struct {
//		Client *ethclient.Client
//		option option
//	}
//	type args struct {
//		ctx              context.Context
//		txHash           common.Hash
//		enableDebugTrace bool
//	}
//	tests := []struct {
//		name             string
//		fields           fields
//		args             args
//		wantRc           *gethtypes.Receipt
//		wantRevertReason string
//		wantErr          bool
//	}{
//		{
//			"should return receipt if tx exists.",
//			fields{
//				&ethclient.Client{},
//				option{
//					retryOpts: []retry.Option{
//						retry.Delay(1 * time.Second),
//						retry.Attempts(10),
//					},
//				},
//			},
//			args{
//				context.TODO(),
//				common.Hash{},
//				false,
//			},
//			&gethtypes.Receipt{
//				Type:              0,
//				PostState:         nil,
//				Status:            0,
//				CumulativeGasUsed: 0,
//				Bloom:             gethtypes.Bloom{},
//				Logs:              nil,
//				TxHash:            common.Hash{},
//				ContractAddress:   common.Address{},
//				GasUsed:           0,
//				EffectiveGasPrice: &big.Int{},
//				BlockHash:         common.Hash{},
//				BlockNumber:       &big.Int{},
//				TransactionIndex:  0,
//			},
//			"",
//			false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			cl := &ETHClient{
//				Client: tt.fields.Client,
//				option: tt.fields.option,
//			}
//			gotRc, gotRevertReason, err := cl.GetTransactionReceipt(tt.args.ctx, tt.args.txHash, tt.args.enableDebugTrace)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("ETHClient.GetTransactionReceipt() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(gotRc, tt.wantRc) {
//				if diff := cmp.Diff(gotRc, tt.wantRc); diff != "" {
//					t.Errorf("ETHClient.GetTransactionReceipt() differs: (-got +want)n%s", diff)
//				}
//				t.Errorf("ETHClient.GetTransactionReceipt() gotRc = %v, want %v", gotRc, tt.wantRc)
//			}
//			if gotRevertReason != tt.wantRevertReason {
//				if diff := cmp.Diff(gotRevertReason, tt.wantRevertReason); diff != "" {
//					t.Errorf("ETHClient.GetTransactionReceipt() differs: (-got +want)n%s", diff)
//				}
//				t.Errorf("ETHClient.GetTransactionReceipt() gotRevertReason = %v, want %v", gotRevertReason, tt.wantRevertReason)
//			}
//		})
//	}
//}
//
//// TODO ethclient.Client が HTTP接続してしまうのでテスト不可
//func TestETHClient_WaitForReceiptAndGet(t *testing.T) {
//
//	type fields struct {
//		Client *ethclient.Client
//		option option
//	}
//	type args struct {
//		ctx              context.Context
//		txHash           common.Hash
//		enableDebugTrace bool
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *gethtypes.Receipt
//		want1   string
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//		{
//			name: "should return receipt if tx exists.",
//			fields: fields{
//				Client: &ethclient.Client{},
//				option: option{
//					retryOpts: []retry.Option{
//						retry.Delay(10 * time.Second),
//						retry.Attempts(20),
//					},
//				},
//			},
//			args: args{
//				ctx:              context.TODO(),
//				txHash:           common.Hash{},
//				enableDebugTrace: false,
//			},
//			want: &gethtypes.Receipt{
//				Type:              0,
//				PostState:         nil,
//				Status:            0,
//				CumulativeGasUsed: 0,
//				Bloom:             gethtypes.Bloom{},
//				Logs:              nil,
//				TxHash:            common.Hash{},
//				ContractAddress:   common.Address{},
//				GasUsed:           0,
//				EffectiveGasPrice: &big.Int{},
//				BlockHash:         common.Hash{},
//				BlockNumber:       &big.Int{},
//				TransactionIndex:  0,
//			},
//			want1:   "",
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			cl := &ETHClient{
//				Client: tt.fields.Client,
//				option: tt.fields.option,
//			}
//			got, got1, err := cl.WaitForReceiptAndGet(tt.args.ctx, tt.args.txHash, tt.args.enableDebugTrace)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("ETHClient.WaitForReceiptAndGet() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("ETHClient.WaitForReceiptAndGet() got = %v, want %v", got, tt.want)
//			}
//			if got1 != tt.want1 {
//				t.Errorf("ETHClient.WaitForReceiptAndGet() got1 = %v, want %v", got1, tt.want1)
//			}
//		})
//	}
//}
// TODO ethclient.Client が HTTP接続してしまうのでテスト不可
//func TestETHClient_DebugTraceTransaction(t *testing.T) {
//
//	type fields struct {
//		Client *ethclient.Client
//		option option
//	}
//	type args struct {
//		ctx    context.Context
//		txHash common.Hash
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    string
//		wantErr bool
//	}{
//		{
//			name: "should return debug trace transaction.",
//			fields: fields{
//				Client: &ethclient.Client{},
//				option: option{
//					retryOpts: nil,
//				},
//			},
//			args: args{
//				ctx:    context.TODO(),
//				txHash: common.Hash{},
//			},
//			want:    "",
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			cl := &ETHClient{
//				Client: tt.fields.Client,
//				option: tt.fields.option,
//			}
//			got, err := cl.DebugTraceTransaction(tt.args.ctx, tt.args.txHash)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("ETHClient.DebugTraceTransaction() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if got != tt.want {
//				if diff := cmp.Diff(got, tt.want); diff != "" {
//					t.Errorf("ETHClient.DebugTraceTransaction() differs: (-got +want)n%s", diff)
//				}
//				t.Errorf("ETHClient.DebugTraceTransaction() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func TestReceipt_HasRevertReason(t *testing.T) {

	type fields struct {
		Receipt      gethtypes.Receipt
		RevertReason []byte
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "should return true if receipt has revert reason.",
			fields: fields{
				Receipt: gethtypes.Receipt{
					Type:              0,
					PostState:         nil,
					Status:            0,
					CumulativeGasUsed: 0,
					Bloom:             gethtypes.Bloom{},
					Logs:              nil,
					TxHash:            common.Hash{},
					ContractAddress:   common.Address{},
					GasUsed:           0,
					EffectiveGasPrice: &big.Int{},
					BlockHash:         common.Hash{},
					BlockNumber:       &big.Int{},
					TransactionIndex:  0,
				},
				RevertReason: nil,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rc := Receipt{
				Receipt:      tt.fields.Receipt,
				RevertReason: tt.fields.RevertReason,
			}
			if got := rc.HasRevertReason(); got != tt.want {
				t.Errorf("Receipt.HasRevertReason() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestReceipt_GetRevertReason(t *testing.T) {
	type fields struct {
		Receipt      gethtypes.Receipt
		RevertReason []byte
	}
	tests := []struct {
		name    string
		fields  fields
		want    string
		wantErr bool
	}{
		{
			name: "should return revert reason.",
			fields: fields{
				Receipt: gethtypes.Receipt{
					Type:              0,
					PostState:         nil,
					Status:            0,
					CumulativeGasUsed: 0,
					Bloom:             gethtypes.Bloom{},
					Logs:              nil,
					TxHash:            common.Hash{},
					ContractAddress:   common.Address{},
					GasUsed:           0,
					EffectiveGasPrice: &big.Int{},
					BlockHash:         common.Hash{},
					BlockNumber:       &big.Int{},
					TransactionIndex:  0,
				},
				RevertReason: nil,
			},
			want:    "",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rc := Receipt{
				Receipt:      tt.fields.Receipt,
				RevertReason: tt.fields.RevertReason,
			}
			got, err := rc.GetRevertReason()
			if (err != nil) != tt.wantErr {
				t.Errorf("Receipt.GetRevertReason() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Receipt.GetRevertReason() differs: (-got +want)n%s", diff)
				}
				t.Errorf("Receipt.GetRevertReason() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_parseRevertReason(t *testing.T) {
	type args struct {
		bz []byte
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO struct の want が nil を許容しないので除外
		//{
		//	name: "should returns error because length is 0.",
		//	args: args{
		//		bz: []byte{},
		//	},
		//	want:    nil,
		//	wantErr: true,
		//},
		{
			name: "should returns an error because length is less than 68.",
			args: args{
				bz: make([]byte, 67),
			},
			want:    "",
			wantErr: true,
		},
		// TODO https://docs.goquorum.consensys.io/configure-and-manage/manage/revert-reason#revert-reason-format
		//{
		//	name: "should return revert reason.",
		//	args: args{
		//		bz: []byte(""),
		//	},
		//	want:    "",
		//	wantErr: false,
		//},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parseRevertReason(tt.args.bz)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseRevertReason() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("parseRevertReason() differs: (-got +want)n%s", diff)
				}
				t.Errorf("parseRevertReason() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_searchRevertReason(t *testing.T) {
	type args struct {
		result *callFrame
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "should return result revert reason.",
			args: args{
				result: &callFrame{
					Type:         0,
					From:         common.Address{},
					Gas:          0,
					GasUsed:      0,
					To:           nil,
					Input:        nil,
					Output:       nil,
					Error:        "",
					RevertReason: "Not enough Ether provided",
					Calls:        nil,
					Logs:         nil,
					Value:        &big.Int{},
				},
			},
			want:    "Not enough Ether provided",
			wantErr: false,
		},
		{
			name: "should return call revert reason.",
			args: args{
				result: &callFrame{
					Type:         0,
					From:         common.Address{},
					Gas:          0,
					GasUsed:      0,
					To:           nil,
					Input:        nil,
					Output:       nil,
					Error:        "",
					RevertReason: "",
					Calls: []callFrame{
						{
							Type:         0,
							From:         common.Address{},
							Gas:          0,
							GasUsed:      0,
							To:           nil,
							Input:        nil,
							Output:       nil,
							Error:        "",
							RevertReason: "Not enough Ether provided",
							Logs:         nil,
							Value:        &big.Int{},
						},
					},
					Logs:  nil,
					Value: &big.Int{},
				},
			},
			want:    "Not enough Ether provided",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := searchRevertReason(tt.args.result)
			if (err != nil) != tt.wantErr {
				t.Errorf("searchRevertReason() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("searchRevertReason() differs: (-got +want)n%s", diff)
				}
				//t.Errorf("searchRevertReason() = %v, want %v", got, tt.want)
			}
		})
	}
}

//// TODO ethclient.Client が HTTP接続してしまうのでテスト不可
//func TestETHClient_EstimateGasFromTx(t *testing.T) {
//	type fields struct {
//		Client *ethclient.Client
//		option option
//	}
//	type args struct {
//		ctx context.Context
//		tx  *gethtypes.Transaction
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    uint64
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//		{
//			name: "should return gas from tx",
//			fields: fields{
//				Client: &ethclient.Client{},
//				option: option{
//					retryOpts: nil,
//				},
//			},
//			args: args{
//				ctx: context.TODO(),
//				tx:  &gethtypes.Transaction{},
//			},
//			want:    0,
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			cl := &ETHClient{
//				Client: tt.fields.Client,
//				option: tt.fields.option,
//			}
//			got, err := cl.EstimateGasFromTx(tt.args.ctx, tt.args.tx)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("ETHClient.EstimateGasFromTx() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if got != tt.want {
//				if diff := cmp.Diff(got, tt.want); diff != "" {
//					t.Errorf("ETHClient.EstimateGasFromTx() differs: (-got +want)n%s", diff)
//				}
//				//t.Errorf("ETHClient.EstimateGasFromTx() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

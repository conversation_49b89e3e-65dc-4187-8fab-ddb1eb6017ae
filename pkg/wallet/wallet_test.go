//go:generate mockgen -source=$GOFILE -package=wallet -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package wallet

import (
	"reflect"
	"testing"

	"github.com/google/go-cmp/cmp"
)

func TestParseHDPathLevel(t *testing.T) {
	type args struct {
		path string
	}
	tests := []struct {
		name    string
		args    args
		want    *HDPathLevel
		wantErr bool
	}{
		{
			name: "should return an invalid path level error.",
			args: args{
				path: "m/44'/60'/0'/0",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return prefix should be 'm' error.",
			args: args{
				path: "x/44'/60'/0'/0/0",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return missing apostrophe error.",
			args: args{
				path: "x\\44'\\60'\\0'\\0\\0",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return hp.Purpose error.",
			args: args{
				path: "m/10000X0000'/60'/0'/0/0",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return hp.CoinType error.",
			args: args{
				path: "m/44'/60000X0000'/0'/0/0",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return hp.Account error.",
			args: args{
				path: "m/44'/60'/X'/0/0",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return hp.Change error.",
			args: args{
				path: "m/44'/60'/0'/X/0",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return hp.Index error.",
			args: args{
				path: "m/44'/60'/0'/0/X",
			},
			want:    nil,
			wantErr: true,
		},

		{
			name: "should parse HDPathLevel.",
			args: args{
				path: "m/44'/60'/0'/0/0",
			},
			want: &HDPathLevel{
				Purpose:  44,
				CoinType: 60,
				Account:  0,
				Change:   0,
				Index:    0,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseHDPathLevel(tt.args.path)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseHDPathLevel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("ParseHDPathLevel() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

func Test_checkValidApostrophe(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "should check valid apostrophe.",
			args: args{
				s: "math razor capable expose worth grape metal sunset metal sudden usage scheme",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := checkValidApostrophe(tt.args.s); got != tt.want {
				t.Errorf("checkValidApostrophe() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_strToUint32(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name    string
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "should return error.",
			args: args{
				s: "429496729X",
			},
			want:    0,
			wantErr: true,
		},
		{
			name: "should return uint32.",
			args: args{
				s: "4294967295",
			},
			want:    4294967295,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := strToUint32(tt.args.s)
			if (err != nil) != tt.wantErr {
				t.Errorf("strToUint32() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("strToUint32() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

func TestHDPathLevel_Validate(t *testing.T) {
	type fields struct {
		Purpose  uint32
		CoinType uint32
		Account  uint32
		Change   uint32
		Index    uint32
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "should return purpose should be 44 error.",
			fields: fields{
				Purpose:  45,
				CoinType: 60,
				Account:  0,
				Change:   0,
				Index:    0,
			},
			wantErr: true,
		},
		{
			name: "should return change should be 0 or 1 error.",
			fields: fields{
				Purpose:  44,
				CoinType: 60,
				Account:  0,
				Change:   2,
				Index:    0,
			},
			wantErr: true,
		},
		{
			name: "should Validate HDPathLevel.",
			fields: fields{
				Purpose:  44,
				CoinType: 60,
				Account:  0,
				Change:   0,
				Index:    0,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			hp := &HDPathLevel{
				Purpose:  tt.fields.Purpose,
				CoinType: tt.fields.CoinType,
				Account:  tt.fields.Account,
				Change:   tt.fields.Change,
				Index:    tt.fields.Index,
			}
			if err := hp.Validate(); (err != nil) != tt.wantErr {
				t.Errorf("HDPathLevel.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestHDPathLevel_String(t *testing.T) {
	type fields struct {
		Purpose  uint32
		CoinType uint32
		Account  uint32
		Change   uint32
		Index    uint32
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "should return Format String.",
			fields: fields{
				Purpose:  44,
				CoinType: 60,
				Account:  0,
				Change:   0,
				Index:    0,
			},
			want: "m/44'/60'/0'/0/0",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			hp := &HDPathLevel{
				Purpose:  tt.fields.Purpose,
				CoinType: tt.fields.CoinType,
				Account:  tt.fields.Account,
				Change:   tt.fields.Change,
				Index:    tt.fields.Index,
			}
			if got := hp.String(); got != tt.want {
				t.Errorf("HDPathLevel.String() = %v, want %v", got, tt.want)
			}
		})
	}
}

//// TODO Curve の値が設定不可のためテスト不可
//func TestGetPrvKeyFromHDWallet(t *testing.T) {
//
//	var stubPublicKeyX = &big.Int{}
//	var stubPublicKeyY = &big.Int{}
//	var stubPrivateKeyD = &big.Int{}
//
//	stubPublicKeyX, _ = (&big.Int{}).SetString("6539035237218799949430983481213690656854243018933010602182908071", 10)
//	stubPublicKeyY, _ = (&big.Int{}).SetString("7761845796299318165034273488371161766718321743910432918057800769", 10)
//	stubPrivateKeyD, _ = (&big.Int{}).SetString("103621489529634167205139750085116413410837146389409995551260132005794187042961", 10)
//
//	stubMnemonic :=  "math razor capable expose worth grape metal sunset metal sudden usage scheme"
//	stubPath := "m/44'/60'/0'/0/0"
//	stubHp, _ := ParseHDPathLevel(stubPath)
//	stubSeed := bip39.NewSeed(stubMnemonic, "")
//	stubMasterKey, _ := hdkeychain.NewMaster(stubSeed, &chaincfg.MainNetParams)
//	acc, _ := stubMasterKey.Derive(hdkeychain.HardenedKeyStart + stubHp.Purpose)
//	acc, _ = acc.Derive(hdkeychain.HardenedKeyStart + stubHp.CoinType)
//	acc, _ = acc.Derive(hdkeychain.HardenedKeyStart + stubHp.Account)
//	acc, _ = acc.Derive(0 + stubHp.Change)
//	acc, _ = acc.Derive(stubHp.Index)
//	stubBtcecPrivKey, _ := acc.ECPrivKey()
//
//	// TODO Curve の値が設定不可
//	stubCurve := stubBtcecPrivKey.S256()
//
//	type args struct {
//		seed []byte
//		hp   *HDPathLevel
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    *ecdsa.PrivateKey
//		wantErr bool
//	}{
//		{
//			name: "should return PrvKeyFromHDWallet.",
//			args: args{
//				seed: bip39.NewSeed("math razor capable expose worth grape metal sunset metal sudden usage scheme", ""),
//				hp: &HDPathLevel{
//					Purpose:  44,
//					CoinType: 60,
//					Account:  0,
//					Change:   0,
//					Index:    0,
//				},
//			},
//			want: &ecdsa.PrivateKey{
//				PublicKey: ecdsa.PublicKey{
//					Curve: stubCurve,
//					X: stubPublicKeyX,
//					Y: stubPublicKeyY,
//				},
//				D: stubPrivateKeyD,
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := GetPrvKeyFromHDWallet(tt.args.seed, tt.args.hp)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("GetPrvKeyFromHDWallet() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				if diff := cmp.Diff(got, tt.want); diff != "" {
//					t.Errorf("GetPrvKeyFromHDWallet() differs: (-got +want)n%s", diff)
//				}
//			}
//		})
//	}
//}
//
//// TODO Curve の値が設定不可のためテスト不可
//func TestGetPrvKeyFromMnemonicAndHDWPath(t *testing.T) {
//
//	var stubPublicKeyX = &big.Int{}
//	var stubPublicKeyY = &big.Int{}
//	var stubPrivateKeyD = &big.Int{}
//
//	stubPublicKeyX, _ = (&big.Int{}).SetString("6539035237218799949430983481213690656854243018933010602182908071", 10)
//	stubPublicKeyY, _ = (&big.Int{}).SetString("7761845796299318165034273488371161766718321743910432918057800769", 10)
//	stubPrivateKeyD, _ = (&big.Int{}).SetString("103621489529634167205139750085116413410837146389409995551260132005794187042961", 10)
//
//	stubMnemonic :=  "math razor capable expose worth grape metal sunset metal sudden usage scheme"
//	stubPath := "m/44'/60'/0'/0/0"
//	stubHp, _ := ParseHDPathLevel(stubPath)
//	stubSeed := bip39.NewSeed(stubMnemonic, "")
//	stubMasterKey, _ := hdkeychain.NewMaster(stubSeed, &chaincfg.MainNetParams)
//	acc, _ := stubMasterKey.Derive(hdkeychain.HardenedKeyStart + stubHp.Purpose)
//	acc, _ = acc.Derive(hdkeychain.HardenedKeyStart + stubHp.CoinType)
//	acc, _ = acc.Derive(hdkeychain.HardenedKeyStart + stubHp.Account)
//	acc, _ = acc.Derive(0 + stubHp.Change)
//	acc, _ = acc.Derive(stubHp.Index)
//	stubBtcecPrivKey, _ := acc.ECPrivKey()
//
//	// TODO Curve の値が設定不可
//	stubCurve := stubBtcecPrivKey.S256()
//
//	type args struct {
//		mnemonic string
//		path     string
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    *ecdsa.PrivateKey
//		wantErr bool
//	}{
//		// TODO Curve の値が設定不可
//		{
//			name: "should return PrvKeyFromHDWallet.",
//			args: args{
//				mnemonic: stubMnemonic,
//				path:     stubPath,
//			},
//			want: &ecdsa.PrivateKey{
//				PublicKey: ecdsa.PublicKey{
//					Curve: stubCurve,
//					X: stubPublicKeyX,
//					Y: stubPublicKeyY,
//				},
//				D: stubPrivateKeyD,
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := GetPrvKeyFromMnemonicAndHDWPath(tt.args.mnemonic, tt.args.path)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("GetPrvKeyFromMnemonicAndHDWPath() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				if diff := cmp.Diff(got, tt.want); diff != "" {
//					t.Errorf("GetPrvKeyFromMnemonicAndHDWPath() differs: (-got +want)n%s", diff)
//				}
//			}
//		})
//	}
//}

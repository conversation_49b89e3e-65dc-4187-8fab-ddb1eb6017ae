package utils

import (
	"math/big"
	"reflect"
	"testing"
)

func TestParseEtherAmount(t *testing.T) {
	type args struct {
		amount string
	}
	tests := []struct {
		name    string
		args    args
		want    *big.Int
		wantErr bool
	}{
		{
			name: "Returns an error if an unknown suffix is unknown included.",
			args: args{
				amount: "123dummy",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "If ether is included, returns the parsed amount.",
			args: args{
				amount: "1ether",
			},
			want:    big.NewInt(1000000000000000000),
			wantErr: false,
		},
		{
			name: "If gwei is included, returns the parsed amount.",
			args: args{
				amount: "234gwei",
			},
			want:    big.NewInt(234000000000),
			wantErr: false,
		},
		{
			name: "If wei is included, returns the parsed amount.",
			args: args{
				amount: "567wei",
			},
			want:    big.NewInt(567),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseEtherAmount(tt.args.amount)
			if (err != nil) != tt.wantErr {
				t.E<PERSON>rf("ParseEtherAmount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ParseEtherAmount() = %v, want %v", got, tt.want)
			}
		})
	}
}

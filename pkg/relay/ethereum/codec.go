//go:generate mockgen -source=$GOFILE -package=ethereum -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package ethereum

import (
	codectypes "github.com/cosmos/cosmos-sdk/codec/types"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
)

// RegisterInterfaces register the module interfaces to protobuf Any.
func RegisterInterfaces(registry codectypes.InterfaceRegistry) {
	registry.RegisterImplementations(
		(*core.ChainConfig)(nil),
		&ChainConfig{},
	)
	registry.RegisterImplementations(
		(*core.MsgID)(nil),
		&MsgID{},
	)
}

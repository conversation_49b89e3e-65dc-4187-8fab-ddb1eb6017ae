//go:generate mockgen -source=$GOFILE -package=ethereum -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package ethereum

import (
	"testing"

	codectypes "github.com/cosmos/cosmos-sdk/codec/types"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
)

func TestRegisterInterfaces(t *testing.T) {
	type args struct {
		registry codectypes.InterfaceRegistry
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "RegisterInterfaces",
			args: args{
				registry: core.MakeCodec().InterfaceRegistry(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			RegisterInterfaces(tt.args.registry)
		})
	}
}

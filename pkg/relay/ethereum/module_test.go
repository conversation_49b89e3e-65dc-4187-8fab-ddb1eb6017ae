//go:generate mockgen -source=$GOFILE -package=ethereum -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package ethereum

import (
	"reflect"
	"testing"

	codectypes "github.com/cosmos/cosmos-sdk/codec/types"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/config"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/log"
	"github.com/spf13/cobra"
	"golang.org/x/exp/slog"
)

func TestModule_Name(t *testing.T) {
	tests := []struct {
		name string
		m    Module
		want string
	}{
		{
			name: "should return ModuleName.",
			m:    Module{},
			want: "ethereum.chain",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := Module{}
			if got := m.Name(); got != tt.want {
				t.Errorf("Module.Name() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestModule_RegisterInterfaces(t *testing.T) {
	type args struct {
		registry codectypes.InterfaceRegistry
	}
	tests := []struct {
		name string
		m    Module
		args args
	}{
		{
			name: "should register the module interfaces to protobuf Any.",
			m:    Module{},
			args: args{
				registry: core.MakeCodec().InterfaceRegistry(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := Module{}
			m.RegisterInterfaces(tt.args.registry)
		})
	}
}

// TODO 一部ケース実施不可
func TestModule_GetCmd(t *testing.T) {

	stubGlobalConfig := config.GlobalConfig{
		Timeout:        "10s",
		LightCacheSize: 20,
		LoggerConfig: config.LoggerConfig{
			Level:  "INFO",
			Format: "text",
			Output: "stdout",
		},
	}

	type args struct {
		ctx *config.Context
	}
	tests := []struct {
		name string
		m    Module
		args args
		want *cobra.Command
	}{
		{
			name: "should return nil.",
			m:    Module{},
			args: args{
				ctx: &config.Context{
					Modules: nil,
					Codec:   core.MakeCodec(),
					Config: &config.Config{
						Global:     stubGlobalConfig,
						Chains:     []core.ChainProverConfig{},
						Paths:      core.Paths{},
						ConfigPath: ".",
					},
				},
			},
			want: nil,
		},

		// TODO 自パッケージ配下で自身の Modules は指定できないため実施不可
		//{
		//	name: "should return Cmd.",
		//	m:    Module{},
		//	args: args{
		//		ctx: &config.Context{
		//			Modules: nil,
		//			Codec:   core.MakeCodec(),
		//			Config: &config.Config{
		//				Global:     stubGlobalConfig,
		//				Chains:     []core.ChainProverConfig{},
		//				Paths:      core.Paths{},
		//				ConfigPath: "config/config.json",
		//			},
		//		},
		//	},
		//	want: &cobra.Command{
		//		Use:                    "",
		//		Aliases:                nil,
		//		SuggestFor:             nil,
		//		Short:                  "",
		//		GroupID:                "",
		//		Long:                   "",
		//		Example:                "",
		//		ValidArgs:              nil,
		//		ValidArgsFunction:      nil,
		//		Args:                   nil,
		//		ArgAliases:             nil,
		//		BashCompletionFunction: "",
		//		Deprecated:             "",
		//		Annotations:            nil,
		//		Version:                "",
		//		PersistentPreRun:       nil,
		//		PersistentPreRunE:      nil,
		//		PreRun:                 nil,
		//		PreRunE:                nil,
		//		Run:                    nil,
		//		RunE:                   nil,
		//		PostRun:                nil,
		//		PostRunE:               nil,
		//		PersistentPostRun:      nil,
		//		PersistentPostRunE:     nil,
		//		FParseErrWhitelist: cobra.FParseErrWhitelist{
		//			UnknownFlags: false,
		//		},
		//		CompletionOptions: cobra.CompletionOptions{
		//			DisableDefaultCmd:   false,
		//			DisableNoDescFlag:   false,
		//			DisableDescriptions: false,
		//			HiddenDefaultCmd:    false,
		//		},
		//		TraverseChildren:           false,
		//		Hidden:                     false,
		//		SilenceErrors:              false,
		//		SilenceUsage:               false,
		//		DisableFlagParsing:         false,
		//		DisableAutoGenTag:          false,
		//		DisableFlagsInUseLine:      false,
		//		DisableSuggestions:         false,
		//		SuggestionsMinimumDistance: 0,
		//	},
		//},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := Module{}
			if got := m.GetCmd(tt.args.ctx); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Module.GetCmd() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetModuleLogger(t *testing.T) {
	tests := []struct {
		name string
		want *log.DcjpyLogger
	}{
		{
			name: "should return default logger when nil",
			want: &log.DcjpyLogger{
				Logger: &slog.Logger{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetModuleLogger()
			if got == nil {
				t.Errorf("GetModuleLogger() = %v, want %v", got, tt.want)
			}
		})
	}
}

//go:generate mockgen -source=$GOFILE -package=ethereum -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package ethereum

import (
	"math/big"
	"reflect"
	"testing"

	"github.com/cosmos/cosmos-sdk/codec"
	codectypes "github.com/cosmos/cosmos-sdk/codec/types"
	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/client"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/contract/ibchandler"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/google/go-cmp/cmp"
)

func TestNewMsgID(t *testing.T) {
	type args struct {
		txHash common.Hash
	}
	tests := []struct {
		name string
		args args
		want *MsgID
	}{
		{
			name: "should return TxHashHex.",
			args: args{
				txHash: common.Hash{},
			},
			want: &MsgID{
				TxHashHex: "0x0000000000000000000000000000000000000000000000000000000000000000",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewMsgID(tt.args.txHash); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewMsgID() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMsgID_Is_MsgID(t *testing.T) {
	type fields struct {
		TxHashHex string
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "should set TxHashHex.",
			fields: fields{
				TxHashHex: "47173285a8d7340d9b9f293cd76500e9f5e16f260c377cce805d905f2a1f6d7f",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &MsgID{
				TxHashHex: tt.fields.TxHashHex,
			}
			m.Is_MsgID()
		})
	}
}

func TestMsgID_TxHash(t *testing.T) {
	type fields struct {
		TxHashHex string
	}
	tests := []struct {
		name   string
		fields fields
		want   common.Hash
	}{
		{
			name: "should return TxHashHex.",
			fields: fields{
				TxHashHex: "47173285a8d7340d9b9f293cd76500e9f5e16f260c377cce805d905f2a1f6d7f",
			},
			want: common.HexToHash("47173285a8d7340d9b9f293cd76500e9f5e16f260c377cce805d905f2a1f6d7f"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			id := &MsgID{
				TxHashHex: tt.fields.TxHashHex,
			}
			if got := id.TxHash(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MsgID.TxHash() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("MsgID.TxHash() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

func TestMsgResult_BlockHeight(t *testing.T) {
	type fields struct {
		height       clienttypes.Height
		status       bool
		revertReason string
		events       []core.MsgEventLog
	}
	tests := []struct {
		name   string
		fields fields
		want   clienttypes.Height
	}{
		{
			name: "should return BlockHeight.",
			fields: fields{
				height: clienttypes.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				status:       false,
				revertReason: "",
				events:       nil,
			},
			want: clienttypes.Height{
				RevisionNumber: 0,
				RevisionHeight: 100,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &MsgResult{
				height:       tt.fields.height,
				status:       tt.fields.status,
				revertReason: tt.fields.revertReason,
				events:       tt.fields.events,
			}
			if got := r.BlockHeight(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MsgResult.BlockHeight() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMsgResult_Status(t *testing.T) {
	type fields struct {
		height       clienttypes.Height
		status       bool
		revertReason string
		events       []core.MsgEventLog
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
		want1  string
	}{
		{
			name: "should return status and revertReason.",
			fields: fields{
				height: clienttypes.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				status:       false,
				revertReason: "Not enough Ether provided",
				events:       nil,
			},
			want:  false,
			want1: "Not enough Ether provided",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &MsgResult{
				height:       tt.fields.height,
				status:       tt.fields.status,
				revertReason: tt.fields.revertReason,
				events:       tt.fields.events,
			}
			got, got1 := r.Status()
			if got != tt.want {
				t.Errorf("MsgResult.Status() got = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("MsgResult.Status() differs: (-got +want)n%s", diff)
				}
			}
			if got1 != tt.want1 {
				t.Errorf("MsgResult.Status() got1 = %v, want %v", got1, tt.want1)
				if diff := cmp.Diff(got1, tt.want1); diff != "" {
					t.Errorf("MsgResult.Status() differs: (-got1 +want1)n%s", diff)
				}
			}
		})
	}
}

func TestMsgResult_Events(t *testing.T) {
	type fields struct {
		height       clienttypes.Height
		status       bool
		revertReason string
		events       []core.MsgEventLog
	}
	tests := []struct {
		name   string
		fields fields
		want   []core.MsgEventLog
	}{
		{
			name: "should return events.",
			fields: fields{
				height: clienttypes.Height{
					RevisionNumber: 0,
					RevisionHeight: 0,
				},
				status:       false,
				revertReason: "",
				events:       []core.MsgEventLog{},
			},
			want: []core.MsgEventLog{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &MsgResult{
				height:       tt.fields.height,
				status:       tt.fields.status,
				revertReason: tt.fields.revertReason,
				events:       tt.fields.events,
			}
			if got := r.Events(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MsgResult.Events() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChain_makeMsgResultFromReceipt(t *testing.T) {
	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		receipt      *types.Receipt
		revertReason string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *MsgResult
		wantErr bool
	}{
		{
			name: "should error log has no topic.",
			fields: fields{
				config: ChainConfig{
					ChainId:    "********",
					EthChainId: 5151,
					RpcAddr:    "http://**********:8451",
					Signer: &codectypes.Any{
						TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					IbcAddress:            "******************************************",
					InitialSendCheckpoint: 1,
					InitialRecvCheckpoint: 1,
					EnableDebugTrace:      false,
					AverageBlockTimeMsec:  2000,
					MaxRetryForInclusion:  3,
					GasEstimateRate: &Fraction{
						Numerator:   1,
						Denominator: 1,
					},
					MaxGasLimit: *********,
					TxType:      "auto",
				},
				pathEnd: &core.PathEnd{
					ChainID:      "********",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-1",
					PortID:       "balance-sync",
					Order:        "unordered",
					Version:      "balance-sync-1",
				},
				homePath:         ".",
				chainID:          &big.Int{},
				codec:            core.MakeCodec(),
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				receipt: &types.Receipt{
					Type:              0,
					PostState:         nil,
					Status:            0,
					CumulativeGasUsed: 0,
					Bloom:             types.Bloom{},
					Logs: []*types.Log{
						{},
						{},
						{},
					},
					TxHash:            common.Hash{},
					ContractAddress:   common.Address{},
					GasUsed:           0,
					EffectiveGasPrice: &big.Int{},
					BlockHash:         common.Hash{},
					BlockNumber:       big.NewInt(100),
					TransactionIndex:  0,
				},
				revertReason: "Not enough Ether provided",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return MsgResult",
			fields: fields{
				config: ChainConfig{
					ChainId:    "********",
					EthChainId: 5151,
					RpcAddr:    "http://**********:8451",
					Signer: &codectypes.Any{
						TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					IbcAddress:            "******************************************",
					InitialSendCheckpoint: 1,
					InitialRecvCheckpoint: 1,
					EnableDebugTrace:      false,
					AverageBlockTimeMsec:  2000,
					MaxRetryForInclusion:  3,
					GasEstimateRate: &Fraction{
						Numerator:   1,
						Denominator: 1,
					},
					MaxGasLimit: *********,
					TxType:      "auto",
				},
				pathEnd: &core.PathEnd{
					ChainID:      "********",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-1",
					PortID:       "balance-sync",
					Order:        "unordered",
					Version:      "balance-sync-1",
				},
				homePath:         ".",
				chainID:          &big.Int{},
				codec:            core.MakeCodec(),
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				receipt: &types.Receipt{
					Type:              0,
					PostState:         nil,
					Status:            0,
					CumulativeGasUsed: 0,
					Bloom:             types.Bloom{},
					Logs:              []*types.Log{},
					TxHash:            common.Hash{},
					ContractAddress:   common.Address{},
					GasUsed:           0,
					EffectiveGasPrice: &big.Int{},
					BlockHash:         common.Hash{},
					BlockNumber:       big.NewInt(100),
					TransactionIndex:  0,
				},
				revertReason: "Not enough Ether provided",
			},
			want: &MsgResult{
				height: clienttypes.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				status:       false,
				revertReason: "Not enough Ether provided",
				events:       nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.makeMsgResultFromReceipt(tt.args.receipt, tt.args.revertReason)
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.makeMsgResultFromReceipt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.makeMsgResultFromReceipt() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Chain.makeMsgResultFromReceipt() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

func TestChain_parseMsgEventLogs(t *testing.T) {

	stubChainConfig := ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	stubPathEnd := &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		logs []*types.Log
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []core.MsgEventLog
		wantErr bool
	}{
		{
			name: "Returns nill because the log does not exist.",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         "",
				chainID:          &big.Int{},
				codec:            core.MakeCodec(),
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				logs: []*types.Log{},
			},
			want:    nil,
			wantErr: false,
		},

		//// TODO ibchandler が init されておらず ABIのイベント名がマッチングできないのでテスト不可
		//{
		//	name: "Returns GeneratedClientIdentifier event log.",
		//	fields: fields{
		//		config:           stubChainConfig,
		//		pathEnd:          stubPathEnd,
		//		homePath:         ".relayer",
		//		chainID:          big.NewInt(5151),
		//		codec:            core.MakeCodec(),
		//		msgEventListener: nil,
		//		client: &client.ETHClient{
		//			Client: &ethclient.Client{},
		//		},
		//		ibcHandler: &ibchandler.Ibchandler{
		//			IbchandlerCaller:     ibchandler.IbchandlerCaller{},
		//			IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
		//			IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
		//		},
		//		signer:                    nil,
		//		connectionOpenedConfirmed: false,
		//	},
		//	args: args{
		//		logs: []*types.Log{
		//			{
		//				Address: common.HexToAddress("47173285a8d7340d9b9f293cd76500e9f5e16f260c377cce805d905f2a1f6d7f"),
		//				Topics: []common.Hash{
		//					common.HexToHash(hex.EncodeToString([]byte(string("GeneratedClientIdentifier")))),
		//				},
		//				Data:        []byte{},
		//				BlockNumber: 0,
		//				TxHash:      common.Hash{},
		//				TxIndex:     0,
		//				BlockHash:   common.Hash{},
		//				Index:       0,
		//				Removed:     false,
		//			},
		//		},
		//	},
		//	want:    []core.MsgEventLog{},
		//	wantErr: false,
		//},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.parseMsgEventLogs(tt.args.logs)
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.parseMsgEventLogs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.parseMsgEventLogs() = %v, want %v", got, tt.want)
			}
		})
	}
}

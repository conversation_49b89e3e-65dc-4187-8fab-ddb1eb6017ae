// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: relayer/chains/ethereum/msgid/msgid.proto

package ethereum

import (
	fmt "fmt"
	_ "github.com/cosmos/gogoproto/gogoproto"
	proto "github.com/cosmos/gogoproto/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type MsgID struct {
	TxHashHex string `protobuf:"bytes,1,opt,name=tx_hash_hex,json=txHashHex,proto3" json:"tx_hash_hex,omitempty"`
}

func (m *MsgID) Reset()         { *m = MsgID{} }
func (m *MsgID) String() string { return proto.CompactTextString(m) }
func (*MsgID) ProtoMessage()    {}
func (*MsgID) Descriptor() ([]byte, []int) {
	return fileDescriptor_79397c77b93ee9e3, []int{0}
}
func (m *MsgID) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MsgID) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MsgID.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MsgID) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MsgID.Merge(m, src)
}
func (m *MsgID) XXX_Size() int {
	return m.Size()
}
func (m *MsgID) XXX_DiscardUnknown() {
	xxx_messageInfo_MsgID.DiscardUnknown(m)
}

var xxx_messageInfo_MsgID proto.InternalMessageInfo

func init() {
	proto.RegisterType((*MsgID)(nil), "relayer.chains.ethereum.msgid.MsgID")
}

func init() {
	proto.RegisterFile("relayer/chains/ethereum/msgid/msgid.proto", fileDescriptor_79397c77b93ee9e3)
}

var fileDescriptor_79397c77b93ee9e3 = []byte{
	// 214 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xd2, 0x2c, 0x4a, 0xcd, 0x49,
	0xac, 0x4c, 0x2d, 0xd2, 0x4f, 0xce, 0x48, 0xcc, 0xcc, 0x2b, 0xd6, 0x4f, 0x2d, 0xc9, 0x48, 0x2d,
	0x4a, 0x2d, 0xcd, 0xd5, 0xcf, 0x2d, 0x4e, 0xcf, 0x4c, 0x81, 0x90, 0x7a, 0x05, 0x45, 0xf9, 0x25,
	0xf9, 0x42, 0xb2, 0x50, 0xa5, 0x7a, 0x10, 0xa5, 0x7a, 0x30, 0xa5, 0x7a, 0x60, 0x45, 0x52, 0x22,
	0xe9, 0xf9, 0xe9, 0xf9, 0x60, 0x95, 0xfa, 0x20, 0x16, 0x44, 0x93, 0x92, 0x3a, 0x17, 0xab, 0x6f,
	0x71, 0xba, 0xa7, 0x8b, 0x90, 0x1c, 0x17, 0x77, 0x49, 0x45, 0x7c, 0x46, 0x62, 0x71, 0x46, 0x7c,
	0x46, 0x6a, 0x85, 0x04, 0xa3, 0x02, 0xa3, 0x06, 0x67, 0x10, 0x67, 0x49, 0x85, 0x47, 0x62, 0x71,
	0x86, 0x47, 0x6a, 0x85, 0x53, 0xf4, 0x89, 0x87, 0x72, 0x0c, 0x27, 0x1e, 0xc9, 0x31, 0x5e, 0x78,
	0x24, 0xc7, 0xf8, 0xe0, 0x91, 0x1c, 0xe3, 0x84, 0xc7, 0x72, 0x0c, 0x17, 0x1e, 0xcb, 0x31, 0xdc,
	0x78, 0x2c, 0xc7, 0x10, 0x65, 0x9b, 0x9e, 0x59, 0x92, 0x51, 0x9a, 0xa4, 0x97, 0x9c, 0x9f, 0xab,
	0x9f, 0x92, 0x9a, 0x5c, 0x5a, 0x54, 0x94, 0x5a, 0xa2, 0x9b, 0x93, 0x98, 0xa4, 0x9f, 0x92, 0x9c,
	0x94, 0xae, 0x9b, 0x92, 0x9c, 0x55, 0x50, 0xa9, 0x0b, 0xf3, 0x49, 0x41, 0x76, 0xba, 0x3e, 0x98,
	0x0d, 0xf7, 0x4c, 0x12, 0x1b, 0xd8, 0x31, 0xc6, 0x80, 0x00, 0x00, 0x00, 0xff, 0xff, 0xa8, 0x12,
	0x84, 0x6f, 0xee, 0x00, 0x00, 0x00,
}

func (m *MsgID) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MsgID) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MsgID) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TxHashHex) > 0 {
		i -= len(m.TxHashHex)
		copy(dAtA[i:], m.TxHashHex)
		i = encodeVarintMsgid(dAtA, i, uint64(len(m.TxHashHex)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintMsgid(dAtA []byte, offset int, v uint64) int {
	offset -= sovMsgid(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *MsgID) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TxHashHex)
	if l > 0 {
		n += 1 + l + sovMsgid(uint64(l))
	}
	return n
}

func sovMsgid(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozMsgid(x uint64) (n int) {
	return sovMsgid(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *MsgID) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMsgid
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MsgID: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MsgID: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TxHashHex", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMsgid
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMsgid
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthMsgid
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TxHashHex = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMsgid(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMsgid
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipMsgid(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowMsgid
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMsgid
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMsgid
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthMsgid
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupMsgid
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthMsgid
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthMsgid        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowMsgid          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupMsgid = fmt.Errorf("proto: unexpected end of group")
)

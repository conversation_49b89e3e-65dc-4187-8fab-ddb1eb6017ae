//go:generate mockgen -source=$GOFILE -package=ethereum -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package ethereum

import (
	"context"
	"math/big"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/cosmos/cosmos-sdk/codec"
	codectypes "github.com/cosmos/cosmos-sdk/codec/types"
	sdk "github.com/cosmos/cosmos-sdk/types"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/log"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/client"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/contract/ibchandler"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/google/go-cmp/cmp"
	"go.uber.org/mock/gomock"
)

func TestMain(m *testing.M) {
	log.InitLogger("Debug", "text", os.Stdout)
	exitVal := m.Run()
	os.Exit(exitVal)
}

// TODO protobuf 構造により SignerConfig が取得できないためテスト不可
//func TestNewChain(t *testing.T) {
//
//	stubChainConfig := ChainConfig{
//		ChainId:    "********",
//		EthChainId: 5151,
//		RpcAddr:    "http://**********:8451",
//		Signer: &codectypes.Any{
//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//			Value:                nil,
//			XXX_NoUnkeyedLiteral: struct{}{},
//			XXX_unrecognized:     nil,
//			XXX_sizecache:        0,
//		},
//		IbcAddress:            "******************************************",
//		InitialSendCheckpoint: 1,
//		InitialRecvCheckpoint: 1,
//		EnableDebugTrace:      false,
//		AverageBlockTimeMsec:  2000,
//		MaxRetryForInclusion:  3,
//		GasEstimateRate: &Fraction{
//			Numerator:   1,
//			Denominator: 1,
//		},
//		MaxGasLimit: *********,
//		TxType:      "auto",
//	}
//	stubPathEnd := &core.PathEnd{
//		ChainID:      "********",
//		ClientID:     "hb-ibft2-0",
//		ConnectionID: "connection-0",
//		ChannelID:    "channel-0",
//		PortID:       "account-sync",
//		Order:        "unordered",
//		Version:      "account-sync-1",
//	}
//
//	type args struct {
//		config ChainConfig
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    *Chain
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//		{
//			name: "",
//			args: args{
//				config: stubChainConfig,
//			},
//			want: &Chain{
//				config:           stubChainConfig,
//				pathEnd:          stubPathEnd,
//				homePath:         ".relayer",
//				chainID:          &big.Int{},
//				codec:            core.MakeCodec(),
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := NewChain(tt.args.config)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("NewChain() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("NewChain() = %v, want %v", got, tt.want)
//				if diff := cmp.Diff(got, tt.want); diff != "" {
//					t.Errorf("NewChain() differs: (-got +want)n%s", diff)
//				}
//			}
//		})
//	}
//}

func TestChain_Config(t *testing.T) {
	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	tests := []struct {
		name   string
		fields fields
		want   ChainConfig
	}{
		// TODO: Add test cases.
		{
			name: "",
			fields: fields{
				config: ChainConfig{
					ChainId:    "",
					EthChainId: 0,
					RpcAddr:    "",
					Signer: &codectypes.Any{
						TypeUrl:              "",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					IbcAddress:            "",
					InitialSendCheckpoint: 0,
					InitialRecvCheckpoint: 0,
					EnableDebugTrace:      false,
					AverageBlockTimeMsec:  0,
					MaxRetryForInclusion:  0,
					GasEstimateRate: &Fraction{
						Numerator:   0,
						Denominator: 0,
					},
					MaxGasLimit: 0,
					TxType:      "",
				},
				pathEnd: &core.PathEnd{
					ChainID:      "",
					ClientID:     "",
					ConnectionID: "",
					ChannelID:    "",
					PortID:       "",
					Order:        "",
					Version:      "",
				},
				homePath:         "",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			want: ChainConfig{
				ChainId:    "",
				EthChainId: 0,
				RpcAddr:    "",
				Signer: &codectypes.Any{
					TypeUrl:              "",
					Value:                nil,
					XXX_NoUnkeyedLiteral: struct{}{},
					XXX_unrecognized:     nil,
					XXX_sizecache:        0,
				},
				IbcAddress:            "",
				InitialSendCheckpoint: 0,
				InitialRecvCheckpoint: 0,
				EnableDebugTrace:      false,
				AverageBlockTimeMsec:  0,
				MaxRetryForInclusion:  0,
				GasEstimateRate: &Fraction{
					Numerator:   0,
					Denominator: 0,
				},
				MaxGasLimit: 0,
				TxType:      "",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			if got := c.Config(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.Config() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChain_Init(t *testing.T) {

	stubChainConfig := ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	stubPathEnd := &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		homePath string
		timeout  time.Duration
		codec    codec.ProtoCodecMarshaler
		debug    bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "should init chain.",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            core.MakeCodec(),
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				homePath: ".relayer",
				timeout:  10,
				codec:    core.MakeCodec(),
				debug:    false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			if err := c.Init(tt.args.homePath, tt.args.timeout, tt.args.codec, tt.args.debug); (err != nil) != tt.wantErr {
				t.Errorf("Chain.Init() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestChain_SetupForRelay(t *testing.T) {

	stubChainConfig := ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	stubPathEnd := &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "should set context.",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				ctx: context.TODO(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			if err := c.SetupForRelay(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("Chain.SetupForRelay() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestChain_ChainID(t *testing.T) {

	stubChainConfig := ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	stubPathEnd := &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "should get chain id from pathEnd",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            core.MakeCodec(),
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			want: "********",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			if got := c.ChainID(); got != tt.want {
				t.Errorf("Chain.ChainID() = %v, want %v", got, tt.want)
			}
		})
	}
}

//// TODO BlockNumber 取得時に実際にアクセスされてしまうためテスト不可
//func TestChain_LatestHeight(t *testing.T) {
//
//	stubChainConfig := ChainConfig{
//		ChainId:    "********",
//		EthChainId: 5151,
//		RpcAddr:    "http://**********:8451",
//		Signer: &codectypes.Any{
//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//			Value:                nil,
//			XXX_NoUnkeyedLiteral: struct{}{},
//			XXX_unrecognized:     nil,
//			XXX_sizecache:        0,
//		},
//		IbcAddress:            "******************************************",
//		InitialSendCheckpoint: 1,
//		InitialRecvCheckpoint: 1,
//		EnableDebugTrace:      false,
//		AverageBlockTimeMsec:  2000,
//		MaxRetryForInclusion:  3,
//		GasEstimateRate: &Fraction{
//			Numerator:   1,
//			Denominator: 1,
//		},
//		MaxGasLimit: *********,
//		TxType:      "auto",
//	}
//	stubPathEnd := &core.PathEnd{
//		ChainID:      "********",
//		ClientID:     "hb-ibft2-0",
//		ConnectionID: "connection-0",
//		ChannelID:    "channel-0",
//		PortID:       "account-sync",
//		Order:        "unordered",
//		Version:      "account-sync-1",
//	}
//
//	type fields struct {
//		config                    ChainConfig
//		pathEnd                   *core.PathEnd
//		homePath                  string
//		chainID                   *big.Int
//		codec                     codec.ProtoCodecMarshaler
//		msgEventListener          core.MsgEventListener
//		client                    *client.ETHClient
//		ibcHandler                *ibchandler.Ibchandler
//		signer                    Signer
//		connectionOpenedConfirmed bool
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		want    ibcexported.Height
//		wantErr bool
//	}{
//		{
//			name: "should get latest height from pathEnd",
//			fields: fields{
//				config:           stubChainConfig,
//				pathEnd:          stubPathEnd,
//				homePath:         ".relayer",
//				chainID:          &big.Int{},
//				codec:            core.MakeCodec(),
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			want:    nil,
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			c := &Chain{
//				config:                    tt.fields.config,
//				pathEnd:                   tt.fields.pathEnd,
//				homePath:                  tt.fields.homePath,
//				chainID:                   tt.fields.chainID,
//				codec:                     tt.fields.codec,
//				msgEventListener:          tt.fields.msgEventListener,
//				client:                    tt.fields.client,
//				ibcHandler:                tt.fields.ibcHandler,
//				signer:                    tt.fields.signer,
//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
//			}
//			got, err := c.LatestHeight()
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Chain.LatestHeight() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("Chain.LatestHeight() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
//// TODO Timestamp 取得時に実際にアクセスされてしまうためテスト不可
//func TestChain_Timestamp(t *testing.T) {
//
//	stubChainConfig := ChainConfig{
//		ChainId:    "********",
//		EthChainId: 5151,
//		RpcAddr:    "http://**********:8451",
//		Signer: &codectypes.Any{
//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//			Value:                nil,
//			XXX_NoUnkeyedLiteral: struct{}{},
//			XXX_unrecognized:     nil,
//			XXX_sizecache:        0,
//		},
//		IbcAddress:            "******************************************",
//		InitialSendCheckpoint: 1,
//		InitialRecvCheckpoint: 1,
//		EnableDebugTrace:      false,
//		AverageBlockTimeMsec:  2000,
//		MaxRetryForInclusion:  3,
//		GasEstimateRate: &Fraction{
//			Numerator:   1,
//			Denominator: 1,
//		},
//		MaxGasLimit: *********,
//		TxType:      "auto",
//	}
//	stubPathEnd := &core.PathEnd{
//		ChainID:      "********",
//		ClientID:     "hb-ibft2-0",
//		ConnectionID: "connection-0",
//		ChannelID:    "channel-0",
//		PortID:       "account-sync",
//		Order:        "unordered",
//		Version:      "account-sync-1",
//	}
//
//	type fields struct {
//		config                    ChainConfig
//		pathEnd                   *core.PathEnd
//		homePath                  string
//		chainID                   *big.Int
//		codec                     codec.ProtoCodecMarshaler
//		msgEventListener          core.MsgEventListener
//		client                    *client.ETHClient
//		ibcHandler                *ibchandler.Ibchandler
//		signer                    Signer
//		connectionOpenedConfirmed bool
//	}
//	type args struct {
//		height ibcexported.Height
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    time.Time
//		wantErr bool
//	}{
//		{
//			name: "",
//			fields: fields{
//				config:           stubChainConfig,
//				pathEnd:          stubPathEnd,
//				homePath:         ".relayer",
//				chainID:          &big.Int{},
//				codec:            core.MakeCodec(),
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			args: args{
//				height: nil,
//			},
//			want:    time.Time{},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			c := &Chain{
//				config:                    tt.fields.config,
//				pathEnd:                   tt.fields.pathEnd,
//				homePath:                  tt.fields.homePath,
//				chainID:                   tt.fields.chainID,
//				codec:                     tt.fields.codec,
//				msgEventListener:          tt.fields.msgEventListener,
//				client:                    tt.fields.client,
//				ibcHandler:                tt.fields.ibcHandler,
//				signer:                    tt.fields.signer,
//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
//			}
//			got, err := c.Timestamp(tt.args.height)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Chain.Timestamp() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("Chain.Timestamp() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func TestChain_AverageBlockTime(t *testing.T) {

	stubChainConfig := ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	stubPathEnd := &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	tests := []struct {
		name   string
		fields fields
		want   time.Duration
	}{
		{
			name: "should return average block time",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			want: time.Duration(2000) * time.Millisecond,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			if got := c.AverageBlockTime(); got != tt.want {
				t.Errorf("Chain.AverageBlockTime() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Chain.AverageBlockTime() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

func TestChain_GetAddress(t *testing.T) {

	stubChainConfig := ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	stubPathEnd := &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	tests := []struct {
		name    string
		fields  fields
		want    sdk.AccAddress
		wantErr bool
	}{
		{
			name: "should returns the address of relayer",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            core.MakeCodec(),
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			want:    make([]byte, 20),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.GetAddress()
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.GetAddress() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.GetAddress() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Chain.GetAddress() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

func TestChain_Codec(t *testing.T) {

	stubChainConfig := ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	stubPathEnd := &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	tests := []struct {
		name   string
		fields fields
		want   codec.ProtoCodecMarshaler
	}{
		{
			name: "should returns the marshaler",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            core.MakeCodec(),
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			want: core.MakeCodec(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			if got := c.Codec(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.Codec() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChain_Client(t *testing.T) {

	stubChainConfig := ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	stubPathEnd := &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	tests := []struct {
		name   string
		fields fields
		want   *client.ETHClient
	}{
		{
			name: "should returns the RPC client for ethereum",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            core.MakeCodec(),
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			want: &client.ETHClient{
				Client: &ethclient.Client{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			if got := c.Client(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.Client() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChain_SetRelayInfo(t *testing.T) {

	ctrl := gomock.NewController(t)

	// create mock instances
	srcMockChain := core.NewMockChain(ctrl)
	mockProver := core.NewMockProver(ctrl)

	stubChainConfig := ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	stubPathEnd := &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		p   *core.PathEnd
		in1 *core.ProvableChain
		in2 *core.PathEnd
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "should invalid path error.",
			fields: fields{
				config:  stubChainConfig,
				pathEnd: stubPathEnd,
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				p: &core.PathEnd{
					ChainID:      "********",
					ClientID:     "",
					ConnectionID: "",
					ChannelID:    "",
					PortID:       "",
					Order:        "",
					Version:      "",
				},
				in1: &core.ProvableChain{
					Chain:  srcMockChain,
					Prover: mockProver,
				},
				in2: &core.PathEnd{
					ChainID:      "********",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
			},
			wantErr: true,
		},
		{
			name: "should sets source's path and counterparty's info to the chain.",
			fields: fields{
				config:  stubChainConfig,
				pathEnd: stubPathEnd,
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				p: &core.PathEnd{
					ChainID:      "********",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				in1: &core.ProvableChain{
					Chain:  srcMockChain,
					Prover: mockProver,
				},
				in2: &core.PathEnd{
					ChainID:      "********",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			if err := c.SetRelayInfo(tt.args.p, tt.args.in1, tt.args.in2); (err != nil) != tt.wantErr {
				t.Errorf("Chain.SetRelayInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestChain_Path(t *testing.T) {

	stubChainConfig := ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	stubPathEnd := &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	tests := []struct {
		name   string
		fields fields
		want   *core.PathEnd
	}{
		{
			name: "should return chain pathEnd.",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			want: stubPathEnd,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			if got := c.Path(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.Path() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Chain.Path() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

func TestChain_RegisterMsgEventListener(t *testing.T) {

	stubChainConfig := ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	stubPathEnd := &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		listener core.MsgEventListener
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				// TODO core.MsgEventListener
				listener: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			c.RegisterMsgEventListener(tt.args.listener)
		})
	}
}

//// TODO ibcHandler が ConsensusState を取得しに行くのでテスト不可
//func TestChain_QueryClientConsensusState(t *testing.T) {
//
//	stubChainConfig := ChainConfig{
//		ChainId:    "********",
//		EthChainId: 5151,
//		RpcAddr:    "http://**********:8451",
//		Signer: &codectypes.Any{
//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//			Value:                nil,
//			XXX_NoUnkeyedLiteral: struct{}{},
//			XXX_unrecognized:     nil,
//			XXX_sizecache:        0,
//		},
//		IbcAddress:            "******************************************",
//		InitialSendCheckpoint: 1,
//		InitialRecvCheckpoint: 1,
//		EnableDebugTrace:      false,
//		AverageBlockTimeMsec:  2000,
//		MaxRetryForInclusion:  3,
//		GasEstimateRate: &Fraction{
//			Numerator:   1,
//			Denominator: 1,
//		},
//		MaxGasLimit: *********,
//		TxType:      "auto",
//	}
//	stubPathEnd := &core.PathEnd{
//		ChainID:      "********",
//		ClientID:     "hb-ibft2-0",
//		ConnectionID: "connection-0",
//		ChannelID:    "channel-0",
//		PortID:       "account-sync",
//		Order:        "unordered",
//		Version:      "account-sync-1",
//	}
//	stubHeight := clienttypes.Height{
//		RevisionNumber: 0,
//		RevisionHeight: 100,
//	}
//
//	ctrl := gomock.NewController(t)
//	mockQueryContext := core.NewMockQueryContext(ctrl)
//	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
//	mockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
//
//	type fields struct {
//		config                    ChainConfig
//		pathEnd                   *core.PathEnd
//		homePath                  string
//		chainID                   *big.Int
//		codec                     codec.ProtoCodecMarshaler
//		msgEventListener          core.MsgEventListener
//		client                    *client.ETHClient
//		ibcHandler                *ibchandler.Ibchandler
//		signer                    Signer
//		connectionOpenedConfirmed bool
//	}
//	type args struct {
//		ctx                 core.QueryContext
//		dstClientConsHeight ibcexported.Height
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *clienttypes.QueryConsensusStateResponse
//		wantErr bool
//	}{
//		{
//			name: "should retrevies the latest consensus state for a client in state at a given height.",
//			fields: fields{
//				config:           stubChainConfig,
//				pathEnd:          stubPathEnd,
//				homePath:         ".relayer",
//				chainID:          &big.Int{},
//				codec:            core.MakeCodec(),
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			args: args{
//				ctx:                 mockQueryContext,
//				dstClientConsHeight: stubHeight,
//			},
//			want: &clienttypes.QueryConsensusStateResponse{
//				ConsensusState: &codectypes.Any{
//					TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//					Value:                nil,
//					XXX_NoUnkeyedLiteral: struct{}{},
//					XXX_unrecognized:     nil,
//					XXX_sizecache:        0,
//				},
//				Proof:       nil,
//				ProofHeight: stubHeight,
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			c := &Chain{
//				config:                    tt.fields.config,
//				pathEnd:                   tt.fields.pathEnd,
//				homePath:                  tt.fields.homePath,
//				chainID:                   tt.fields.chainID,
//				codec:                     tt.fields.codec,
//				msgEventListener:          tt.fields.msgEventListener,
//				client:                    tt.fields.client,
//				ibcHandler:                tt.fields.ibcHandler,
//				signer:                    tt.fields.signer,
//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
//			}
//			got, err := c.QueryClientConsensusState(tt.args.ctx, tt.args.dstClientConsHeight)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Chain.QueryClientConsensusState() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("Chain.QueryClientConsensusState() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
//// TODO ibcHandler が ClientState を取得しに行くのでテスト不可
//func TestChain_QueryClientState(t *testing.T) {
//
//	stubChainConfig := ChainConfig{
//		ChainId:    "********",
//		EthChainId: 5151,
//		RpcAddr:    "http://**********:8451",
//		Signer: &codectypes.Any{
//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//			Value:                nil,
//			XXX_NoUnkeyedLiteral: struct{}{},
//			XXX_unrecognized:     nil,
//			XXX_sizecache:        0,
//		},
//		IbcAddress:            "******************************************",
//		InitialSendCheckpoint: 1,
//		InitialRecvCheckpoint: 1,
//		EnableDebugTrace:      false,
//		AverageBlockTimeMsec:  2000,
//		MaxRetryForInclusion:  3,
//		GasEstimateRate: &Fraction{
//			Numerator:   1,
//			Denominator: 1,
//		},
//		MaxGasLimit: *********,
//		TxType:      "auto",
//	}
//	stubPathEnd := &core.PathEnd{
//		ChainID:      "********",
//		ClientID:     "hb-ibft2-0",
//		ConnectionID: "connection-0",
//		ChannelID:    "channel-0",
//		PortID:       "account-sync",
//		Order:        "unordered",
//		Version:      "account-sync-1",
//	}
//	stubHeight := clienttypes.Height{
//		RevisionNumber: 0,
//		RevisionHeight: 100,
//	}
//
//	ctrl := gomock.NewController(t)
//	mockQueryContext := core.NewMockQueryContext(ctrl)
//	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
//	mockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
//
//	type fields struct {
//		config                    ChainConfig
//		pathEnd                   *core.PathEnd
//		homePath                  string
//		chainID                   *big.Int
//		codec                     codec.ProtoCodecMarshaler
//		msgEventListener          core.MsgEventListener
//		client                    *client.ETHClient
//		ibcHandler                *ibchandler.Ibchandler
//		signer                    Signer
//		connectionOpenedConfirmed bool
//	}
//	type args struct {
//		ctx core.QueryContext
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *clienttypes.QueryClientStateResponse
//		wantErr bool
//	}{
//		{
//			name: "should returns the client state of dst chain height represents the height of dst chain.",
//			fields: fields{
//				config:           stubChainConfig,
//				pathEnd:          stubPathEnd,
//				homePath:         ".relayer",
//				chainID:          &big.Int{},
//				codec:            core.MakeCodec(),
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			args: args{
//				ctx: mockQueryContext,
//			},
//			want: &clienttypes.QueryClientStateResponse{
//				ClientState: &codectypes.Any{
//					TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//					Value:                nil,
//					XXX_NoUnkeyedLiteral: struct{}{},
//					XXX_unrecognized:     nil,
//					XXX_sizecache:        0,
//				},
//				Proof: nil,
//				ProofHeight: clienttypes.Height{
//					RevisionNumber: 0,
//					RevisionHeight: 100,
//				},
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			c := &Chain{
//				config:                    tt.fields.config,
//				pathEnd:                   tt.fields.pathEnd,
//				homePath:                  tt.fields.homePath,
//				chainID:                   tt.fields.chainID,
//				codec:                     tt.fields.codec,
//				msgEventListener:          tt.fields.msgEventListener,
//				client:                    tt.fields.client,
//				ibcHandler:                tt.fields.ibcHandler,
//				signer:                    tt.fields.signer,
//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
//			}
//			got, err := c.QueryClientState(tt.args.ctx)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Chain.QueryClientState() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("Chain.QueryClientState() = %v, want %v", got, tt.want)
//				if diff := cmp.Diff(got, tt.want); diff != "" {
//					t.Errorf("Chain.QueryClientState() differs: (-got +want)n%s", diff)
//				}
//			}
//		})
//	}
//}
//
//// TODO ibcHandler が Connection を取得しに行くのでテスト不可
//func TestChain_QueryConnection(t *testing.T) {
//
//	stubChainConfig := ChainConfig{
//		ChainId:    "********",
//		EthChainId: 5151,
//		RpcAddr:    "http://**********:8451",
//		Signer: &codectypes.Any{
//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//			Value:                nil,
//			XXX_NoUnkeyedLiteral: struct{}{},
//			XXX_unrecognized:     nil,
//			XXX_sizecache:        0,
//		},
//		IbcAddress:            "******************************************",
//		InitialSendCheckpoint: 1,
//		InitialRecvCheckpoint: 1,
//		EnableDebugTrace:      false,
//		AverageBlockTimeMsec:  2000,
//		MaxRetryForInclusion:  3,
//		GasEstimateRate: &Fraction{
//			Numerator:   1,
//			Denominator: 1,
//		},
//		MaxGasLimit: *********,
//		TxType:      "auto",
//	}
//	stubPathEnd := &core.PathEnd{
//		ChainID:      "********",
//		ClientID:     "hb-ibft2-0",
//		ConnectionID: "connection-0",
//		ChannelID:    "channel-0",
//		PortID:       "account-sync",
//		Order:        "unordered",
//		Version:      "account-sync-1",
//	}
//	stubHeight := clienttypes.Height{
//		RevisionNumber: 0,
//		RevisionHeight: 100,
//	}
//
//	ctrl := gomock.NewController(t)
//	mockQueryContext := core.NewMockQueryContext(ctrl)
//	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
//	mockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
//
//	type fields struct {
//		config                    ChainConfig
//		pathEnd                   *core.PathEnd
//		homePath                  string
//		chainID                   *big.Int
//		codec                     codec.ProtoCodecMarshaler
//		msgEventListener          core.MsgEventListener
//		client                    *client.ETHClient
//		ibcHandler                *ibchandler.Ibchandler
//		signer                    Signer
//		connectionOpenedConfirmed bool
//	}
//	type args struct {
//		ctx core.QueryContext
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *conntypes.QueryConnectionResponse
//		wantErr bool
//	}{
//		{
//			name: "should returns the remote end of a given connection",
//			fields: fields{
//				config:           stubChainConfig,
//				pathEnd:          stubPathEnd,
//				homePath:         ".relayer",
//				chainID:          &big.Int{},
//				codec:            core.MakeCodec(),
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			args: args{
//				ctx: mockQueryContext,
//			},
//			want: &conntypes.QueryConnectionResponse{
//				Connection: &conntypes.ConnectionEnd{
//					ClientId: "hb-ibft2-0",
//					Versions: nil,
//					State:    0,
//					Counterparty: conntypes.Counterparty{
//						ClientId:     "hb-ibft2-0",
//						ConnectionId: "connection-0",
//						Prefix: commitmenttypes.MerklePrefix{
//							KeyPrefix: nil,
//						},
//					},
//					DelayPeriod: 0,
//				},
//				Proof: nil,
//				ProofHeight: clienttypes.Height{
//					RevisionNumber: 0,
//					RevisionHeight: 100,
//				},
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			c := &Chain{
//				config:                    tt.fields.config,
//				pathEnd:                   tt.fields.pathEnd,
//				homePath:                  tt.fields.homePath,
//				chainID:                   tt.fields.chainID,
//				codec:                     tt.fields.codec,
//				msgEventListener:          tt.fields.msgEventListener,
//				client:                    tt.fields.client,
//				ibcHandler:                tt.fields.ibcHandler,
//				signer:                    tt.fields.signer,
//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
//			}
//			got, err := c.QueryConnection(tt.args.ctx)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Chain.QueryConnection() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("Chain.QueryConnection() = %v, want %v", got, tt.want)
//				if diff := cmp.Diff(got, tt.want); diff != "" {
//					t.Errorf("Chain.QueryConnection() differs: (-got +want)n%s", diff)
//				}
//			}
//		})
//	}
//}
//
//// TODO ibcHandler が Channel を取得しに行くのでテスト不可
//func TestChain_QueryChannel(t *testing.T) {
//
//	stubChainConfig := ChainConfig{
//		ChainId:    "********",
//		EthChainId: 5151,
//		RpcAddr:    "http://**********:8451",
//		Signer: &codectypes.Any{
//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//			Value:                nil,
//			XXX_NoUnkeyedLiteral: struct{}{},
//			XXX_unrecognized:     nil,
//			XXX_sizecache:        0,
//		},
//		IbcAddress:            "******************************************",
//		InitialSendCheckpoint: 1,
//		InitialRecvCheckpoint: 1,
//		EnableDebugTrace:      false,
//		AverageBlockTimeMsec:  2000,
//		MaxRetryForInclusion:  3,
//		GasEstimateRate: &Fraction{
//			Numerator:   1,
//			Denominator: 1,
//		},
//		MaxGasLimit: *********,
//		TxType:      "auto",
//	}
//	stubPathEnd := &core.PathEnd{
//		ChainID:      "********",
//		ClientID:     "hb-ibft2-0",
//		ConnectionID: "connection-0",
//		ChannelID:    "channel-0",
//		PortID:       "account-sync",
//		Order:        "unordered",
//		Version:      "account-sync-1",
//	}
//	stubHeight := clienttypes.Height{
//		RevisionNumber: 0,
//		RevisionHeight: 100,
//	}
//
//	ctrl := gomock.NewController(t)
//	mockQueryContext := core.NewMockQueryContext(ctrl)
//	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
//	mockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
//
//	type fields struct {
//		config                    ChainConfig
//		pathEnd                   *core.PathEnd
//		homePath                  string
//		chainID                   *big.Int
//		codec                     codec.ProtoCodecMarshaler
//		msgEventListener          core.MsgEventListener
//		client                    *client.ETHClient
//		ibcHandler                *ibchandler.Ibchandler
//		signer                    Signer
//		connectionOpenedConfirmed bool
//	}
//	type args struct {
//		ctx core.QueryContext
//	}
//	tests := []struct {
//		name        string
//		fields      fields
//		args        args
//		wantChanRes *chantypes.QueryChannelResponse
//		wantErr     bool
//	}{
//		{
//			name: "should returns the channel associated with a channelID.",
//			fields: fields{
//				config:           stubChainConfig,
//				pathEnd:          stubPathEnd,
//				homePath:         ".relayer",
//				chainID:          &big.Int{},
//				codec:            core.MakeCodec(),
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			args: args{
//				ctx: mockQueryContext,
//			},
//			wantChanRes: &chantypes.QueryChannelResponse{
//				Channel: &chantypes.Channel{
//					State:    0,
//					Ordering: 0,
//					Counterparty: chantypes.Counterparty{
//						PortId:    "account-sync",
//						ChannelId: "channel-0",
//					},
//					ConnectionHops: nil,
//					Version:        "account-sync-1",
//				},
//				Proof:       nil,
//				ProofHeight: stubHeight,
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			c := &Chain{
//				config:                    tt.fields.config,
//				pathEnd:                   tt.fields.pathEnd,
//				homePath:                  tt.fields.homePath,
//				chainID:                   tt.fields.chainID,
//				codec:                     tt.fields.codec,
//				msgEventListener:          tt.fields.msgEventListener,
//				client:                    tt.fields.client,
//				ibcHandler:                tt.fields.ibcHandler,
//				signer:                    tt.fields.signer,
//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
//			}
//			gotChanRes, err := c.QueryChannel(tt.args.ctx)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Chain.QueryChannel() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(gotChanRes, tt.wantChanRes) {
//				t.Errorf("Chain.QueryChannel() = %v, want %v", gotChanRes, tt.wantChanRes)
//				if diff := cmp.Diff(gotChanRes, tt.wantChanRes); diff != "" {
//					t.Errorf("Chain.ensureDataDirectory() differs: (-gotChanRes +wantChanRes)n%s", diff)
//				}
//			}
//		})
//	}
//}
//
//// TODO ibcHandler が getPacketReceipt を取得しに行くのでテスト不可
//func TestChain_QueryUnreceivedPackets(t *testing.T) {
//
//	stubChainConfig := ChainConfig{
//		ChainId:    "********",
//		EthChainId: 5151,
//		RpcAddr:    "http://**********:8451",
//		Signer: &codectypes.Any{
//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//			Value:                nil,
//			XXX_NoUnkeyedLiteral: struct{}{},
//			XXX_unrecognized:     nil,
//			XXX_sizecache:        0,
//		},
//		IbcAddress:            "******************************************",
//		InitialSendCheckpoint: 1,
//		InitialRecvCheckpoint: 1,
//		EnableDebugTrace:      false,
//		AverageBlockTimeMsec:  2000,
//		MaxRetryForInclusion:  3,
//		GasEstimateRate: &Fraction{
//			Numerator:   1,
//			Denominator: 1,
//		},
//		MaxGasLimit: *********,
//		TxType:      "auto",
//	}
//	stubPathEnd := &core.PathEnd{
//		ChainID:      "********",
//		ClientID:     "hb-ibft2-0",
//		ConnectionID: "connection-0",
//		ChannelID:    "channel-0",
//		PortID:       "account-sync",
//		Order:        "unordered",
//		Version:      "account-sync-1",
//	}
//	stubHeight := clienttypes.Height{
//		RevisionNumber: 0,
//		RevisionHeight: 100,
//	}
//
//	ctrl := gomock.NewController(t)
//	mockQueryContext := core.NewMockQueryContext(ctrl)
//	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
//	mockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
//
//	type fields struct {
//		config                    ChainConfig
//		pathEnd                   *core.PathEnd
//		homePath                  string
//		chainID                   *big.Int
//		codec                     codec.ProtoCodecMarshaler
//		msgEventListener          core.MsgEventListener
//		client                    *client.ETHClient
//		ibcHandler                *ibchandler.Ibchandler
//		signer                    Signer
//		connectionOpenedConfirmed bool
//	}
//	type args struct {
//		ctx  core.QueryContext
//		seqs []uint64
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    []uint64
//		wantErr bool
//	}{
//		{
//			name: "should returns a list of unrelayed packet commitments.",
//			fields: fields{
//				config:           stubChainConfig,
//				pathEnd:          stubPathEnd,
//				homePath:         ".relayer",
//				chainID:          &big.Int{},
//				codec:            core.MakeCodec(),
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			args: args{
//				ctx:  mockQueryContext,
//				seqs: []uint64{100},
//			},
//			want:    []uint64{100},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			c := &Chain{
//				config:                    tt.fields.config,
//				pathEnd:                   tt.fields.pathEnd,
//				homePath:                  tt.fields.homePath,
//				chainID:                   tt.fields.chainID,
//				codec:                     tt.fields.codec,
//				msgEventListener:          tt.fields.msgEventListener,
//				client:                    tt.fields.client,
//				ibcHandler:                tt.fields.ibcHandler,
//				signer:                    tt.fields.signer,
//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
//			}
//			got, err := c.QueryUnreceivedPackets(tt.args.ctx, tt.args.seqs)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Chain.QueryUnreceivedPackets() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("Chain.QueryUnreceivedPackets() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
//// TODO protobuf 構造により CallOpts で hd/signer の Address が取得できないためテスト不可
//func TestChain_QueryUnfinalizedRelayPackets(t *testing.T) {
//
//	stubChainConfig := ChainConfig{
//		ChainId:    "********",
//		EthChainId: 5151,
//		RpcAddr:    "http://**********:8451",
//		Signer: &codectypes.Any{
//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//			Value:                nil,
//			XXX_NoUnkeyedLiteral: struct{}{},
//			XXX_unrecognized:     nil,
//			XXX_sizecache:        0,
//		},
//		IbcAddress:            "******************************************",
//		InitialSendCheckpoint: 1,
//		InitialRecvCheckpoint: 1,
//		EnableDebugTrace:      false,
//		AverageBlockTimeMsec:  2000,
//		MaxRetryForInclusion:  3,
//		GasEstimateRate: &Fraction{
//			Numerator:   1,
//			Denominator: 1,
//		},
//		MaxGasLimit: *********,
//		TxType:      "auto",
//	}
//	stubPathEnd := &core.PathEnd{
//		ChainID:      "********",
//		ClientID:     "hb-ibft2-0",
//		ConnectionID: "connection-0",
//		ChannelID:    "channel-0",
//		PortID:       "account-sync",
//		Order:        "unordered",
//		Version:      "account-sync-1",
//	}
//	stubHeight := clienttypes.Height{
//		RevisionNumber: 0,
//		RevisionHeight: 100,
//	}
//
//	ctrl := gomock.NewController(t)
//	mockQueryContext := core.NewMockQueryContext(ctrl)
//	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
//	mockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
//
//	type fields struct {
//		config                    ChainConfig
//		pathEnd                   *core.PathEnd
//		homePath                  string
//		chainID                   *big.Int
//		codec                     codec.ProtoCodecMarshaler
//		msgEventListener          core.MsgEventListener
//		client                    *client.ETHClient
//		ibcHandler                *ibchandler.Ibchandler
//		signer                    Signer
//		connectionOpenedConfirmed bool
//	}
//	type args struct {
//		ctx          core.QueryContext
//		counterparty core.LightClientICS04Querier
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    core.PacketInfoList
//		wantErr bool
//	}{
//		{
//			name: "should returns packets and heights that are sent but not received at the latest finalized block on the counterparty chain.",
//			fields: fields{
//				config:           stubChainConfig,
//				pathEnd:          stubPathEnd,
//				homePath:         ".relayer",
//				chainID:          &big.Int{},
//				codec:            core.MakeCodec(),
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			args: args{
//				ctx:          mockQueryContext,
//				counterparty: nil,
//			},
//			want:    nil,
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			c := &Chain{
//				config:                    tt.fields.config,
//				pathEnd:                   tt.fields.pathEnd,
//				homePath:                  tt.fields.homePath,
//				chainID:                   tt.fields.chainID,
//				codec:                     tt.fields.codec,
//				msgEventListener:          tt.fields.msgEventListener,
//				client:                    tt.fields.client,
//				ibcHandler:                tt.fields.ibcHandler,
//				signer:                    tt.fields.signer,
//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
//			}
//			got, err := c.QueryUnfinalizedRelayPackets(tt.args.ctx, tt.args.counterparty)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Chain.QueryUnfinalizedRelayPackets() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("Chain.QueryUnfinalizedRelayPackets() = %v, want %v", got, tt.want)
//				if diff := cmp.Diff(got, tt.want); diff != "" {
//					t.Errorf("Chain.QueryUnfinalizedRelayPackets() differs: (-got +want)n%s", diff)
//				}
//			}
//		})
//	}
//}
//
//// TODO ibcHandler が Commitment を取得しに行くのでテスト不可
//func TestChain_QueryUnreceivedAcknowledgements(t *testing.T) {
//
//	stubChainConfig := ChainConfig{
//		ChainId:    "********",
//		EthChainId: 5151,
//		RpcAddr:    "http://**********:8451",
//		Signer: &codectypes.Any{
//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//			Value:                nil,
//			XXX_NoUnkeyedLiteral: struct{}{},
//			XXX_unrecognized:     nil,
//			XXX_sizecache:        0,
//		},
//		IbcAddress:            "******************************************",
//		InitialSendCheckpoint: 1,
//		InitialRecvCheckpoint: 1,
//		EnableDebugTrace:      false,
//		AverageBlockTimeMsec:  2000,
//		MaxRetryForInclusion:  3,
//		GasEstimateRate: &Fraction{
//			Numerator:   1,
//			Denominator: 1,
//		},
//		MaxGasLimit: *********,
//		TxType:      "auto",
//	}
//	stubPathEnd := &core.PathEnd{
//		ChainID:      "********",
//		ClientID:     "hb-ibft2-0",
//		ConnectionID: "connection-0",
//		ChannelID:    "channel-0",
//		PortID:       "account-sync",
//		Order:        "unordered",
//		Version:      "account-sync-1",
//	}
//	stubHeight := clienttypes.Height{
//		RevisionNumber: 0,
//		RevisionHeight: 100,
//	}
//
//	ctrl := gomock.NewController(t)
//	mockQueryContext := core.NewMockQueryContext(ctrl)
//	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
//	mockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
//
//	type fields struct {
//		config                    ChainConfig
//		pathEnd                   *core.PathEnd
//		homePath                  string
//		chainID                   *big.Int
//		codec                     codec.ProtoCodecMarshaler
//		msgEventListener          core.MsgEventListener
//		client                    *client.ETHClient
//		ibcHandler                *ibchandler.Ibchandler
//		signer                    Signer
//		connectionOpenedConfirmed bool
//	}
//	type args struct {
//		ctx  core.QueryContext
//		seqs []uint64
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    []uint64
//		wantErr bool
//	}{
//		{
//			name: "should returns a list of unrelayed packet acks.",
//			fields: fields{
//				config:           stubChainConfig,
//				pathEnd:          stubPathEnd,
//				homePath:         ".relayer",
//				chainID:          &big.Int{},
//				codec:            core.MakeCodec(),
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			args: args{
//				ctx:  mockQueryContext,
//				seqs: []uint64{100},
//			},
//			want:    []uint64{100},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			c := &Chain{
//				config:                    tt.fields.config,
//				pathEnd:                   tt.fields.pathEnd,
//				homePath:                  tt.fields.homePath,
//				chainID:                   tt.fields.chainID,
//				codec:                     tt.fields.codec,
//				msgEventListener:          tt.fields.msgEventListener,
//				client:                    tt.fields.client,
//				ibcHandler:                tt.fields.ibcHandler,
//				signer:                    tt.fields.signer,
//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
//			}
//			got, err := c.QueryUnreceivedAcknowledgements(tt.args.ctx, tt.args.seqs)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Chain.QueryUnreceivedAcknowledgements() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("Chain.QueryUnreceivedAcknowledgements() = %v, want %v", got, tt.want)
//				if diff := cmp.Diff(got, tt.want); diff != "" {
//					t.Errorf("Chain.QueryUnreceivedAcknowledgements() differs: (-got +want)n%s", diff)
//				}
//			}
//		})
//	}
//}
//
//// TODO ibcHandler が ReceivedPackets を取得しに行くのでテスト不可
//func TestChain_QueryUnfinalizedRelayAcknowledgements(t *testing.T) {
//
//	stubChainConfig := ChainConfig{
//		ChainId:    "********",
//		EthChainId: 5151,
//		RpcAddr:    "http://**********:8451",
//		Signer: &codectypes.Any{
//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//			Value:                nil,
//			XXX_NoUnkeyedLiteral: struct{}{},
//			XXX_unrecognized:     nil,
//			XXX_sizecache:        0,
//		},
//		IbcAddress:            "******************************************",
//		InitialSendCheckpoint: 1,
//		InitialRecvCheckpoint: 1,
//		EnableDebugTrace:      false,
//		AverageBlockTimeMsec:  2000,
//		MaxRetryForInclusion:  3,
//		GasEstimateRate: &Fraction{
//			Numerator:   1,
//			Denominator: 1,
//		},
//		MaxGasLimit: *********,
//		TxType:      "auto",
//	}
//	stubPathEnd := &core.PathEnd{
//		ChainID:      "********",
//		ClientID:     "hb-ibft2-0",
//		ConnectionID: "connection-0",
//		ChannelID:    "channel-0",
//		PortID:       "account-sync",
//		Order:        "unordered",
//		Version:      "account-sync-1",
//	}
//	stubHeight := clienttypes.Height{
//		RevisionNumber: 0,
//		RevisionHeight: 100,
//	}
//
//	ctrl := gomock.NewController(t)
//	mockQueryContext := core.NewMockQueryContext(ctrl)
//	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
//	mockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
//
//	type fields struct {
//		config                    ChainConfig
//		pathEnd                   *core.PathEnd
//		homePath                  string
//		chainID                   *big.Int
//		codec                     codec.ProtoCodecMarshaler
//		msgEventListener          core.MsgEventListener
//		client                    *client.ETHClient
//		ibcHandler                *ibchandler.Ibchandler
//		signer                    Signer
//		connectionOpenedConfirmed bool
//	}
//	type args struct {
//		ctx          core.QueryContext
//		counterparty core.LightClientICS04Querier
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    core.PacketInfoList
//		wantErr bool
//	}{
//		{
//			name: "should returns acks and heights that are sent but not received at the latest finalized block on the counterpartychain.",
//			fields: fields{
//				config:           stubChainConfig,
//				pathEnd:          stubPathEnd,
//				homePath:         ".relayer",
//				chainID:          &big.Int{},
//				codec:            core.MakeCodec(),
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			args: args{
//				ctx:          mockQueryContext,
//				counterparty: nil,
//			},
//			want:    core.PacketInfoList{},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			c := &Chain{
//				config:                    tt.fields.config,
//				pathEnd:                   tt.fields.pathEnd,
//				homePath:                  tt.fields.homePath,
//				chainID:                   tt.fields.chainID,
//				codec:                     tt.fields.codec,
//				msgEventListener:          tt.fields.msgEventListener,
//				client:                    tt.fields.client,
//				ibcHandler:                tt.fields.ibcHandler,
//				signer:                    tt.fields.signer,
//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
//			}
//			got, err := c.QueryUnfinalizedRelayAcknowledgements(tt.args.ctx, tt.args.counterparty)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Chain.QueryUnfinalizedRelayAcknowledgements() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("Chain.QueryUnfinalizedRelayAcknowledgements() = %v, want %v", got, tt.want)
//				if diff := cmp.Diff(got, tt.want); diff != "" {
//					t.Errorf("Chain.QueryUnfinalizedRelayAcknowledgements() differs: (-got +want)n%s", diff)
//				}
//			}
//		})
//	}
//}
//
//// TODO not supported のため panic が発生
//func TestChain_QueryBalance(t *testing.T) {
//
//	stubChainConfig := ChainConfig{
//		ChainId:    "********",
//		EthChainId: 5151,
//		RpcAddr:    "http://**********:8451",
//		Signer: &codectypes.Any{
//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//			Value:                nil,
//			XXX_NoUnkeyedLiteral: struct{}{},
//			XXX_unrecognized:     nil,
//			XXX_sizecache:        0,
//		},
//		IbcAddress:            "******************************************",
//		InitialSendCheckpoint: 1,
//		InitialRecvCheckpoint: 1,
//		EnableDebugTrace:      false,
//		AverageBlockTimeMsec:  2000,
//		MaxRetryForInclusion:  3,
//		GasEstimateRate: &Fraction{
//			Numerator:   1,
//			Denominator: 1,
//		},
//		MaxGasLimit: *********,
//		TxType:      "auto",
//	}
//	stubPathEnd := &core.PathEnd{
//		ChainID:      "********",
//		ClientID:     "hb-ibft2-0",
//		ConnectionID: "connection-0",
//		ChannelID:    "channel-0",
//		PortID:       "account-sync",
//		Order:        "unordered",
//		Version:      "account-sync-1",
//	}
//	stubHeight := clienttypes.Height{
//		RevisionNumber: 0,
//		RevisionHeight: 100,
//	}
//
//	ctrl := gomock.NewController(t)
//	mockQueryContext := core.NewMockQueryContext(ctrl)
//	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
//	mockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
//
//	type fields struct {
//		config                    ChainConfig
//		pathEnd                   *core.PathEnd
//		homePath                  string
//		chainID                   *big.Int
//		codec                     codec.ProtoCodecMarshaler
//		msgEventListener          core.MsgEventListener
//		client                    *client.ETHClient
//		ibcHandler                *ibchandler.Ibchandler
//		signer                    Signer
//		connectionOpenedConfirmed bool
//	}
//	type args struct {
//		ctx     core.QueryContext
//		address sdk.AccAddress
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    sdk.Coins
//		wantErr bool
//	}{
//		{
//			name: "should returns balances correctly",
//			fields: fields{
//				config:           stubChainConfig,
//				pathEnd:          stubPathEnd,
//				homePath:         ".relayer",
//				chainID:          &big.Int{},
//				codec:            core.MakeCodec(),
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			args: args{
//				ctx:     mockQueryContext,
//				address: sdk.AccAddress{},
//			},
//			want:    nil,
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			c := &Chain{
//				config:                    tt.fields.config,
//				pathEnd:                   tt.fields.pathEnd,
//				homePath:                  tt.fields.homePath,
//				chainID:                   tt.fields.chainID,
//				codec:                     tt.fields.codec,
//				msgEventListener:          tt.fields.msgEventListener,
//				client:                    tt.fields.client,
//				ibcHandler:                tt.fields.ibcHandler,
//				signer:                    tt.fields.signer,
//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
//			}
//			got, err := c.QueryBalance(tt.args.ctx, tt.args.address)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Chain.QueryBalance() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("Chain.QueryBalance() = %v, want %v", got, tt.want)
//				if diff := cmp.Diff(got, tt.want); diff != "" {
//					t.Errorf("Chain.QueryBalance() differs: (-got +want)n%s", diff)
//				}
//			}
//		})
//	}
//}
//
//// TODO not supported のため panic が発生
//func TestChain_QueryDenomTraces(t *testing.T) {
//
//	stubChainConfig := ChainConfig{
//		ChainId:    "********",
//		EthChainId: 5151,
//		RpcAddr:    "http://**********:8451",
//		Signer: &codectypes.Any{
//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//			Value:                nil,
//			XXX_NoUnkeyedLiteral: struct{}{},
//			XXX_unrecognized:     nil,
//			XXX_sizecache:        0,
//		},
//		IbcAddress:            "******************************************",
//		InitialSendCheckpoint: 1,
//		InitialRecvCheckpoint: 1,
//		EnableDebugTrace:      false,
//		AverageBlockTimeMsec:  2000,
//		MaxRetryForInclusion:  3,
//		GasEstimateRate: &Fraction{
//			Numerator:   1,
//			Denominator: 1,
//		},
//		MaxGasLimit: *********,
//		TxType:      "auto",
//	}
//	stubPathEnd := &core.PathEnd{
//		ChainID:      "********",
//		ClientID:     "hb-ibft2-0",
//		ConnectionID: "connection-0",
//		ChannelID:    "channel-0",
//		PortID:       "account-sync",
//		Order:        "unordered",
//		Version:      "account-sync-1",
//	}
//	stubHeight := clienttypes.Height{
//		RevisionNumber: 0,
//		RevisionHeight: 100,
//	}
//
//	ctrl := gomock.NewController(t)
//	mockQueryContext := core.NewMockQueryContext(ctrl)
//	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
//	mockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
//
//	type fields struct {
//		config                    ChainConfig
//		pathEnd                   *core.PathEnd
//		homePath                  string
//		chainID                   *big.Int
//		codec                     codec.ProtoCodecMarshaler
//		msgEventListener          core.MsgEventListener
//		client                    *client.ETHClient
//		ibcHandler                *ibchandler.Ibchandler
//		signer                    Signer
//		connectionOpenedConfirmed bool
//	}
//	type args struct {
//		ctx    core.QueryContext
//		offset uint64
//		limit  uint64
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *transfertypes.QueryDenomTracesResponse
//		wantErr bool
//	}{
//		{
//			name: "should returns all the denom traces from a given chain",
//			fields: fields{
//				config:           stubChainConfig,
//				pathEnd:          stubPathEnd,
//				homePath:         ".relayer",
//				chainID:          &big.Int{},
//				codec:            core.MakeCodec(),
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			args: args{
//				ctx:    mockQueryContext,
//				offset: 0,
//				limit:  0,
//			},
//			want: &transfertypes.QueryDenomTracesResponse{
//				DenomTraces: nil,
//				Pagination: &query.PageResponse{
//					NextKey: nil,
//					Total:   0,
//				},
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			c := &Chain{
//				config:                    tt.fields.config,
//				pathEnd:                   tt.fields.pathEnd,
//				homePath:                  tt.fields.homePath,
//				chainID:                   tt.fields.chainID,
//				codec:                     tt.fields.codec,
//				msgEventListener:          tt.fields.msgEventListener,
//				client:                    tt.fields.client,
//				ibcHandler:                tt.fields.ibcHandler,
//				signer:                    tt.fields.signer,
//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
//			}
//			got, err := c.QueryDenomTraces(tt.args.ctx, tt.args.offset, tt.args.limit)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Chain.QueryDenomTraces() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("Chain.QueryDenomTraces() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
//// TODO protobuf 構造により CallOpts で hd/signer の Address が取得できないためテスト不可
//func TestChain_callOptsFromQueryContext(t *testing.T) {
//
//	stubChainConfig := ChainConfig{
//		ChainId:    "********",
//		EthChainId: 5151,
//		RpcAddr:    "http://**********:8451",
//		Signer: &codectypes.Any{
//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//			Value:                nil,
//			XXX_NoUnkeyedLiteral: struct{}{},
//			XXX_unrecognized:     nil,
//			XXX_sizecache:        0,
//		},
//		IbcAddress:            "******************************************",
//		InitialSendCheckpoint: 1,
//		InitialRecvCheckpoint: 1,
//		EnableDebugTrace:      false,
//		AverageBlockTimeMsec:  2000,
//		MaxRetryForInclusion:  3,
//		GasEstimateRate: &Fraction{
//			Numerator:   1,
//			Denominator: 1,
//		},
//		MaxGasLimit: *********,
//		TxType:      "auto",
//	}
//	stubPathEnd := &core.PathEnd{
//		ChainID:      "********",
//		ClientID:     "hb-ibft2-0",
//		ConnectionID: "connection-0",
//		ChannelID:    "channel-0",
//		PortID:       "account-sync",
//		Order:        "unordered",
//		Version:      "account-sync-1",
//	}
//	stubHeight := clienttypes.Height{
//		RevisionNumber: 0,
//		RevisionHeight: 100,
//	}
//
//	ctrl := gomock.NewController(t)
//	mockQueryContext := core.NewMockQueryContext(ctrl)
//	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
//	mockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
//
//	type fields struct {
//		config                    ChainConfig
//		pathEnd                   *core.PathEnd
//		homePath                  string
//		chainID                   *big.Int
//		codec                     codec.ProtoCodecMarshaler
//		msgEventListener          core.MsgEventListener
//		client                    *client.ETHClient
//		ibcHandler                *ibchandler.Ibchandler
//		signer                    Signer
//		connectionOpenedConfirmed bool
//	}
//	type args struct {
//		ctx core.QueryContext
//	}
//	tests := []struct {
//		name   string
//		fields fields
//		args   args
//		want   *bind.CallOpts
//	}{
//		// TODO: Add test cases.
//		{
//			name: "",
//			fields: fields{
//				config:           stubChainConfig,
//				pathEnd:          stubPathEnd,
//				homePath:         ".relayer",
//				chainID:          &big.Int{},
//				codec:            nil,
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			args: args{
//				ctx: mockQueryContext,
//			},
//			want: &bind.CallOpts{
//				Pending:     false,
//				From:        common.Address{},
//				BlockNumber: &big.Int{},
//				Context:     nil,
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			c := &Chain{
//				config:                    tt.fields.config,
//				pathEnd:                   tt.fields.pathEnd,
//				homePath:                  tt.fields.homePath,
//				chainID:                   tt.fields.chainID,
//				codec:                     tt.fields.codec,
//				msgEventListener:          tt.fields.msgEventListener,
//				client:                    tt.fields.client,
//				ibcHandler:                tt.fields.ibcHandler,
//				signer:                    tt.fields.signer,
//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
//			}
//			if got := c.callOptsFromQueryContext(tt.args.ctx); !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("Chain.callOptsFromQueryContext() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
//func TestChain_GetChainLogger(t *testing.T) {
//
//	stubChainConfig := ChainConfig{
//		ChainId:    "********",
//		EthChainId: 5151,
//		RpcAddr:    "http://**********:8451",
//		Signer: &codectypes.Any{
//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//			Value:                nil,
//			XXX_NoUnkeyedLiteral: struct{}{},
//			XXX_unrecognized:     nil,
//			XXX_sizecache:        0,
//		},
//		IbcAddress:            "******************************************",
//		InitialSendCheckpoint: 1,
//		InitialRecvCheckpoint: 1,
//		EnableDebugTrace:      false,
//		AverageBlockTimeMsec:  2000,
//		MaxRetryForInclusion:  3,
//		GasEstimateRate: &Fraction{
//			Numerator:   1,
//			Denominator: 1,
//		},
//		MaxGasLimit: *********,
//		TxType:      "auto",
//	}
//	stubPathEnd := &core.PathEnd{
//		ChainID:      "********",
//		ClientID:     "hb-ibft2-0",
//		ConnectionID: "connection-0",
//		ChannelID:    "channel-0",
//		PortID:       "account-sync",
//		Order:        "unordered",
//		Version:      "account-sync-1",
//	}
//	stubHeight := clienttypes.Height{
//		RevisionNumber: 0,
//		RevisionHeight: 100,
//	}
//
//	ctrl := gomock.NewController(t)
//	mockQueryContext := core.NewMockQueryContext(ctrl)
//	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
//	mockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
//
//	type fields struct {
//		config                    ChainConfig
//		pathEnd                   *core.PathEnd
//		homePath                  string
//		chainID                   *big.Int
//		codec                     codec.ProtoCodecMarshaler
//		msgEventListener          core.MsgEventListener
//		client                    *client.ETHClient
//		ibcHandler                *ibchandler.Ibchandler
//		signer                    Signer
//		connectionOpenedConfirmed bool
//	}
//	tests := []struct {
//		name   string
//		fields fields
//		want   *log.DcjpyLogger
//	}{
//		{
//			name: "should get ChainLogger.",
//			fields: fields{
//				config:           stubChainConfig,
//				pathEnd:          stubPathEnd,
//				homePath:         ".relayer",
//				chainID:          &big.Int{},
//				codec:            core.MakeCodec(),
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			want: &log.DcjpyLogger{
//				Logger: &slog.Logger{},
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			c := &Chain{
//				config:                    tt.fields.config,
//				pathEnd:                   tt.fields.pathEnd,
//				homePath:                  tt.fields.homePath,
//				chainID:                   tt.fields.chainID,
//				codec:                     tt.fields.codec,
//				msgEventListener:          tt.fields.msgEventListener,
//				client:                    tt.fields.client,
//				ibcHandler:                tt.fields.ibcHandler,
//				signer:                    tt.fields.signer,
//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
//			}
//			got := c.GetChainLogger()
//			if got == nil {
//				t.Errorf("Chain.GetChainLogger() = %v", got)
//			}
//		})
//	}
//}
//
//// TODO QueryConnection が GetConnection しに行くのでテスト不可
//func TestChain_confirmConnectionOpened(t *testing.T) {
//
//	stubChainConfig := ChainConfig{
//		ChainId:    "********",
//		EthChainId: 5151,
//		RpcAddr:    "http://**********:8451",
//		Signer: &codectypes.Any{
//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//			Value:                nil,
//			XXX_NoUnkeyedLiteral: struct{}{},
//			XXX_unrecognized:     nil,
//			XXX_sizecache:        0,
//		},
//		IbcAddress:            "******************************************",
//		InitialSendCheckpoint: 1,
//		InitialRecvCheckpoint: 1,
//		EnableDebugTrace:      false,
//		AverageBlockTimeMsec:  2000,
//		MaxRetryForInclusion:  3,
//		GasEstimateRate: &Fraction{
//			Numerator:   1,
//			Denominator: 1,
//		},
//		MaxGasLimit: *********,
//		TxType:      "auto",
//	}
//	stubPathEnd := &core.PathEnd{
//		ChainID:      "********",
//		ClientID:     "hb-ibft2-0",
//		ConnectionID: "connection-0",
//		ChannelID:    "channel-0",
//		PortID:       "account-sync",
//		Order:        "unordered",
//		Version:      "account-sync-1",
//	}
//	stubHeight := clienttypes.Height{
//		RevisionNumber: 0,
//		RevisionHeight: 100,
//	}
//
//	ctrl := gomock.NewController(t)
//	mockQueryContext := core.NewMockQueryContext(ctrl)
//	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
//	mockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
//
//	type fields struct {
//		config                    ChainConfig
//		pathEnd                   *core.PathEnd
//		homePath                  string
//		chainID                   *big.Int
//		codec                     codec.ProtoCodecMarshaler
//		msgEventListener          core.MsgEventListener
//		client                    *client.ETHClient
//		ibcHandler                *ibchandler.Ibchandler
//		signer                    Signer
//		connectionOpenedConfirmed bool
//	}
//	type args struct {
//		ctx context.Context
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    bool
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//		{
//			name: "",
//			fields: fields{
//				config:           stubChainConfig,
//				pathEnd:          stubPathEnd,
//				homePath:         ".relayer",
//				chainID:          &big.Int{},
//				codec:            core.MakeCodec(),
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			args: args{
//				ctx: context.TODO(),
//			},
//			want:    false,
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			c := &Chain{
//				config:                    tt.fields.config,
//				pathEnd:                   tt.fields.pathEnd,
//				homePath:                  tt.fields.homePath,
//				chainID:                   tt.fields.chainID,
//				codec:                     tt.fields.codec,
//				msgEventListener:          tt.fields.msgEventListener,
//				client:                    tt.fields.client,
//				ibcHandler:                tt.fields.ibcHandler,
//				signer:                    tt.fields.signer,
//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
//			}
//			got, err := c.confirmConnectionOpened(tt.args.ctx)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Chain.confirmConnectionOpened() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if got != tt.want {
//				t.Errorf("Chain.confirmConnectionOpened() = %v, want %v", got, tt.want)
//				if diff := cmp.Diff(got, tt.want); diff != "" {
//					t.Errorf("Chain.confirmConnectionOpened() differs: (-got +want)n%s", diff)
//				}
//			}
//		})
//	}
//}

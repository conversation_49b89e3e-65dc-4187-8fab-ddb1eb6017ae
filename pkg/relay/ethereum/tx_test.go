//go:generate mockgen -source=$GOFILE -package=ethereum -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package ethereum

import (
	"math/big"
	"reflect"
	"testing"

	"github.com/cosmos/cosmos-sdk/codec"
	codectypes "github.com/cosmos/cosmos-sdk/codec/types"
	sdk "github.com/cosmos/cosmos-sdk/types"
	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	types2 "github.com/cosmos/ibc-go/v7/modules/core/03-connection/types"
	chantypes "github.com/cosmos/ibc-go/v7/modules/core/04-channel/types"
	commitmenttypes "github.com/cosmos/ibc-go/v7/modules/core/23-commitment/types"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/client"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/contract/ibchandler"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	gethtypes "github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/google/go-cmp/cmp"
)

func TestChain_SendMsgs(t *testing.T) {

	var stubChainConfig = ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	var stubPathEnd = &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		msgs []sdk.Msg
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []core.MsgID
		wantErr bool
	}{
		// TODO BlockNumber 取得時に eth_blockNumber が実行されるのでテスト不可
		//{
		//	name: "shoule return failed to confirm connection opened.",
		//	prepareMock: func() {
		//		srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
		//		srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
		//		dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
		//		dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
		//
		//		srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
		//		dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
		//
		//		mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
		//		mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
		//		mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
		//		mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
		//		mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
		//		mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//
		//		srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
		//		srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
		//		srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
		//		// Error
		//		srcMockChain.EXPECT().LatestHeight().Return(nil, nil).AnyTimes()
		//		srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubUninitializedQueryConnectionResponse, nil).AnyTimes()
		//		srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()
		//
		//		dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
		//		dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
		//		dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
		//		dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
		//		dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubUninitializedQueryConnectionResponse, nil).AnyTimes()
		//		dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()
		//
		//		srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
		//		srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//
		//		dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
		//		dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//	},
		//	fields: fields{
		//		config:           stubChainConfig,
		//		pathEnd:          stubPathEnd,
		//		homePath:         ".relayer",
		//		chainID:          &big.Int{},
		//		codec:            core.MakeCodec(),
		//		msgEventListener: nil,
		//		client: &client.ETHClient{
		//			Client: &ethclient.Client{},
		//		},
		//		ibcHandler: &ibchandler.Ibchandler{
		//			IbchandlerCaller:     ibchandler.IbchandlerCaller{},
		//			IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
		//			IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
		//		},
		//		signer:                    nil,
		//		connectionOpenedConfirmed: false,
		//	},
		//	args: args{
		//		msgs: []sdk.Msg{},
		//	},
		//	want:    nil,
		//	wantErr: true,
		//},
		// TODO SendTx 時 に 必要な chain.signer.Address() が設定不可なのでテスト不可
		//{
		//	name: "",
		//	prepareMock: func() {
		//		srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
		//		srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
		//		dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
		//		dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
		//
		//		srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
		//		dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
		//
		//		mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
		//		mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
		//		mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
		//		mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
		//		mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
		//		mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//
		//		srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
		//		// Error
		//		srcMockChain.EXPECT().Path().Return(invalidPath).AnyTimes()
		//		srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
		//		srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
		//		srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubUninitializedQueryConnectionResponse, nil).AnyTimes()
		//		srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()
		//
		//		dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
		//		dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
		//		dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
		//		dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
		//		dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubUninitializedQueryConnectionResponse, nil).AnyTimes()
		//		dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()
		//
		//		srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
		//		srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//
		//		dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
		//		dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//	},
		//	fields: fields{
		//		config:           stubChainConfig,
		//		pathEnd:          stubPathEnd,
		//		homePath:         ".relayer",
		//		chainID:          &big.Int{},
		//		codec:            core.MakeCodec(),
		//		msgEventListener: nil,
		//		client: &client.ETHClient{
		//			Client: &ethclient.Client{},
		//		},
		//		ibcHandler: &ibchandler.Ibchandler{
		//			IbchandlerCaller:     ibchandler.IbchandlerCaller{},
		//			IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
		//			IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
		//		},
		//		signer:                    nil,
		//		connectionOpenedConfirmed: true,
		//	},
		//	args: args{
		//		msgs: append(
		//			[]sdk.Msg{},
		//			// Client
		//			&clienttypes.MsgCreateClient{},
		//			&clienttypes.MsgUpdateClient{},
		//			// Connection
		//			&types2.MsgConnectionOpenInit{},
		//			&types2.MsgConnectionOpenTry{},
		//			&types2.MsgConnectionOpenAck{},
		//			&types2.MsgConnectionOpenConfirm{},
		//			// Channel
		//			&chantypes.MsgChannelOpenInit{},
		//			&chantypes.MsgChannelOpenTry{},
		//			&chantypes.MsgChannelOpenAck{},
		//			&chantypes.MsgChannelOpenConfirm{},
		//			&chantypes.MsgChannelCloseConfirm{},
		//			&chantypes.MsgRecvPacket{},
		//			&chantypes.MsgAcknowledgement{},
		//		),
		//	},
		//	want:    nil,
		//	wantErr: false,
		//},
		{
			name: "no SendMsgs.",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            core.MakeCodec(),
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: true,
			},
			args: args{
				msgs: []sdk.Msg{},
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.SendMsgs(tt.args.msgs)
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.SendMsgs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.SendMsgs() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Chain.SendMsgs() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

// TODO: eth_getTransactionReceipt が実行されてしまうため実施不可
func TestChain_GetMsgResult(t *testing.T) {
	t.Skip("cannot not possible because eth_getTransactionReceipt will be executed..")

	var stubChainConfig = ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	var stubPathEnd = &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		id core.MsgID
	}
	tests := []struct {
		name        string
		prepareMock func()
		fields      fields
		args        args
		want        core.MsgResult
		wantErr     bool
	}{
		{
			name: "success",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            core.MakeCodec(),
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				id: NewMsgID(common.HexToHash("******************************************")),
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.GetMsgResult(tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.GetMsgResult() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.GetMsgResult() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Chain.GetMsgResult() differs: (-got +want)n%s", diff)
					if diff := cmp.Diff(got, tt.want); diff != "" {
						t.Errorf("Chain.GetMsgResult() differs: (-got +want)n%s", diff)
					}
				}
			}
		})
	}
}

// TODO ibcHandler が CreateClient を実行してしまうため実施不可
func TestChain_TxCreateClient(t *testing.T) {
	t.Skip("cannot not possible because ibcHandler executes CreateClient.")

	var stubChainConfig = ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	var stubPathEnd = &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		opts *bind.TransactOpts
		msg  *clienttypes.MsgCreateClient
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *gethtypes.Transaction
		wantErr bool
	}{
		{
			name: "should TxCreateClient.",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				opts: &bind.TransactOpts{
					From:      common.Address{},
					Nonce:     &big.Int{},
					Signer:    nil,
					Value:     &big.Int{},
					GasPrice:  &big.Int{},
					GasFeeCap: &big.Int{},
					GasTipCap: &big.Int{},
					GasLimit:  0,
					Context:   nil,
					NoSend:    false,
				},
				msg: &clienttypes.MsgCreateClient{
					ClientState: &codectypes.Any{
						TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					ConsensusState: &codectypes.Any{
						TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					Signer: "aaaa",
				},
			},
			want:    &gethtypes.Transaction{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.TxCreateClient(tt.args.opts, tt.args.msg)
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.TxCreateClient() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.TxCreateClient() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Chain.TxCreateClient() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

// TODO ibcHandler が UpdateClient を実行してしまうため実施不可
func TestChain_TxUpdateClient(t *testing.T) {
	t.Skip("cannot not possible because ibcHandler executes UpdateClient.")

	var stubChainConfig = ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	var stubPathEnd = &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		opts                       *bind.TransactOpts
		msg                        *clienttypes.MsgUpdateClient
		skipUpdateClientCommitment bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *gethtypes.Transaction
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "should TxUpdateClient.",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				opts: &bind.TransactOpts{
					From:      common.Address{},
					Nonce:     &big.Int{},
					Signer:    nil,
					Value:     &big.Int{},
					GasPrice:  &big.Int{},
					GasFeeCap: &big.Int{},
					GasTipCap: &big.Int{},
					GasLimit:  0,
					Context:   nil,
					NoSend:    false,
				},
				msg: &clienttypes.MsgUpdateClient{
					ClientId: "",
					ClientMessage: &codectypes.Any{
						TypeUrl:              "",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					Signer: "",
				},
				skipUpdateClientCommitment: false,
			},
			want:    &gethtypes.Transaction{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.TxUpdateClient(tt.args.opts, tt.args.msg, tt.args.skipUpdateClientCommitment)
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.TxUpdateClient() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.TxUpdateClient() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TODO ibcHandler が UpdateClient を実行してしまうため実施不可
func TestChain_TxConnectionOpenInit(t *testing.T) {
	t.Skip("cannot not possible because ibcHandler executes ConnectionOpenInit.")

	var stubChainConfig = ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	var stubPathEnd = &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		opts *bind.TransactOpts
		msg  *types2.MsgConnectionOpenInit
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *gethtypes.Transaction
		wantErr bool
	}{
		{
			name: "should TxConnectionOpenInit.",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				opts: &bind.TransactOpts{
					From:      common.Address{},
					Nonce:     &big.Int{},
					Signer:    nil,
					Value:     &big.Int{},
					GasPrice:  &big.Int{},
					GasFeeCap: &big.Int{},
					GasTipCap: &big.Int{},
					GasLimit:  0,
					Context:   nil,
					NoSend:    false,
				},
				msg: &types2.MsgConnectionOpenInit{
					ClientId: "",
					Counterparty: types2.Counterparty{
						ClientId:     "",
						ConnectionId: "",
						Prefix: commitmenttypes.MerklePrefix{
							KeyPrefix: nil,
						},
					},
					Version: &types2.Version{
						Identifier: "",
						Features:   nil,
					},
					DelayPeriod: 0,
					Signer:      "",
				},
			},
			want:    &gethtypes.Transaction{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.TxConnectionOpenInit(tt.args.opts, tt.args.msg)
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.TxConnectionOpenInit() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.TxConnectionOpenInit() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TODO ibcHandler が ConnectionOpenTry を実行してしまうため実施不可
func TestChain_TxConnectionOpenTry(t *testing.T) {
	t.Skip("cannot not possible because ibcHandler executes ConnectionOpenTry.")

	var stubChainConfig = ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	var stubPathEnd = &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		opts *bind.TransactOpts
		msg  *types2.MsgConnectionOpenTry
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *gethtypes.Transaction
		wantErr bool
	}{
		{
			name: "should TxConnectionOpenTry",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				opts: &bind.TransactOpts{
					From:      common.Address{},
					Nonce:     &big.Int{},
					Signer:    nil,
					Value:     &big.Int{},
					GasPrice:  &big.Int{},
					GasFeeCap: &big.Int{},
					GasTipCap: &big.Int{},
					GasLimit:  0,
					Context:   nil,
					NoSend:    false,
				},
				msg: &types2.MsgConnectionOpenTry{
					ClientId:             "",
					PreviousConnectionId: "",
					ClientState: &codectypes.Any{
						TypeUrl:              "",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					Counterparty: types2.Counterparty{
						ClientId:     "",
						ConnectionId: "",
						Prefix: commitmenttypes.MerklePrefix{
							KeyPrefix: nil,
						},
					},
					DelayPeriod:          0,
					CounterpartyVersions: nil,
					ProofHeight: clienttypes.Height{
						RevisionNumber: 0,
						RevisionHeight: 0,
					},
					ProofInit:      nil,
					ProofClient:    nil,
					ProofConsensus: nil,
					ConsensusHeight: clienttypes.Height{
						RevisionNumber: 0,
						RevisionHeight: 0,
					},
					Signer:                  "",
					HostConsensusStateProof: nil,
				},
			},
			want:    &gethtypes.Transaction{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.TxConnectionOpenTry(tt.args.opts, tt.args.msg)
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.TxConnectionOpenTry() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.TxConnectionOpenTry() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TODO ibcHandler が TxConnectionOpenAck を実行してしまうため実施不可
func TestChain_TxConnectionOpenAck(t *testing.T) {
	t.Skip("cannot not possible because ibcHandler executes TxConnectionOpenAck.")

	var stubChainConfig = ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	var stubPathEnd = &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		opts *bind.TransactOpts
		msg  *types2.MsgConnectionOpenAck
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *gethtypes.Transaction
		wantErr bool
	}{
		{
			name: "should TxConnectionOpenAck.",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				opts: &bind.TransactOpts{
					From:      common.Address{},
					Nonce:     &big.Int{},
					Signer:    nil,
					Value:     &big.Int{},
					GasPrice:  &big.Int{},
					GasFeeCap: &big.Int{},
					GasTipCap: &big.Int{},
					GasLimit:  0,
					Context:   nil,
					NoSend:    false,
				},
				msg: &types2.MsgConnectionOpenAck{
					ConnectionId:             "",
					CounterpartyConnectionId: "",
					Version: &types2.Version{
						Identifier: "",
						Features:   nil,
					},
					ClientState: &codectypes.Any{
						TypeUrl:              "",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					ProofHeight: clienttypes.Height{
						RevisionNumber: 0,
						RevisionHeight: 0,
					},
					ProofTry:       nil,
					ProofClient:    nil,
					ProofConsensus: nil,
					ConsensusHeight: clienttypes.Height{
						RevisionNumber: 0,
						RevisionHeight: 0,
					},
					Signer:                  "",
					HostConsensusStateProof: nil,
				},
			},
			want:    &gethtypes.Transaction{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.TxConnectionOpenAck(tt.args.opts, tt.args.msg)
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.TxConnectionOpenAck() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.TxConnectionOpenAck() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Chain.TxConnectionOpenAck() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

// TODO ibcHandler が TxConnectionOpenConfirm を実行してしまうため実施不可
func TestChain_TxConnectionOpenConfirm(t *testing.T) {
	t.Skip("cannot not possible because ibcHandler executes TxConnectionOpenConfirm.")

	var stubChainConfig = ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	var stubPathEnd = &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		opts *bind.TransactOpts
		msg  *types2.MsgConnectionOpenConfirm
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *gethtypes.Transaction
		wantErr bool
	}{
		{
			name: "should TxConnectionOpenConfirm.",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				opts: &bind.TransactOpts{
					From:      common.Address{},
					Nonce:     &big.Int{},
					Signer:    nil,
					Value:     &big.Int{},
					GasPrice:  &big.Int{},
					GasFeeCap: &big.Int{},
					GasTipCap: &big.Int{},
					GasLimit:  0,
					Context:   nil,
					NoSend:    false,
				},
				msg: &types2.MsgConnectionOpenConfirm{
					ConnectionId: "",
					ProofAck:     nil,
					ProofHeight: clienttypes.Height{
						RevisionNumber: 0,
						RevisionHeight: 0,
					},
					Signer: "",
				},
			},
			want:    &gethtypes.Transaction{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.TxConnectionOpenConfirm(tt.args.opts, tt.args.msg)
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.TxConnectionOpenConfirm() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.TxConnectionOpenConfirm() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Chain.TxConnectionOpenConfirm() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

// TODO ibcHandler が TxChannelOpenInit を実行してしまうため実施不可
func TestChain_TxChannelOpenInit(t *testing.T) {
	t.Skip("cannot not possible because ibcHandler executes TxChannelOpenInit.")

	var stubChainConfig = ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	var stubPathEnd = &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		opts *bind.TransactOpts
		msg  *chantypes.MsgChannelOpenInit
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *gethtypes.Transaction
		wantErr bool
	}{
		{
			name: "should TxChannelOpenInit.",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				opts: &bind.TransactOpts{
					From:      common.Address{},
					Nonce:     &big.Int{},
					Signer:    nil,
					Value:     &big.Int{},
					GasPrice:  &big.Int{},
					GasFeeCap: &big.Int{},
					GasTipCap: &big.Int{},
					GasLimit:  0,
					Context:   nil,
					NoSend:    false,
				},
				msg: &chantypes.MsgChannelOpenInit{
					PortId: "",
					Channel: chantypes.Channel{
						State:    0,
						Ordering: 0,
						Counterparty: chantypes.Counterparty{
							PortId:    "",
							ChannelId: "",
						},
						ConnectionHops: nil,
						Version:        "",
					},
					Signer: "",
				},
			},
			want:    &gethtypes.Transaction{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.TxChannelOpenInit(tt.args.opts, tt.args.msg)
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.TxChannelOpenInit() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.TxChannelOpenInit() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Chain.TxChannelOpenInit() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

// TODO ibcHandler が TxChannelOpenTry を実行してしまうため実施不可
func TestChain_TxChannelOpenTry(t *testing.T) {
	t.Skip("cannot not possible because ibcHandler executes TxChannelOpenInit.")

	var stubChainConfig = ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	var stubPathEnd = &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		opts *bind.TransactOpts
		msg  *chantypes.MsgChannelOpenTry
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *gethtypes.Transaction
		wantErr bool
	}{
		{
			name: "should TxChannelOpenTry.",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				opts: &bind.TransactOpts{
					From:      common.Address{},
					Nonce:     &big.Int{},
					Signer:    nil,
					Value:     &big.Int{},
					GasPrice:  &big.Int{},
					GasFeeCap: &big.Int{},
					GasTipCap: &big.Int{},
					GasLimit:  0,
					Context:   nil,
					NoSend:    false,
				},
				msg: &chantypes.MsgChannelOpenTry{
					PortId:            "",
					PreviousChannelId: "",
					Channel: chantypes.Channel{
						State:    0,
						Ordering: 0,
						Counterparty: chantypes.Counterparty{
							PortId:    "",
							ChannelId: "",
						},
						ConnectionHops: nil,
						Version:        "",
					},
					CounterpartyVersion: "",
					ProofInit:           nil,
					ProofHeight: clienttypes.Height{
						RevisionNumber: 0,
						RevisionHeight: 0,
					},
					Signer: "",
				},
			},
			want:    &gethtypes.Transaction{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.TxChannelOpenTry(tt.args.opts, tt.args.msg)
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.TxChannelOpenTry() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.TxChannelOpenTry() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Chain.TxChannelOpenTry() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

// TODO ibcHandler が TxChannelOpenAck を実行してしまうため実施不可
func TestChain_TxChannelOpenAck(t *testing.T) {
	t.Skip("cannot not possible because ibcHandler executes TxChannelOpenAck.")

	var stubChainConfig = ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	var stubPathEnd = &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		opts *bind.TransactOpts
		msg  *chantypes.MsgChannelOpenAck
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *gethtypes.Transaction
		wantErr bool
	}{
		{
			name: "should TxChannelOpenAck.",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				opts: &bind.TransactOpts{
					From:      common.Address{},
					Nonce:     &big.Int{},
					Signer:    nil,
					Value:     &big.Int{},
					GasPrice:  &big.Int{},
					GasFeeCap: &big.Int{},
					GasTipCap: &big.Int{},
					GasLimit:  0,
					Context:   nil,
					NoSend:    false,
				},
				msg: &chantypes.MsgChannelOpenAck{
					PortId:                "",
					ChannelId:             "",
					CounterpartyChannelId: "",
					CounterpartyVersion:   "",
					ProofTry:              nil,
					ProofHeight: clienttypes.Height{
						RevisionNumber: 0,
						RevisionHeight: 0,
					},
					Signer: "",
				},
			},
			want:    &gethtypes.Transaction{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.TxChannelOpenAck(tt.args.opts, tt.args.msg)
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.TxChannelOpenAck() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.TxChannelOpenAck() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Chain.TxChannelOpenAck() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

// TODO ibcHandler が TxChannelOpenConfirm を実行してしまうため実施不可
func TestChain_TxChannelOpenConfirm(t *testing.T) {
	t.Skip("cannot not possible because ibcHandler executes TxChannelOpenAck.")

	var stubChainConfig = ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	var stubPathEnd = &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		opts *bind.TransactOpts
		msg  *chantypes.MsgChannelOpenConfirm
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *gethtypes.Transaction
		wantErr bool
	}{
		{
			name: "should TxChannelOpenConfirm.",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				opts: &bind.TransactOpts{
					From:      common.Address{},
					Nonce:     &big.Int{},
					Signer:    nil,
					Value:     &big.Int{},
					GasPrice:  &big.Int{},
					GasFeeCap: &big.Int{},
					GasTipCap: &big.Int{},
					GasLimit:  0,
					Context:   nil,
					NoSend:    false,
				},
				msg: &chantypes.MsgChannelOpenConfirm{
					PortId:    "",
					ChannelId: "",
					ProofAck:  nil,
					ProofHeight: clienttypes.Height{
						RevisionNumber: 0,
						RevisionHeight: 0,
					},
					Signer: "",
				},
			},
			want:    &gethtypes.Transaction{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.TxChannelOpenConfirm(tt.args.opts, tt.args.msg)
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.TxChannelOpenConfirm() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.TxChannelOpenConfirm() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Chain.TxChannelOpenConfirm() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

// TODO ibcHandler が TxRecvPacket を実行してしまうため実施不可
func TestChain_TxRecvPacket(t *testing.T) {
	t.Skip("cannot not possible because ibcHandler executes TxRecvPacket.")

	var stubChainConfig = ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	var stubPathEnd = &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		opts *bind.TransactOpts
		msg  *chantypes.MsgRecvPacket
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *gethtypes.Transaction
		wantErr bool
	}{
		{
			name: "should TxRecvPacket",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				opts: &bind.TransactOpts{
					From:      common.Address{},
					Nonce:     &big.Int{},
					Signer:    nil,
					Value:     &big.Int{},
					GasPrice:  &big.Int{},
					GasFeeCap: &big.Int{},
					GasTipCap: &big.Int{},
					GasLimit:  0,
					Context:   nil,
					NoSend:    false,
				},
				msg: &chantypes.MsgRecvPacket{
					Packet: chantypes.Packet{
						Sequence:           0,
						SourcePort:         "",
						SourceChannel:      "",
						DestinationPort:    "",
						DestinationChannel: "",
						Data:               nil,
						TimeoutHeight: clienttypes.Height{
							RevisionNumber: 0,
							RevisionHeight: 0,
						},
						TimeoutTimestamp: 0,
					},
					ProofCommitment: nil,
					ProofHeight: clienttypes.Height{
						RevisionNumber: 0,
						RevisionHeight: 0,
					},
					Signer: "",
				},
			},
			want:    &gethtypes.Transaction{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.TxRecvPacket(tt.args.opts, tt.args.msg)
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.TxRecvPacket() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.TxRecvPacket() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Chain.TxRecvPacket() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

// TODO ibcHandler が TxAcknowledgement を実行してしまうため実施不可
func TestChain_TxAcknowledgement(t *testing.T) {
	t.Skip("cannot not possible because ibcHandler executes TxAcknowledgement.")

	var stubChainConfig = ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	var stubPathEnd = &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		opts *bind.TransactOpts
		msg  *chantypes.MsgAcknowledgement
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *gethtypes.Transaction
		wantErr bool
	}{
		{
			name: "should TxAcknowledgement.",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				opts: &bind.TransactOpts{
					From:      common.Address{},
					Nonce:     &big.Int{},
					Signer:    nil,
					Value:     &big.Int{},
					GasPrice:  &big.Int{},
					GasFeeCap: &big.Int{},
					GasTipCap: &big.Int{},
					GasLimit:  0,
					Context:   nil,
					NoSend:    false,
				},
				msg: &chantypes.MsgAcknowledgement{
					Packet: chantypes.Packet{
						Sequence:           0,
						SourcePort:         "",
						SourceChannel:      "",
						DestinationPort:    "",
						DestinationChannel: "",
						Data:               nil,
						TimeoutHeight: clienttypes.Height{
							RevisionNumber: 0,
							RevisionHeight: 0,
						},
						TimeoutTimestamp: 0,
					},
					Acknowledgement: nil,
					ProofAcked:      nil,
					ProofHeight: clienttypes.Height{
						RevisionNumber: 0,
						RevisionHeight: 0,
					},
					Signer: "",
				},
			},
			want:    &gethtypes.Transaction{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.TxAcknowledgement(tt.args.opts, tt.args.msg)
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.TxAcknowledgement() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.TxAcknowledgement() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Chain.TxAcknowledgement() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

// TODO ibcHandler が いずれかの Tx を実行してしまうため実施不可
func TestChain_SendTx(t *testing.T) {
	t.Skip("cannot not possible because ibcHandler executes any Tx.")

	var stubChainConfig = ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	var stubPathEnd = &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}

	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		opts                       *bind.TransactOpts
		msg                        sdk.Msg
		skipUpdateClientCommitment bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *gethtypes.Transaction
		wantErr bool
	}{
		{
			name: "should SendTx.",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				opts: &bind.TransactOpts{
					From:      common.Address{},
					Nonce:     &big.Int{},
					Signer:    nil,
					Value:     &big.Int{},
					GasPrice:  &big.Int{},
					GasFeeCap: &big.Int{},
					GasTipCap: &big.Int{},
					GasLimit:  0,
					Context:   nil,
					NoSend:    false,
				},
				msg:                        nil,
				skipUpdateClientCommitment: false,
			},
			want:    &gethtypes.Transaction{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.SendTx(tt.args.opts, tt.args.msg, tt.args.skipUpdateClientCommitment)
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.SendTx() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Chain.SendTx() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Chain.SendTx() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

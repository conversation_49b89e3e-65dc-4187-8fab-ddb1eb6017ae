//go:generate mockgen -source=$GOFILE -package=ethereum -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package ethereum

import (
	"math/big"
	"testing"

	"github.com/cosmos/cosmos-sdk/codec"
	codectypes "github.com/cosmos/cosmos-sdk/codec/types"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/client"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/contract/ibchandler"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/google/go-cmp/cmp"
)

func Test_checkpointFileName(t *testing.T) {
	type args struct {
		cpType checkpointType
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test checkpoint send file name",
			args: args{
				cpType: sendCheckpoint,
			},
			want: "send.cp",
		},
		{
			name: "test checkpoint recv file name",
			args: args{
				cpType: recvCheckpoint,
			},
			want: "recv.cp",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := checkpointFileName(tt.args.cpType); got != tt.want {
				t.Errorf("checkpointFileName() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChain_ensureDataDirectory(t *testing.T) {
	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	tests := []struct {
		name    string
		fields  fields
		want    string
		wantErr bool
	}{
		{
			name: "error ensureDataDirectory",
			fields: fields{
				config: ChainConfig{
					ChainId:    "********",
					EthChainId: 5151,
					RpcAddr:    "http://**********",
					Signer: &codectypes.Any{
						TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					IbcAddress:            "******************************************",
					InitialSendCheckpoint: 1,
					InitialRecvCheckpoint: 1,
					EnableDebugTrace:      false,
					AverageBlockTimeMsec:  2000,
					MaxRetryForInclusion:  3,
					GasEstimateRate: &Fraction{
						Numerator:   1,
						Denominator: 1,
					},
					MaxGasLimit: *********,
					TxType:      "auto",
				},
				pathEnd: &core.PathEnd{
					ChainID:      "********",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				homePath:         "/dev/null",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "test ensureDataDirectory",
			fields: fields{
				config: ChainConfig{
					ChainId:    "********",
					EthChainId: 5151,
					RpcAddr:    "http://**********",
					Signer: &codectypes.Any{
						TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					IbcAddress:            "******************************************",
					InitialSendCheckpoint: 1,
					InitialRecvCheckpoint: 1,
					EnableDebugTrace:      false,
					AverageBlockTimeMsec:  2000,
					MaxRetryForInclusion:  3,
					GasEstimateRate: &Fraction{
						Numerator:   1,
						Denominator: 1,
					},
					MaxGasLimit: *********,
					TxType:      "auto",
				},
				pathEnd: &core.PathEnd{
					ChainID:      "********",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				homePath:         ".",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			want:    "ethereum/********",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.ensureDataDirectory()
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.ensureDataDirectory() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Chain.ensureDataDirectory() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

func TestChain_loadCheckpoint(t *testing.T) {
	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		cpType checkpointType
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint64
		wantErr bool
	}{
		{
			name: "error loadCheckpoint file name",
			fields: fields{
				config: ChainConfig{
					ChainId:    "********",
					EthChainId: 5151,
					RpcAddr:    "http://**********",
					Signer: &codectypes.Any{
						TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					IbcAddress:            "******************************************",
					InitialSendCheckpoint: 1,
					InitialRecvCheckpoint: 1,
					EnableDebugTrace:      false,
					AverageBlockTimeMsec:  2000,
					MaxRetryForInclusion:  3,
					GasEstimateRate: &Fraction{
						Numerator:   1,
						Denominator: 1,
					},
					MaxGasLimit: *********,
					TxType:      "auto",
				},
				pathEnd: &core.PathEnd{
					ChainID:      "********",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				homePath:         "/dev/null",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				cpType: sendCheckpoint,
			},
			want:    0,
			wantErr: true,
		},
		{
			name: "test loadCheckpoint send file name",
			fields: fields{
				config: ChainConfig{
					ChainId:    "********",
					EthChainId: 5151,
					RpcAddr:    "http://**********",
					Signer: &codectypes.Any{
						TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					IbcAddress:            "******************************************",
					InitialSendCheckpoint: 1,
					InitialRecvCheckpoint: 1,
					EnableDebugTrace:      false,
					AverageBlockTimeMsec:  2000,
					MaxRetryForInclusion:  3,
					GasEstimateRate: &Fraction{
						Numerator:   1,
						Denominator: 1,
					},
					MaxGasLimit: *********,
					TxType:      "auto",
				},
				pathEnd: &core.PathEnd{
					ChainID:      "********",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				homePath:         ".",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				cpType: sendCheckpoint,
			},
			want:    1,
			wantErr: false,
		},
		{
			name: "test loadCheckpoint recv file name",
			fields: fields{
				config: ChainConfig{
					ChainId:    "********",
					EthChainId: 5151,
					RpcAddr:    "http://**********",
					Signer: &codectypes.Any{
						TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					IbcAddress:            "******************************************",
					InitialSendCheckpoint: 1,
					InitialRecvCheckpoint: 1,
					EnableDebugTrace:      false,
					AverageBlockTimeMsec:  2000,
					MaxRetryForInclusion:  3,
					GasEstimateRate: &Fraction{
						Numerator:   1,
						Denominator: 1,
					},
					MaxGasLimit: *********,
					TxType:      "auto",
				},
				pathEnd: &core.PathEnd{
					ChainID:      "********",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				homePath:         ".",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				cpType: recvCheckpoint,
			},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got, err := c.loadCheckpoint(tt.args.cpType)
			if (err != nil) != tt.wantErr {
				t.Errorf("Chain.loadCheckpoint() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("Chain.loadCheckpoint() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChain_saveCheckpoint(t *testing.T) {
	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	type args struct {
		v      uint64
		cpType checkpointType
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "error saveCheckpoint file",
			fields: fields{
				config: ChainConfig{
					ChainId:    "********",
					EthChainId: 5151,
					RpcAddr:    "http://**********",
					Signer: &codectypes.Any{
						TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					IbcAddress:            "******************************************",
					InitialSendCheckpoint: 1,
					InitialRecvCheckpoint: 1,
					EnableDebugTrace:      false,
					AverageBlockTimeMsec:  2000,
					MaxRetryForInclusion:  3,
					GasEstimateRate: &Fraction{
						Numerator:   1,
						Denominator: 1,
					},
					MaxGasLimit: *********,
					TxType:      "auto",
				},
				pathEnd: &core.PathEnd{
					ChainID:      "********",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				homePath:         "/dev/null",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				v:      0,
				cpType: recvCheckpoint,
			},
			wantErr: true,
		},
		{
			name: "test saveCheckpoint recv file",
			fields: fields{
				config: ChainConfig{
					ChainId:    "********",
					EthChainId: 5151,
					RpcAddr:    "http://**********",
					Signer: &codectypes.Any{
						TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					IbcAddress:            "******************************************",
					InitialSendCheckpoint: 1,
					InitialRecvCheckpoint: 1,
					EnableDebugTrace:      false,
					AverageBlockTimeMsec:  2000,
					MaxRetryForInclusion:  3,
					GasEstimateRate: &Fraction{
						Numerator:   1,
						Denominator: 1,
					},
					MaxGasLimit: *********,
					TxType:      "auto",
				},
				pathEnd: &core.PathEnd{
					ChainID:      "********",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				homePath:         ".",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				v:      0,
				cpType: recvCheckpoint,
			},
			wantErr: false,
		},
		{
			name: "test saveCheckpoint send file",
			fields: fields{
				config: ChainConfig{
					ChainId:    "********",
					EthChainId: 5151,
					RpcAddr:    "http://**********",
					Signer: &codectypes.Any{
						TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					IbcAddress:            "******************************************",
					InitialSendCheckpoint: 1,
					InitialRecvCheckpoint: 1,
					EnableDebugTrace:      false,
					AverageBlockTimeMsec:  2000,
					MaxRetryForInclusion:  3,
					GasEstimateRate: &Fraction{
						Numerator:   1,
						Denominator: 1,
					},
					MaxGasLimit: *********,
					TxType:      "auto",
				},
				pathEnd: &core.PathEnd{
					ChainID:      "********",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				homePath:         ".",
				chainID:          &big.Int{},
				codec:            nil,
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			args: args{
				v:      0,
				cpType: sendCheckpoint,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			if err := c.saveCheckpoint(tt.args.v, tt.args.cpType); (err != nil) != tt.wantErr {
				t.Errorf("Chain.saveCheckpoint() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

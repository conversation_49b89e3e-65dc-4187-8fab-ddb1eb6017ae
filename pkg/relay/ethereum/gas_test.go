//go:generate mockgen -source=$GOFILE -package=ethereum -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package ethereum

import (
	"context"
	"math/big"
	"reflect"
	"testing"

	codectypes "github.com/cosmos/cosmos-sdk/codec/types"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/client"
	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/google/go-cmp/cmp"
)

func TestNewGasFeeCalculator(t *testing.T) {
	type args struct {
		client *client.ETHClient
		config *ChainConfig
	}
	tests := []struct {
		name string
		args args
		want *GasFeeCalculator
	}{
		{
			name: "should create gas fee calculator.",
			args: args{
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				config: &ChainConfig{
					ChainId:    "********",
					EthChainId: 5151,
					RpcAddr:    "http://**********",
					Signer: &codectypes.Any{
						TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					IbcAddress:            "******************************************",
					InitialSendCheckpoint: 1,
					InitialRecvCheckpoint: 1,
					EnableDebugTrace:      false,
					AverageBlockTimeMsec:  2000,
					MaxRetryForInclusion:  3,
					GasEstimateRate: &Fraction{
						Numerator:   1,
						Denominator: 1,
					},
					MaxGasLimit: 200000000,
					TxType:      "auto",
				},
			},
			want: &GasFeeCalculator{
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				config: &ChainConfig{
					ChainId:    "********",
					EthChainId: 5151,
					RpcAddr:    "http://**********",
					Signer: &codectypes.Any{
						TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					IbcAddress:            "******************************************",
					InitialSendCheckpoint: 1,
					InitialRecvCheckpoint: 1,
					EnableDebugTrace:      false,
					AverageBlockTimeMsec:  2000,
					MaxRetryForInclusion:  3,
					GasEstimateRate: &Fraction{
						Numerator:   1,
						Denominator: 1,
					},
					MaxGasLimit: 200000000,
					TxType:      "auto",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewGasFeeCalculator(tt.args.client, tt.args.config); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewGasFeeCalculator() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGasFeeCalculator_Apply(t *testing.T) {
	type fields struct {
		client *client.ETHClient
		config *ChainConfig
	}
	type args struct {
		ctx    context.Context
		txOpts *bind.TransactOpts
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "should call gas fee calculator.",
			fields: fields{
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				config: &ChainConfig{
					ChainId:    "********",
					EthChainId: 5151,
					RpcAddr:    "http://**********",
					Signer: &codectypes.Any{
						TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
						Value:                nil,
						XXX_NoUnkeyedLiteral: struct{}{},
						XXX_unrecognized:     nil,
						XXX_sizecache:        0,
					},
					IbcAddress:            "******************************************",
					InitialSendCheckpoint: 1,
					InitialRecvCheckpoint: 1,
					EnableDebugTrace:      false,
					AverageBlockTimeMsec:  2000,
					MaxRetryForInclusion:  3,
					GasEstimateRate: &Fraction{
						Numerator:   1,
						Denominator: 1,
					},
					MaxGasLimit: 200000000,
					TxType:      "auto",
				},
			},
			args: args{
				ctx: nil,
				txOpts: &bind.TransactOpts{
					From:      common.Address{},
					Nonce:     &big.Int{},
					Signer:    nil,
					Value:     &big.Int{},
					GasPrice:  &big.Int{},
					GasFeeCap: &big.Int{},
					GasTipCap: &big.Int{},
					GasLimit:  0,
					Context:   nil,
					NoSend:    false,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &GasFeeCalculator{
				client: tt.fields.client,
				config: tt.fields.config,
			}
			if err := m.Apply(tt.args.ctx, tt.args.txOpts); (err != nil) != tt.wantErr {
				t.Errorf("GasFeeCalculator.Apply() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_getFeeInfo(t *testing.T) {

	type args struct {
		v *ethereum.FeeHistory
	}
	tests := []struct {
		name  string
		args  args
		want  *big.Int
		want1 *big.Int
		want2 bool
	}{
		{
			name: "Should return nil because Reward's length is 0.",
			args: args{
				v: &ethereum.FeeHistory{
					OldestBlock:  big.NewInt(9223372036854775807),
					Reward:       [][]*big.Int{},
					BaseFee:      nil,
					GasUsedRatio: nil,
				},
			},
			want:  nil,
			want1: nil,
			want2: false,
		},
		{
			name: "Should return nil because BaseFee len is 0.",
			args: args{
				v: &ethereum.FeeHistory{
					OldestBlock: big.NewInt(9223372036854775807),
					Reward: [][]*big.Int{
						{big.NewInt(9223372036854775807)},
						{big.NewInt(9223372036854775807)},
					},
					BaseFee:      []*big.Int{},
					GasUsedRatio: nil,
				},
			},
			want:  nil,
			want1: nil,
			want2: false,
		},
		{
			name: "Should return FeeInfo.",
			args: args{
				v: &ethereum.FeeHistory{
					OldestBlock: big.NewInt(9223372036854775807),
					Reward: [][]*big.Int{
						{big.NewInt(1)},
						{big.NewInt(9223372036854775807)},
					},
					BaseFee:      []*big.Int{big.NewInt(2)},
					GasUsedRatio: []float64{float64(3.14159265358979323846)},
				},
			},
			want:  big.NewInt(2).SetUint64(1),
			want1: big.NewInt(2).SetUint64(2),
			want2: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, got2 := getFeeInfo(tt.args.v)
			opt := cmp.AllowUnexported(big.Int{})
			if !reflect.DeepEqual(got, tt.want) {
				if diff := cmp.Diff(got, tt.want, opt); diff != "" {
					t.Errorf("getFeeInfo differs: (-got +want)n%s", diff)
				}
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				if diff := cmp.Diff(got1, tt.want1, opt); diff != "" {
					t.Errorf("getFeeInfo differs: (-got1 +want1)n%s", diff)
				}
			}
			if got2 != tt.want2 {
				if diff := cmp.Diff(got2, tt.want2); diff != "" {
					t.Errorf("getFeeInfo differs: (-got2 +want2)n%s", diff)
				}
			}
		})
	}
}

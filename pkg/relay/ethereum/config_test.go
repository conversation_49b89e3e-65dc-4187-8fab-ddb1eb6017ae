//go:generate mockgen -source=$GOFILE -package=ethereum -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package ethereum

import (
	"math/big"
	"reflect"
	"testing"

	codectypes "github.com/cosmos/cosmos-sdk/codec/types"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
	"github.com/ethereum/go-ethereum/common"
	"github.com/google/go-cmp/cmp"
)

func TestChainConfig_Build(t *testing.T) {
	t.Skip("cannot not possible because SignerConfig dependency.")
	type fields struct {
		ChainId               string
		EthChainId            uint64
		RpcAddr               string
		Signer                *codectypes.Any
		IbcAddress            string
		InitialSendCheckpoint uint64
		InitialRecvCheckpoint uint64
		EnableDebugTrace      bool
		AverageBlockTimeMsec  uint64
		MaxRetryForInclusion  uint64
		GasEstimateRate       *Fraction
		MaxGasLimit           uint64
		TxType                string
	}
	tests := []struct {
		name    string
		fields  fields
		want    core.Chain
		wantErr bool
	}{
		{
			name: "should build chain.",
			fields: fields{
				ChainId:    "51500001",
				EthChainId: 5151,
				RpcAddr:    "",
				Signer: &codectypes.Any{
					TypeUrl:              "http://**********:8451",
					Value:                nil,
					XXX_NoUnkeyedLiteral: struct{}{},
					XXX_unrecognized:     nil,
					XXX_sizecache:        0,
				},
				IbcAddress:            "******************************************",
				InitialSendCheckpoint: 1,
				InitialRecvCheckpoint: 1,
				EnableDebugTrace:      false,
				AverageBlockTimeMsec:  2000,
				MaxRetryForInclusion:  3,
				GasEstimateRate: &Fraction{
					Numerator:   1,
					Denominator: 1,
				},
				MaxGasLimit: 200000000,
				TxType:      "auto",
			},
			want:    &Chain{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := ChainConfig{
				ChainId:               tt.fields.ChainId,
				EthChainId:            tt.fields.EthChainId,
				RpcAddr:               tt.fields.RpcAddr,
				Signer:                tt.fields.Signer,
				IbcAddress:            tt.fields.IbcAddress,
				InitialSendCheckpoint: tt.fields.InitialSendCheckpoint,
				InitialRecvCheckpoint: tt.fields.InitialRecvCheckpoint,
				EnableDebugTrace:      tt.fields.EnableDebugTrace,
				AverageBlockTimeMsec:  tt.fields.AverageBlockTimeMsec,
				MaxRetryForInclusion:  tt.fields.MaxRetryForInclusion,
				GasEstimateRate:       tt.fields.GasEstimateRate,
				MaxGasLimit:           tt.fields.MaxGasLimit,
				TxType:                tt.fields.TxType,
			}
			got, err := c.Build()
			if (err != nil) != tt.wantErr {
				t.Errorf("ChainConfig.Build() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ChainConfig.Build() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("ChainConfig.Build() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

func TestChainConfig_Validate(t *testing.T) {
	t.Skip("cannot not possible because SignerConfig dependency.")

	type fields struct {
		ChainId               string
		EthChainId            uint64
		RpcAddr               string
		Signer                *codectypes.Any
		IbcAddress            string
		InitialSendCheckpoint uint64
		InitialRecvCheckpoint uint64
		EnableDebugTrace      bool
		AverageBlockTimeMsec  uint64
		MaxRetryForInclusion  uint64
		GasEstimateRate       *Fraction
		MaxGasLimit           uint64
		TxType                string
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "should validate chain.",
			fields: fields{
				ChainId:    "51500001",
				EthChainId: 5151,
				RpcAddr:    "",
				Signer: &codectypes.Any{
					TypeUrl:              "http://**********:8451",
					Value:                nil,
					XXX_NoUnkeyedLiteral: struct{}{},
					XXX_unrecognized:     nil,
					XXX_sizecache:        0,
				},
				IbcAddress:            "******************************************",
				InitialSendCheckpoint: 1,
				InitialRecvCheckpoint: 1,
				EnableDebugTrace:      false,
				AverageBlockTimeMsec:  2000,
				MaxRetryForInclusion:  3,
				GasEstimateRate: &Fraction{
					Numerator:   1,
					Denominator: 1,
				},
				MaxGasLimit: 200000000,
				TxType:      "auto",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := ChainConfig{
				ChainId:               tt.fields.ChainId,
				EthChainId:            tt.fields.EthChainId,
				RpcAddr:               tt.fields.RpcAddr,
				Signer:                tt.fields.Signer,
				IbcAddress:            tt.fields.IbcAddress,
				InitialSendCheckpoint: tt.fields.InitialSendCheckpoint,
				InitialRecvCheckpoint: tt.fields.InitialRecvCheckpoint,
				EnableDebugTrace:      tt.fields.EnableDebugTrace,
				AverageBlockTimeMsec:  tt.fields.AverageBlockTimeMsec,
				MaxRetryForInclusion:  tt.fields.MaxRetryForInclusion,
				GasEstimateRate:       tt.fields.GasEstimateRate,
				MaxGasLimit:           tt.fields.MaxGasLimit,
				TxType:                tt.fields.TxType,
			}
			if err := c.Validate(); (err != nil) != tt.wantErr {
				t.Errorf("ChainConfig.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestFraction_Validate(t *testing.T) {
	type fields struct {
		Numerator   uint64
		Denominator uint64
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "should return zero is invalid fraction denominator error.",
			fields: fields{
				Numerator:   0,
				Denominator: 0,
			},
			wantErr: true,
		},
		{
			name: "should validate numerator and denominator.",
			fields: fields{
				Numerator:   100,
				Denominator: 100,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			f := Fraction{
				Numerator:   tt.fields.Numerator,
				Denominator: tt.fields.Denominator,
			}
			if err := f.Validate(); (err != nil) != tt.wantErr {
				t.Errorf("Fraction.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestFraction_Mul(t *testing.T) {
	type fields struct {
		Numerator   uint64
		Denominator uint64
	}
	type args struct {
		n *big.Int
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "should calculate numerator and denominator.",
			fields: fields{
				Numerator:   100,
				Denominator: 100,
			},
			args: args{
				n: big.NewInt(100),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			f := Fraction{
				Numerator:   tt.fields.Numerator,
				Denominator: tt.fields.Denominator,
			}
			f.Mul(tt.args.n)
		})
	}
}

func TestChainConfig_UnpackInterfaces(t *testing.T) {
	t.Skip("cannot not possible because SignerConfig dependency.")

	type fields struct {
		ChainId               string
		EthChainId            uint64
		RpcAddr               string
		Signer                *codectypes.Any
		IbcAddress            string
		InitialSendCheckpoint uint64
		InitialRecvCheckpoint uint64
		EnableDebugTrace      bool
		AverageBlockTimeMsec  uint64
		MaxRetryForInclusion  uint64
		GasEstimateRate       *Fraction
		MaxGasLimit           uint64
		TxType                string
	}
	type args struct {
		unpacker codectypes.AnyUnpacker
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "should Unpack Interfaces.",
			fields: fields{
				ChainId:    "51500001",
				EthChainId: 5151,
				RpcAddr:    "",
				Signer: &codectypes.Any{
					TypeUrl:              "http://**********:8451",
					Value:                nil,
					XXX_NoUnkeyedLiteral: struct{}{},
					XXX_unrecognized:     nil,
					XXX_sizecache:        0,
				},
				IbcAddress:            "******************************************",
				InitialSendCheckpoint: 1,
				InitialRecvCheckpoint: 1,
				EnableDebugTrace:      false,
				AverageBlockTimeMsec:  2000,
				MaxRetryForInclusion:  3,
				GasEstimateRate: &Fraction{
					Numerator:   1,
					Denominator: 1,
				},
				MaxGasLimit: 200000000,
				TxType:      "auto",
			},
			args: args{
				unpacker: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := ChainConfig{
				ChainId:               tt.fields.ChainId,
				EthChainId:            tt.fields.EthChainId,
				RpcAddr:               tt.fields.RpcAddr,
				Signer:                tt.fields.Signer,
				IbcAddress:            tt.fields.IbcAddress,
				InitialSendCheckpoint: tt.fields.InitialSendCheckpoint,
				InitialRecvCheckpoint: tt.fields.InitialRecvCheckpoint,
				EnableDebugTrace:      tt.fields.EnableDebugTrace,
				AverageBlockTimeMsec:  tt.fields.AverageBlockTimeMsec,
				MaxRetryForInclusion:  tt.fields.MaxRetryForInclusion,
				GasEstimateRate:       tt.fields.GasEstimateRate,
				MaxGasLimit:           tt.fields.MaxGasLimit,
				TxType:                tt.fields.TxType,
			}
			if err := c.UnpackInterfaces(tt.args.unpacker); (err != nil) != tt.wantErr {
				t.Errorf("ChainConfig.UnpackInterfaces() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestChainConfig_IBCAddress(t *testing.T) {
	type fields struct {
		ChainId               string
		EthChainId            uint64
		RpcAddr               string
		Signer                *codectypes.Any
		IbcAddress            string
		InitialSendCheckpoint uint64
		InitialRecvCheckpoint uint64
		EnableDebugTrace      bool
		AverageBlockTimeMsec  uint64
		MaxRetryForInclusion  uint64
		GasEstimateRate       *Fraction
		MaxGasLimit           uint64
		TxType                string
	}
	tests := []struct {
		name   string
		fields fields
		want   common.Address
	}{
		{
			name: "should get IBC address",
			fields: fields{
				ChainId:    "51500001",
				EthChainId: 5151,
				RpcAddr:    "",
				Signer: &codectypes.Any{
					TypeUrl:              "http://**********:8451",
					Value:                nil,
					XXX_NoUnkeyedLiteral: struct{}{},
					XXX_unrecognized:     nil,
					XXX_sizecache:        0,
				},
				IbcAddress:            "******************************************",
				InitialSendCheckpoint: 1,
				InitialRecvCheckpoint: 1,
				EnableDebugTrace:      false,
				AverageBlockTimeMsec:  2000,
				MaxRetryForInclusion:  3,
				GasEstimateRate: &Fraction{
					Numerator:   1,
					Denominator: 1,
				},
				MaxGasLimit: 200000000,
				TxType:      "auto",
			},
			want: common.Address{
				0x82, 0xe0, 0x12, 0x23, 0xd5, 0x1e, 0xb8, 0x7e, 0x16, 0xa0, 0x3e, 0x24, 0x68, 0x7e, 0xdf, 0x0f, 0x29, 0x4d, 0xa6, 0xf1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := ChainConfig{
				ChainId:               tt.fields.ChainId,
				EthChainId:            tt.fields.EthChainId,
				RpcAddr:               tt.fields.RpcAddr,
				Signer:                tt.fields.Signer,
				IbcAddress:            tt.fields.IbcAddress,
				InitialSendCheckpoint: tt.fields.InitialSendCheckpoint,
				InitialRecvCheckpoint: tt.fields.InitialRecvCheckpoint,
				EnableDebugTrace:      tt.fields.EnableDebugTrace,
				AverageBlockTimeMsec:  tt.fields.AverageBlockTimeMsec,
				MaxRetryForInclusion:  tt.fields.MaxRetryForInclusion,
				GasEstimateRate:       tt.fields.GasEstimateRate,
				MaxGasLimit:           tt.fields.MaxGasLimit,
				TxType:                tt.fields.TxType,
			}
			if got := c.IBCAddress(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ChainConfig.IBCAddress() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("ChainConfig.IBCAddress() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

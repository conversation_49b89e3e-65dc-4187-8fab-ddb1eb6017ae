//go:generate mockgen -source=$GOFILE -package=ethereum -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package ethereum

// TODO パラメータ用の Singer生成時に import cycle not allowed in test となるためテスト不可
//func TestChain_CallOpts(t *testing.T) {
//
//	//mockSignerConfig := SignerConfig{
//	//	Mnemonic: "math razor capable expose worth grape metal sunset metal sudden usage scheme",
//	//	Path:     "m/44'/60'/0'/0/0",
//	//}
//	mockChainConfig := ChainConfig{
//		ChainId:    "********",
//		EthChainId: 5151,
//		RpcAddr:    "http://**********:8451",
//		Signer: &codectypes.Any{
//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//			Value:                nil,
//			XXX_NoUnkeyedLiteral: struct{}{},
//			XXX_unrecognized:     nil,
//			XXX_sizecache:        0,
//		},
//		IbcAddress:            "******************************************",
//		InitialSendCheckpoint: 1,
//		InitialRecvCheckpoint: 1,
//		EnableDebugTrace:      false,
//		AverageBlockTimeMsec:  2000,
//		MaxRetryForInclusion:  3,
//		GasEstimateRate: &Fraction{
//			Numerator:   1,
//			Denominator: 1,
//		},
//		MaxGasLimit: *********,
//		TxType:      "auto",
//	}
//	//mockSigner, _ := mockSignerConfig.Build(big.NewInt(int64(5151)))
//
//	type fields struct {
//		config                    ChainConfig
//		pathEnd                   *core.PathEnd
//		homePath                  string
//		chainID                   *big.Int
//		codec                     codec.ProtoCodecMarshaler
//		msgEventListener          core.MsgEventListener
//		client                    *client.ETHClient
//		ibcHandler                *ibchandler.Ibchandler
//		signer                    Signer
//		connectionOpenedConfirmed bool
//	}
//	type args struct {
//		ctx    context.Context
//		height int64
//	}
//	tests := []struct {
//		name   string
//		fields fields
//		args   args
//		want   *bind.CallOpts
//	}{
//		{
//			name: "should returns the CallOpts when hight is 0.",
//			fields: fields{
//				config: mockChainConfig,
//				pathEnd: &core.PathEnd{
//					ChainID:      "********",
//					ClientID:     "hb-ibft2-0",
//					ConnectionID: "connection-0",
//					ChannelID:    "channel-0",
//					PortID:       "account-sync",
//					Order:        "unordered",
//					Version:      "account-sync-1",
//				},
//				homePath:         ".",
//				chainID:          &big.Int{},
//				codec:            nil,
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			args: args{
//				ctx:    context.TODO(),
//				height: 0,
//			},
//			want: &bind.CallOpts{
//				Pending:     false,
//				From:        common.Address{},
//				BlockNumber: &big.Int{},
//				Context:     context.TODO(),
//			},
//		},
//		{
//			name: "should returns the CallOpts where hight is 1 or greater.",
//			fields: fields{
//				config: mockChainConfig,
//				pathEnd: &core.PathEnd{
//					ChainID:      "********",
//					ClientID:     "hb-ibft2-0",
//					ConnectionID: "connection-0",
//					ChannelID:    "channel-0",
//					PortID:       "account-sync",
//					Order:        "unordered",
//					Version:      "account-sync-1",
//				},
//				homePath:         ".",
//				chainID:          big.NewInt(int64(5151)),
//				codec:            nil,
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			args: args{
//				ctx:    context.TODO(),
//				height: 10,
//			},
//			want: &bind.CallOpts{
//				Pending:     false,
//				From:        common.Address{},
//				BlockNumber: &big.Int{},
//				Context:     context.TODO(),
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			chain := &Chain{
//				config:                    tt.fields.config,
//				pathEnd:                   tt.fields.pathEnd,
//				homePath:                  tt.fields.homePath,
//				chainID:                   tt.fields.chainID,
//				codec:                     tt.fields.codec,
//				msgEventListener:          tt.fields.msgEventListener,
//				client:                    tt.fields.client,
//				ibcHandler:                tt.fields.ibcHandler,
//				signer:                    tt.fields.signer,
//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
//			}
//			if got := chain.CallOpts(tt.args.ctx, tt.args.height); !reflect.DeepEqual(got, tt.want) {
//				if diff := cmp.Diff(got, tt.want); diff != "" {
//					t.Errorf("Chain.CallOpts() differs: (-got +want)n%s", diff)
//				}
//			}
//		})
//	}
//}
//
//// TODO パラメータ用の Singer生成時に import cycle not allowed in test となるためテスト不可
//func TestChain_TxOpts(t *testing.T) {
//	type fields struct {
//		config                    ChainConfig
//		pathEnd                   *core.PathEnd
//		homePath                  string
//		chainID                   *big.Int
//		codec                     codec.ProtoCodecMarshaler
//		msgEventListener          core.MsgEventListener
//		client                    *client.ETHClient
//		ibcHandler                *ibchandler.Ibchandler
//		signer                    Signer
//		connectionOpenedConfirmed bool
//	}
//	type args struct {
//		ctx context.Context
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *bind.TransactOpts
//		wantErr bool
//	}{
//		{
//			name: "should returns the TxOpts",
//			fields: fields{
//				config: ChainConfig{
//					ChainId:    "********",
//					EthChainId: 5151,
//					RpcAddr:    "http://**********:8451",
//					Signer: &codectypes.Any{
//						TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
//						Value:                nil,
//						XXX_NoUnkeyedLiteral: struct{}{},
//						XXX_unrecognized:     nil,
//						XXX_sizecache:        0,
//					},
//					IbcAddress:            "******************************************",
//					InitialSendCheckpoint: 1,
//					InitialRecvCheckpoint: 1,
//					EnableDebugTrace:      false,
//					AverageBlockTimeMsec:  2000,
//					MaxRetryForInclusion:  3,
//					GasEstimateRate: &Fraction{
//						Numerator:   1,
//						Denominator: 1,
//					},
//					MaxGasLimit: *********,
//					TxType:      "auto",
//				},
//				pathEnd: &core.PathEnd{
//					ChainID:      "********",
//					ClientID:     "hb-ibft2-0",
//					ConnectionID: "connection-0",
//					ChannelID:    "channel-0",
//					PortID:       "account-sync",
//					Order:        "unordered",
//					Version:      "account-sync-1",
//				},
//				homePath:         ".",
//				chainID:          big.NewInt(int64(5151)),
//				codec:            nil,
//				msgEventListener: nil,
//				client: &client.ETHClient{
//					Client: &ethclient.Client{},
//				},
//				ibcHandler: &ibchandler.Ibchandler{
//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
//				},
//				signer:                    nil,
//				connectionOpenedConfirmed: false,
//			},
//			args: args{
//				ctx: context.TODO(),
//			},
//			want: &bind.TransactOpts{
//				From:      common.Address{},
//				Nonce:     &big.Int{},
//				Signer:    nil,
//				Value:     &big.Int{},
//				GasPrice:  &big.Int{},
//				GasFeeCap: &big.Int{},
//				GasTipCap: &big.Int{},
//				GasLimit:  0,
//				Context:   nil,
//				NoSend:    false,
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			chain := &Chain{
//				config:                    tt.fields.config,
//				pathEnd:                   tt.fields.pathEnd,
//				homePath:                  tt.fields.homePath,
//				chainID:                   tt.fields.chainID,
//				codec:                     tt.fields.codec,
//				msgEventListener:          tt.fields.msgEventListener,
//				client:                    tt.fields.client,
//				ibcHandler:                tt.fields.ibcHandler,
//				signer:                    tt.fields.signer,
//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
//			}
//			got, err := chain.TxOpts(tt.args.ctx)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Chain.TxOpts() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				if diff := cmp.Diff(got, tt.want); diff != "" {
//					t.Errorf("Chain.TxOpts() differs: (-got +want)n%s", diff)
//				}
//			}
//		})
//	}
//}

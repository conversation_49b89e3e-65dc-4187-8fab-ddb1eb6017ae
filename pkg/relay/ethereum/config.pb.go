// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: relayer/chains/ethereum/config/config.proto

package ethereum

import (
	fmt "fmt"
	types "github.com/cosmos/cosmos-sdk/codec/types"
	_ "github.com/cosmos/gogoproto/gogoproto"
	proto "github.com/cosmos/gogoproto/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type ChainConfig struct {
	ChainId               string     `protobuf:"bytes,1,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	EthChainId            uint64     `protobuf:"varint,2,opt,name=eth_chain_id,json=ethChainId,proto3" json:"eth_chain_id,omitempty"`
	RpcAddr               string     `protobuf:"bytes,3,opt,name=rpc_addr,json=rpcAddr,proto3" json:"rpc_addr,omitempty"`
	Signer                *types.Any `protobuf:"bytes,4,opt,name=signer,proto3" json:"signer,omitempty"`
	IbcAddress            string     `protobuf:"bytes,5,opt,name=ibc_address,json=ibcAddress,proto3" json:"ibc_address,omitempty"`
	InitialSendCheckpoint uint64     `protobuf:"varint,6,opt,name=initial_send_checkpoint,json=initialSendCheckpoint,proto3" json:"initial_send_checkpoint,omitempty"`
	InitialRecvCheckpoint uint64     `protobuf:"varint,7,opt,name=initial_recv_checkpoint,json=initialRecvCheckpoint,proto3" json:"initial_recv_checkpoint,omitempty"`
	EnableDebugTrace      bool       `protobuf:"varint,8,opt,name=enable_debug_trace,json=enableDebugTrace,proto3" json:"enable_debug_trace,omitempty"`
	AverageBlockTimeMsec  uint64     `protobuf:"varint,9,opt,name=average_block_time_msec,json=averageBlockTimeMsec,proto3" json:"average_block_time_msec,omitempty"`
	MaxRetryForInclusion  uint64     `protobuf:"varint,10,opt,name=max_retry_for_inclusion,json=maxRetryForInclusion,proto3" json:"max_retry_for_inclusion,omitempty"`
	// option for ibc-solidity ADR-001
	// if set, the relayer updates a LC contract directly if possible
	// if null, the relayer updates a LC contract via the handler
	GasEstimateRate *Fraction `protobuf:"bytes,11,opt,name=gas_estimate_rate,json=gasEstimateRate,proto3" json:"gas_estimate_rate,omitempty"`
	MaxGasLimit     uint64    `protobuf:"varint,12,opt,name=max_gas_limit,json=maxGasLimit,proto3" json:"max_gas_limit,omitempty"`
	TxType          string    `protobuf:"bytes,13,opt,name=tx_type,json=txType,proto3" json:"tx_type,omitempty"`
}

func (m *ChainConfig) Reset()         { *m = ChainConfig{} }
func (m *ChainConfig) String() string { return proto.CompactTextString(m) }
func (*ChainConfig) ProtoMessage()    {}
func (*ChainConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_a8a57ab2f9f14837, []int{0}
}
func (m *ChainConfig) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ChainConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ChainConfig.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ChainConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChainConfig.Merge(m, src)
}
func (m *ChainConfig) XXX_Size() int {
	return m.Size()
}
func (m *ChainConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_ChainConfig.DiscardUnknown(m)
}

var xxx_messageInfo_ChainConfig proto.InternalMessageInfo

type Fraction struct {
	Numerator   uint64 `protobuf:"varint,1,opt,name=numerator,proto3" json:"numerator,omitempty"`
	Denominator uint64 `protobuf:"varint,2,opt,name=denominator,proto3" json:"denominator,omitempty"`
}

func (m *Fraction) Reset()         { *m = Fraction{} }
func (m *Fraction) String() string { return proto.CompactTextString(m) }
func (*Fraction) ProtoMessage()    {}
func (*Fraction) Descriptor() ([]byte, []int) {
	return fileDescriptor_a8a57ab2f9f14837, []int{1}
}
func (m *Fraction) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Fraction) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Fraction.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Fraction) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Fraction.Merge(m, src)
}
func (m *Fraction) XXX_Size() int {
	return m.Size()
}
func (m *Fraction) XXX_DiscardUnknown() {
	xxx_messageInfo_Fraction.DiscardUnknown(m)
}

var xxx_messageInfo_Fraction proto.InternalMessageInfo

func init() {
	proto.RegisterType((*ChainConfig)(nil), "relayer.chains.ethereum.config.ChainConfig")
	proto.RegisterType((*Fraction)(nil), "relayer.chains.ethereum.config.Fraction")
}

func init() {
	proto.RegisterFile("relayer/chains/ethereum/config/config.proto", fileDescriptor_a8a57ab2f9f14837)
}

var fileDescriptor_a8a57ab2f9f14837 = []byte{
	// 571 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x7c, 0x93, 0x41, 0x6f, 0xd3, 0x30,
	0x14, 0xc7, 0x1b, 0x28, 0x5d, 0xe7, 0x6e, 0x02, 0xac, 0xa1, 0x65, 0x13, 0x0a, 0xd5, 0x4e, 0x95,
	0xd8, 0x12, 0x09, 0x04, 0x37, 0x0e, 0x5b, 0x61, 0x68, 0x08, 0x2e, 0xa1, 0x27, 0x38, 0x58, 0x8e,
	0xfd, 0xe6, 0x98, 0x25, 0x76, 0x64, 0x3b, 0x53, 0xf3, 0x2d, 0xf8, 0x58, 0x3b, 0x4e, 0xe2, 0xc2,
	0x11, 0xb6, 0x2f, 0x82, 0xe2, 0xa4, 0xa3, 0x5c, 0x38, 0xc5, 0x7e, 0xbf, 0xff, 0xff, 0x9f, 0xa7,
	0xe4, 0x3d, 0xf4, 0xdc, 0x40, 0x41, 0x1b, 0x30, 0x09, 0xcb, 0xa9, 0x54, 0x36, 0x01, 0x97, 0x83,
	0x81, 0xba, 0x4c, 0x98, 0x56, 0xe7, 0x52, 0xf4, 0x8f, 0xb8, 0x32, 0xda, 0x69, 0x1c, 0xf5, 0xe2,
	0xb8, 0x13, 0xc7, 0x2b, 0x71, 0xdc, 0xa9, 0xf6, 0x77, 0x84, 0x16, 0xda, 0x4b, 0x93, 0xf6, 0xd4,
	0xb9, 0xf6, 0xf7, 0x84, 0xd6, 0xa2, 0x80, 0xc4, 0xdf, 0xb2, 0xfa, 0x3c, 0xa1, 0xaa, 0xe9, 0xd0,
	0xc1, 0x8f, 0x21, 0x9a, 0xcc, 0xdb, 0xac, 0xb9, 0x0f, 0xc0, 0x7b, 0x68, 0xec, 0xa3, 0x89, 0xe4,
	0x61, 0x30, 0x0d, 0x66, 0x9b, 0xe9, 0x86, 0xbf, 0x9f, 0x71, 0x3c, 0x45, 0x5b, 0xe0, 0x72, 0x72,
	0x87, 0xef, 0x4d, 0x83, 0xd9, 0x30, 0x45, 0xe0, 0xf2, 0x79, 0xaf, 0xd8, 0x43, 0x63, 0x53, 0x31,
	0x42, 0x39, 0x37, 0xe1, 0xfd, 0xce, 0x6c, 0x2a, 0x76, 0xcc, 0xb9, 0xc1, 0x87, 0x68, 0x64, 0xa5,
	0x50, 0x60, 0xc2, 0xe1, 0x34, 0x98, 0x4d, 0x5e, 0xec, 0xc4, 0x5d, 0x4f, 0xf1, 0xaa, 0xa7, 0xf8,
	0x58, 0x35, 0x69, 0xaf, 0xc1, 0xcf, 0xd0, 0x44, 0x66, 0x5d, 0x10, 0x58, 0x1b, 0x3e, 0xf0, 0x59,
	0x48, 0x66, 0x3e, 0x0b, 0xac, 0xc5, 0xaf, 0xd1, 0xae, 0x54, 0xd2, 0x49, 0x5a, 0x10, 0x0b, 0x8a,
	0x13, 0x96, 0x03, 0xbb, 0xa8, 0xb4, 0x54, 0x2e, 0x1c, 0xf9, 0xb6, 0x9e, 0xf4, 0xf8, 0x33, 0x28,
	0x3e, 0xbf, 0x83, 0xeb, 0x3e, 0x03, 0xec, 0x72, 0xdd, 0xb7, 0xf1, 0x8f, 0x2f, 0x05, 0x76, 0xb9,
	0xe6, 0x3b, 0x44, 0x18, 0x14, 0xcd, 0x0a, 0x20, 0x1c, 0xb2, 0x5a, 0x10, 0x67, 0x28, 0x83, 0x70,
	0x3c, 0x0d, 0x66, 0xe3, 0xf4, 0x51, 0x47, 0xde, 0xb6, 0x60, 0xd1, 0xd6, 0xf1, 0x2b, 0xb4, 0x4b,
	0x2f, 0xc1, 0x50, 0x01, 0x24, 0x2b, 0x34, 0xbb, 0x20, 0x4e, 0x96, 0x40, 0x4a, 0x0b, 0x2c, 0xdc,
	0xf4, 0x6f, 0xd9, 0xe9, 0xf1, 0x49, 0x4b, 0x17, 0xb2, 0x84, 0x4f, 0x16, 0x58, 0x6b, 0x2b, 0xe9,
	0x92, 0x18, 0x70, 0xa6, 0x21, 0xe7, 0xda, 0x10, 0xa9, 0x58, 0x51, 0x5b, 0xa9, 0x55, 0x88, 0x3a,
	0x5b, 0x49, 0x97, 0x69, 0x4b, 0x4f, 0xb5, 0x39, 0x5b, 0x31, 0xbc, 0x40, 0x8f, 0x05, 0xb5, 0x04,
	0xac, 0x93, 0x25, 0x75, 0x40, 0x0c, 0x75, 0x10, 0x4e, 0xfc, 0x57, 0x9e, 0xc5, 0xff, 0x9f, 0x97,
	0xf8, 0xd4, 0x50, 0xe6, 0xa4, 0x56, 0xe9, 0x43, 0x41, 0xed, 0xbb, 0x3e, 0x21, 0xa5, 0x0e, 0xf0,
	0x01, 0xda, 0x6e, 0x9b, 0x69, 0x93, 0x0b, 0x59, 0x4a, 0x17, 0x6e, 0xf9, 0x16, 0x26, 0x25, 0x5d,
	0xbe, 0xa7, 0xf6, 0x63, 0x5b, 0xc2, 0xbb, 0x68, 0xc3, 0x2d, 0x89, 0x6b, 0x2a, 0x08, 0xb7, 0xfd,
	0x2f, 0x1a, 0xb9, 0xe5, 0xa2, 0xa9, 0xe0, 0xe0, 0x03, 0x1a, 0xaf, 0x92, 0xf1, 0x53, 0xb4, 0xa9,
	0xea, 0x12, 0x0c, 0x75, 0xda, 0xf8, 0x91, 0x1a, 0xa6, 0x7f, 0x0b, 0x78, 0x8a, 0x26, 0x1c, 0x94,
	0x2e, 0xa5, 0xf2, 0xbc, 0x9b, 0xa9, 0xf5, 0xd2, 0xc9, 0xd7, 0xab, 0xdf, 0xd1, 0xe0, 0xea, 0x26,
	0x0a, 0xae, 0x6f, 0xa2, 0xe0, 0xd7, 0x4d, 0x14, 0x7c, 0xbf, 0x8d, 0x06, 0xd7, 0xb7, 0xd1, 0xe0,
	0xe7, 0x6d, 0x34, 0xf8, 0xf2, 0x46, 0x48, 0x97, 0xd7, 0x59, 0xcc, 0x74, 0x99, 0x70, 0x60, 0xb5,
	0x31, 0xe0, 0x8e, 0x0a, 0x9a, 0x25, 0x9c, 0x65, 0xe2, 0x88, 0xb3, 0x6f, 0x55, 0x73, 0xb4, 0x5a,
	0xb0, 0xea, 0x42, 0x24, 0xfe, 0x7c, 0xb7, 0x63, 0xd9, 0xc8, 0x8f, 0xdf, 0xcb, 0x3f, 0x01, 0x00,
	0x00, 0xff, 0xff, 0x69, 0x77, 0xdf, 0xb0, 0x85, 0x03, 0x00, 0x00,
}

func (m *ChainConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChainConfig) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ChainConfig) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TxType) > 0 {
		i -= len(m.TxType)
		copy(dAtA[i:], m.TxType)
		i = encodeVarintConfig(dAtA, i, uint64(len(m.TxType)))
		i--
		dAtA[i] = 0x6a
	}
	if m.MaxGasLimit != 0 {
		i = encodeVarintConfig(dAtA, i, uint64(m.MaxGasLimit))
		i--
		dAtA[i] = 0x60
	}
	if m.GasEstimateRate != nil {
		{
			size, err := m.GasEstimateRate.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintConfig(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x5a
	}
	if m.MaxRetryForInclusion != 0 {
		i = encodeVarintConfig(dAtA, i, uint64(m.MaxRetryForInclusion))
		i--
		dAtA[i] = 0x50
	}
	if m.AverageBlockTimeMsec != 0 {
		i = encodeVarintConfig(dAtA, i, uint64(m.AverageBlockTimeMsec))
		i--
		dAtA[i] = 0x48
	}
	if m.EnableDebugTrace {
		i--
		if m.EnableDebugTrace {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x40
	}
	if m.InitialRecvCheckpoint != 0 {
		i = encodeVarintConfig(dAtA, i, uint64(m.InitialRecvCheckpoint))
		i--
		dAtA[i] = 0x38
	}
	if m.InitialSendCheckpoint != 0 {
		i = encodeVarintConfig(dAtA, i, uint64(m.InitialSendCheckpoint))
		i--
		dAtA[i] = 0x30
	}
	if len(m.IbcAddress) > 0 {
		i -= len(m.IbcAddress)
		copy(dAtA[i:], m.IbcAddress)
		i = encodeVarintConfig(dAtA, i, uint64(len(m.IbcAddress)))
		i--
		dAtA[i] = 0x2a
	}
	if m.Signer != nil {
		{
			size, err := m.Signer.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintConfig(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if len(m.RpcAddr) > 0 {
		i -= len(m.RpcAddr)
		copy(dAtA[i:], m.RpcAddr)
		i = encodeVarintConfig(dAtA, i, uint64(len(m.RpcAddr)))
		i--
		dAtA[i] = 0x1a
	}
	if m.EthChainId != 0 {
		i = encodeVarintConfig(dAtA, i, uint64(m.EthChainId))
		i--
		dAtA[i] = 0x10
	}
	if len(m.ChainId) > 0 {
		i -= len(m.ChainId)
		copy(dAtA[i:], m.ChainId)
		i = encodeVarintConfig(dAtA, i, uint64(len(m.ChainId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Fraction) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Fraction) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Fraction) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Denominator != 0 {
		i = encodeVarintConfig(dAtA, i, uint64(m.Denominator))
		i--
		dAtA[i] = 0x10
	}
	if m.Numerator != 0 {
		i = encodeVarintConfig(dAtA, i, uint64(m.Numerator))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintConfig(dAtA []byte, offset int, v uint64) int {
	offset -= sovConfig(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *ChainConfig) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ChainId)
	if l > 0 {
		n += 1 + l + sovConfig(uint64(l))
	}
	if m.EthChainId != 0 {
		n += 1 + sovConfig(uint64(m.EthChainId))
	}
	l = len(m.RpcAddr)
	if l > 0 {
		n += 1 + l + sovConfig(uint64(l))
	}
	if m.Signer != nil {
		l = m.Signer.Size()
		n += 1 + l + sovConfig(uint64(l))
	}
	l = len(m.IbcAddress)
	if l > 0 {
		n += 1 + l + sovConfig(uint64(l))
	}
	if m.InitialSendCheckpoint != 0 {
		n += 1 + sovConfig(uint64(m.InitialSendCheckpoint))
	}
	if m.InitialRecvCheckpoint != 0 {
		n += 1 + sovConfig(uint64(m.InitialRecvCheckpoint))
	}
	if m.EnableDebugTrace {
		n += 2
	}
	if m.AverageBlockTimeMsec != 0 {
		n += 1 + sovConfig(uint64(m.AverageBlockTimeMsec))
	}
	if m.MaxRetryForInclusion != 0 {
		n += 1 + sovConfig(uint64(m.MaxRetryForInclusion))
	}
	if m.GasEstimateRate != nil {
		l = m.GasEstimateRate.Size()
		n += 1 + l + sovConfig(uint64(l))
	}
	if m.MaxGasLimit != 0 {
		n += 1 + sovConfig(uint64(m.MaxGasLimit))
	}
	l = len(m.TxType)
	if l > 0 {
		n += 1 + l + sovConfig(uint64(l))
	}
	return n
}

func (m *Fraction) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Numerator != 0 {
		n += 1 + sovConfig(uint64(m.Numerator))
	}
	if m.Denominator != 0 {
		n += 1 + sovConfig(uint64(m.Denominator))
	}
	return n
}

func sovConfig(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozConfig(x uint64) (n int) {
	return sovConfig(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *ChainConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConfig
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ChainConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ChainConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChainId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConfig
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConfig
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ChainId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EthChainId", wireType)
			}
			m.EthChainId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EthChainId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RpcAddr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConfig
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConfig
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RpcAddr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Signer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthConfig
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthConfig
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Signer == nil {
				m.Signer = &types.Any{}
			}
			if err := m.Signer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IbcAddress", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConfig
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConfig
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.IbcAddress = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field InitialSendCheckpoint", wireType)
			}
			m.InitialSendCheckpoint = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.InitialSendCheckpoint |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field InitialRecvCheckpoint", wireType)
			}
			m.InitialRecvCheckpoint = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.InitialRecvCheckpoint |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EnableDebugTrace", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.EnableDebugTrace = bool(v != 0)
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AverageBlockTimeMsec", wireType)
			}
			m.AverageBlockTimeMsec = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AverageBlockTimeMsec |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxRetryForInclusion", wireType)
			}
			m.MaxRetryForInclusion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxRetryForInclusion |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GasEstimateRate", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthConfig
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthConfig
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.GasEstimateRate == nil {
				m.GasEstimateRate = &Fraction{}
			}
			if err := m.GasEstimateRate.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxGasLimit", wireType)
			}
			m.MaxGasLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxGasLimit |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TxType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConfig
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConfig
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TxType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConfig(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConfig
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Fraction) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConfig
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Fraction: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Fraction: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Numerator", wireType)
			}
			m.Numerator = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Numerator |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Denominator", wireType)
			}
			m.Denominator = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Denominator |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipConfig(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConfig
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipConfig(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowConfig
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthConfig
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupConfig
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthConfig
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthConfig        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowConfig          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupConfig = fmt.Errorf("proto: unexpected end of group")
)

//go:generate mockgen -source=$GOFILE -package=ethereum -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package ethereum

import (
	"fmt"
	"math/big"

	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	channeltypes "github.com/cosmos/ibc-go/v7/modules/core/04-channel/types"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/log"
	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/contract/ibchandler"
)

var (
	abiGeneratedClientIdentifier,
	abiGeneratedConnectionIdentifier,
	abiGeneratedChannelIdentifier,
	abiSendPacket,
	abiRecvPacket,
	abiWriteAcknowledgement,
	abiAcknowledgePacket abi.Event
)

func init() {
	abiIBCHandler, err := ibchandler.IbchandlerMetaData.GetAbi()
	if err != nil {
		panic(err)
	}
	abiGeneratedClientIdentifier = abiIBCHandler.Events["GeneratedClientIdentifier"]
	abiGeneratedConnectionIdentifier = abiIBCHandler.Events["GeneratedConnectionIdentifier"]
	abiGeneratedChannelIdentifier = abiIBCHandler.Events["GeneratedChannelIdentifier"]
	abiSendPacket = abiIBCHandler.Events["SendPacket"]
	abiRecvPacket = abiIBCHandler.Events["RecvPacket"]
	abiWriteAcknowledgement = abiIBCHandler.Events["WriteAcknowledgement"]
	abiAcknowledgePacket = abiIBCHandler.Events["AcknowledgePacket"]

}

func (chain *Chain) findSentPackets(ctx core.QueryContext, fromHeight uint64) (core.PacketInfoList, error) {
	logger := chain.GetChannelLogger()
	//now := time.Now()

	var dstPortID, dstChannelID string
	if channel, found, err := chain.ibcHandler.GetChannel(
		chain.callOptsFromQueryContext(ctx),
		chain.Path().PortID,
		chain.Path().ChannelID,
	); err != nil {
		logger.Error("failed to get channel", err)
		return nil, err
	} else if !found {
		err := fmt.Errorf("channel not found")
		logger.Error("failed to get channel", err, "port_id", chain.Path().PortID, "channel_id", chain.Path().ChannelID)
		return nil, err
	} else {
		// TODO For Debug
		logger.Debug(fmt.Sprintf("chain: %s/ channel: %s\n"), chain.chainID, channel)

		dstPortID = channel.Counterparty.PortId
		dstChannelID = channel.Counterparty.ChannelId

		// TODO For Debug
		logger.Debug(fmt.Sprintf("dstPortID: %s\n"), dstPortID)
		logger.Debug(fmt.Sprintf("dstChannelID: %s\n"), dstChannelID)
	}

	query := ethereum.FilterQuery{
		FromBlock: big.NewInt(int64(fromHeight)),
		ToBlock:   big.NewInt(int64(ctx.Height().GetRevisionHeight())),
		Addresses: []common.Address{
			chain.config.IBCAddress(),
		},
		Topics: [][]common.Hash{{
			abiSendPacket.ID,
		}},
	}

	logs, err := chain.client.FilterLogs(ctx.Context(), query)
	if err != nil {
		logger.Error("failed to filter logs", err)
		return nil, err
	}

	//defer logger.TimeTrack(now, "findSentPackets", "num_logs", len(logs))

	var packets core.PacketInfoList
	for _, log := range logs {
		height := clienttypes.NewHeight(0, log.BlockNumber)

		sendPacket, err := chain.ibcHandler.ParseSendPacket(log)
		if err != nil {
			return nil, fmt.Errorf("failed to parse SendPacket event: err=%v, log=%v", err, log)
		}

		if chain.Path().ChannelID == sendPacket.SourceChannel &&
			chain.Path().PortID == sendPacket.SourcePort &&
			chain.Path().PortID == dstPortID &&
			sendPacket.SourcePort == dstPortID {

			// TODO For Debug
			logger.Debug(fmt.Sprintf("chain.Path().ChannelID: %s\n"), chain.Path().ChannelID)
			logger.Debug(fmt.Sprintf("sendPacket.SourceChannel: %s\n"), sendPacket.SourceChannel)
			logger.Debug(fmt.Sprintf("sendPacket: %s\n"), sendPacket)

			packet := &core.PacketInfo{
				Packet: channeltypes.NewPacket(
					sendPacket.Data,
					sendPacket.Sequence,
					sendPacket.SourcePort,
					sendPacket.SourceChannel,
					dstPortID,
					dstChannelID,
					clienttypes.Height(sendPacket.TimeoutHeight),
					sendPacket.TimeoutTimestamp,
				),
				EventHeight: height,
			}
			packets = append(packets, packet)
		}
	}

	return packets, nil
}

func (chain *Chain) findReceivedPackets(ctx core.QueryContext, fromHeight uint64) (core.PacketInfoList, error) {
	logger := chain.GetChannelLogger()
	//now := time.Now()

	recvPacketEvents, err := chain.findRecvPacketEvents(ctx, fromHeight)
	if err != nil {
		logger.Error("failed to find recv packet events", err)
		return nil, err
	} else if len(recvPacketEvents) == 0 {
		return nil, nil
	}

	writeAckEvents, err := chain.findWriteAckEvents(ctx, recvPacketEvents[0].Raw.BlockNumber)
	if err != nil {
		logger.Error("failed to find write ack events", err)
		return nil, err
	} else if len(writeAckEvents) == 0 {
		return nil, nil
	}

	//defer logger.TimeTrack(now, "findReceivedPackets", "num_recv_packet_events", len(recvPacketEvents), "num_write_ack_events", len(writeAckEvents))

	var packets core.PacketInfoList
	for _, rp := range recvPacketEvents {
		for _, wa := range writeAckEvents {
			if rp.Packet.Sequence == wa.Sequence {
				if chain.Path().PortID == rp.Packet.SourcePort && chain.Path().PortID == rp.Packet.DestinationPort && rp.Packet.SourcePort == rp.Packet.DestinationPort {
					packets = append(packets, &core.PacketInfo{
						Packet: channeltypes.Packet{
							Sequence:           rp.Packet.Sequence,
							SourcePort:         rp.Packet.SourcePort,
							SourceChannel:      rp.Packet.SourceChannel,
							DestinationPort:    rp.Packet.DestinationPort,
							DestinationChannel: rp.Packet.DestinationChannel,
							Data:               rp.Packet.Data,
							TimeoutHeight:      clienttypes.Height(rp.Packet.TimeoutHeight),
							TimeoutTimestamp:   rp.Packet.TimeoutTimestamp,
						},
						Acknowledgement: wa.Acknowledgement,
						EventHeight:     clienttypes.NewHeight(0, rp.Raw.BlockNumber),
					})
					break
				}
			}
		}
	}

	return packets, nil
}

func (chain *Chain) findRecvPacketEvents(ctx core.QueryContext, fromHeight uint64) ([]*ibchandler.IbchandlerRecvPacket, error) {
	query := ethereum.FilterQuery{
		FromBlock: big.NewInt(int64(fromHeight)),
		ToBlock:   big.NewInt(int64(ctx.Height().GetRevisionHeight())),
		Addresses: []common.Address{
			chain.config.IBCAddress(),
		},
		Topics: [][]common.Hash{{
			abiRecvPacket.ID,
		}},
	}

	logs, err := chain.client.FilterLogs(ctx.Context(), query)
	if err != nil {
		return nil, err
	}

	var events []*ibchandler.IbchandlerRecvPacket
	for _, log := range logs {
		event, err := chain.ibcHandler.ParseRecvPacket(log)
		if err != nil {
			return nil, fmt.Errorf("failed to parse RecvPacket event: err=%v, log=%v", err, log)
		}
		events = append(events, event)
	}

	return events, nil
}

func (chain *Chain) findWriteAckEvents(ctx core.QueryContext, fromHeight uint64) ([]*ibchandler.IbchandlerWriteAcknowledgement, error) {
	query := ethereum.FilterQuery{
		FromBlock: big.NewInt(int64(fromHeight)),
		ToBlock:   big.NewInt(int64(ctx.Height().GetRevisionHeight())),
		Addresses: []common.Address{
			chain.config.IBCAddress(),
		},
		Topics: [][]common.Hash{{
			abiWriteAcknowledgement.ID,
		}},
	}

	logs, err := chain.client.FilterLogs(ctx.Context(), query)
	if err != nil {
		return nil, err
	}

	var events []*ibchandler.IbchandlerWriteAcknowledgement
	for _, log := range logs {
		event, err := chain.ibcHandler.ParseWriteAcknowledgement(log)
		if err != nil {
			return nil, fmt.Errorf("failed to parse WriteAcknowledgement event: err=%v, log=%v", err, log)
		}
		events = append(events, event)
	}

	return events, nil
}

func (chain *Chain) GetChannelLogger() *log.DcjpyLogger {
	logger := GetModuleLogger()
	if chain.Path() == nil {
		return logger
	}
	chainID := chain.Path().ChainID
	portID := chain.Path().PortID
	channelID := chain.Path().ChannelID
	return logger.WithChannel(chainID, portID, channelID)
}

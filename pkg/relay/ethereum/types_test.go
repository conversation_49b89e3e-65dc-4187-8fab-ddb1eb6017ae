//go:generate mockgen -source=$GOFILE -package=ethereum -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package ethereum

import (
	"reflect"
	"testing"

	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	connectiontypes "github.com/cosmos/ibc-go/v7/modules/core/03-connection/types"
	channeltypes "github.com/cosmos/ibc-go/v7/modules/core/04-channel/types"
	commitmenttypes "github.com/cosmos/ibc-go/v7/modules/core/23-commitment/types"
	"github.com/cosmos/ibc-go/v7/modules/core/exported"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/contract/ibchandler"
	"github.com/google/go-cmp/cmp"
)

func Test_connectionEndToPB(t *testing.T) {

	type args struct {
		conn ibchandler.ConnectionEndData
	}
	tests := []struct {
		name string
		args args
		want connectiontypes.ConnectionEnd
	}{
		{
			name: "should return a stateful object on a chain connected to another separate one.",
			args: args{
				conn: ibchandler.ConnectionEndData{
					ClientId: "hb-ibft2-0",
					Versions: []ibchandler.VersionData{
						{"account-sync-1", []string{"ibc"}},
						{"account-sync-1", []string{"ibc"}},
					},
					State: 0,
					Counterparty: ibchandler.CounterpartyData{
						ClientId:     "hb-ibft2-0",
						ConnectionId: "connection-0",
						Prefix: ibchandler.MerklePrefixData{
							KeyPrefix: []byte("ibc"),
						},
					},
					DelayPeriod: 0,
				},
			},
			want: connectiontypes.ConnectionEnd{
				ClientId: "hb-ibft2-0",
				Versions: []*connectiontypes.Version{
					{"account-sync-1", []string{"ibc"}},
					{"account-sync-1", []string{"ibc"}},
				},
				State: 0,
				Counterparty: connectiontypes.Counterparty{
					ClientId:     "hb-ibft2-0",
					ConnectionId: "connection-0",
					Prefix: commitmenttypes.MerklePrefix{
						KeyPrefix: []byte("ibc"),
					},
				},
				DelayPeriod: 0,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := connectionEndToPB(tt.args.conn); !reflect.DeepEqual(got, tt.want) {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("connectionEndToPB differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

func Test_channelToPB(t *testing.T) {
	type args struct {
		chann ibchandler.ChannelData
	}
	tests := []struct {
		name string
		args args
		want channeltypes.Channel
	}{
		{
			name: "should return Channel.",
			args: args{
				chann: ibchandler.ChannelData{
					State:    0,
					Ordering: 0,
					Counterparty: ibchandler.ChannelCounterpartyData{
						PortId:    "account-sync",
						ChannelId: "channel-0",
					},
					ConnectionHops: nil,
					Version:        "account-sync-1",
				},
			},
			want: channeltypes.Channel{
				State:    0,
				Ordering: 0,
				Counterparty: channeltypes.Counterparty{
					PortId:    "account-sync",
					ChannelId: "channel-0",
				},
				ConnectionHops: nil,
				Version:        "account-sync-1",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := channelToPB(tt.args.chann); !reflect.DeepEqual(got, tt.want) {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("channelToPB differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

func Test_pbToHandlerHeight(t *testing.T) {

	type args struct {
		height exported.Height
	}
	tests := []struct {
		name string
		args args
		want ibchandler.HeightData
	}{
		{
			name: "should return IBC height.",
			args: args{
				height: clienttypes.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
			},
			want: ibchandler.HeightData{
				RevisionNumber: 0,
				RevisionHeight: 100,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := pbToHandlerHeight(tt.args.height); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("pbToHandlerHeight() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_pbToHostHeight(t *testing.T) {

	type args struct {
		height exported.Height
	}
	tests := []struct {
		name string
		args args
		want ibchandler.HeightData
	}{
		{
			name: "should return IBC height.",
			args: args{
				height: clienttypes.Height{
					RevisionNumber: 0,
					RevisionHeight: 200,
				},
			},
			want: ibchandler.HeightData{
				RevisionNumber: 0,
				RevisionHeight: 200,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := pbToHostHeight(tt.args.height); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("pbToHostHeight() = %v, want %v", got, tt.want)
			}
		})
	}
}

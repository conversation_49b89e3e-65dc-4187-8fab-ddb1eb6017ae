//go:generate mockgen -source=$GOFILE -package=ethereum -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package ethereum

import (
	"math/big"
	"testing"

	"github.com/cosmos/cosmos-sdk/codec"
	codectypes "github.com/cosmos/cosmos-sdk/codec/types"
	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/log"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/client"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/contract/ibchandler"
	"github.com/ethereum/go-ethereum/ethclient"
	"go.uber.org/mock/gomock"
	"golang.org/x/exp/slog"
)

// TODO ibcHandler が getChannel してしまうため実施不可
func TestChain_findSentPackets(t *testing.T) {
	t.Skip("cannot not possible because ibcHandler executes getChannel.")
	//
	//	stubChainConfig := ChainConfig{
	//		ChainId:    "********",
	//		EthChainId: 5151,
	//		RpcAddr:    "http://**********:8451",
	//		Signer: &codectypes.Any{
	//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
	//			Value:                nil,
	//			XXX_NoUnkeyedLiteral: struct{}{},
	//			XXX_unrecognized:     nil,
	//			XXX_sizecache:        0,
	//		},
	//		IbcAddress:            "******************************************",
	//		InitialSendCheckpoint: 1,
	//		InitialRecvCheckpoint: 1,
	//		EnableDebugTrace:      false,
	//		AverageBlockTimeMsec:  2000,
	//		MaxRetryForInclusion:  3,
	//		GasEstimateRate: &Fraction{
	//			Numerator:   1,
	//			Denominator: 1,
	//		},
	//		MaxGasLimit: *********,
	//		TxType:      "auto",
	//	}
	//	stubPathEnd := &core.PathEnd{
	//		ChainID:      "********",
	//		ClientID:     "hb-ibft2-0",
	//		ConnectionID: "connection-0",
	//		ChannelID:    "channel-0",
	//		PortID:       "account-sync",
	//		Order:        "unordered",
	//		Version:      "account-sync-1",
	//	}
	//	stubHeight := clienttypes.Height{
	//		RevisionNumber: 0,
	//		RevisionHeight: 100,
	//	}
	//
	//	ctrl := gomock.NewController(t)
	//	mockQueryContext := core.NewMockQueryContext(ctrl)
	//	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
	//	mockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
	//
	//	type fields struct {
	//		config                    ChainConfig
	//		pathEnd                   *core.PathEnd
	//		homePath                  string
	//		chainID                   *big.Int
	//		codec                     codec.ProtoCodecMarshaler
	//		msgEventListener          core.MsgEventListener
	//		client                    *client.ETHClient
	//		ibcHandler                *ibchandler.Ibchandler
	//		signer                    Signer
	//		connectionOpenedConfirmed bool
	//	}
	//	type args struct {
	//		ctx        core.QueryContext
	//		fromHeight uint64
	//	}
	//	tests := []struct {
	//		name    string
	//		fields  fields
	//		args    args
	//		want    core.PacketInfoList
	//		wantErr bool
	//	}{
	//		{
	//			name: "should find sent packets.",
	//			fields: fields{
	//				config:           stubChainConfig,
	//				pathEnd:          stubPathEnd,
	//				homePath:         ".relayer",
	//				chainID:          &big.Int{},
	//				codec:            nil,
	//				msgEventListener: nil,
	//				client: &client.ETHClient{
	//					Client: &ethclient.Client{},
	//				},
	//				ibcHandler: &ibchandler.Ibchandler{
	//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
	//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
	//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
	//				},
	//				signer:                    nil,
	//				connectionOpenedConfirmed: false,
	//			},
	//			args: args{
	//				ctx:        mockQueryContext,
	//				fromHeight: 0,
	//			},
	//			want:    nil,
	//			wantErr: false,
	//		},
	//	}
	//	for _, tt := range tests {
	//		t.Run(tt.name, func(t *testing.T) {
	//			chain := &Chain{
	//				config:                    tt.fields.config,
	//				pathEnd:                   tt.fields.pathEnd,
	//				homePath:                  tt.fields.homePath,
	//				chainID:                   tt.fields.chainID,
	//				codec:                     tt.fields.codec,
	//				msgEventListener:          tt.fields.msgEventListener,
	//				client:                    tt.fields.client,
	//				ibcHandler:                tt.fields.ibcHandler,
	//				signer:                    tt.fields.signer,
	//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
	//			}
	//			got, err := chain.findSentPackets(tt.args.ctx, tt.args.fromHeight)
	//			if (err != nil) != tt.wantErr {
	//				t.Errorf("Chain.findSentPackets() error = %v, wantErr %v", err, tt.wantErr)
	//				return
	//			}
	//			if !reflect.DeepEqual(got, tt.want) {
	//				t.Errorf("Chain.findSentPackets() = %v, want %v", got, tt.want)
	//			}
	//		})
	//	}
}

// TODO ethclient が FilterLogs してしまうため実施不可
func TestChain_findReceivedPackets(t *testing.T) {
	t.Skip("cannot not possible UT is not possible because ethclient uses FilterLogs.")

	//
	//	stubChainConfig := ChainConfig{
	//		ChainId:    "********",
	//		EthChainId: 5151,
	//		RpcAddr:    "http://**********:8451",
	//		Signer: &codectypes.Any{
	//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
	//			Value:                nil,
	//			XXX_NoUnkeyedLiteral: struct{}{},
	//			XXX_unrecognized:     nil,
	//			XXX_sizecache:        0,
	//		},
	//		IbcAddress:            "******************************************",
	//		InitialSendCheckpoint: 1,
	//		InitialRecvCheckpoint: 1,
	//		EnableDebugTrace:      false,
	//		AverageBlockTimeMsec:  2000,
	//		MaxRetryForInclusion:  3,
	//		GasEstimateRate: &Fraction{
	//			Numerator:   1,
	//			Denominator: 1,
	//		},
	//		MaxGasLimit: *********,
	//		TxType:      "auto",
	//	}
	//	stubPathEnd := &core.PathEnd{
	//		ChainID:      "********",
	//		ClientID:     "hb-ibft2-0",
	//		ConnectionID: "connection-0",
	//		ChannelID:    "channel-0",
	//		PortID:       "account-sync",
	//		Order:        "unordered",
	//		Version:      "account-sync-1",
	//	}
	//	stubHeight := clienttypes.Height{
	//		RevisionNumber: 0,
	//		RevisionHeight: 100,
	//	}
	//
	//	ctrl := gomock.NewController(t)
	//	mockQueryContext := core.NewMockQueryContext(ctrl)
	//	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
	//	mockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
	//
	//	type fields struct {
	//		config                    ChainConfig
	//		pathEnd                   *core.PathEnd
	//		homePath                  string
	//		chainID                   *big.Int
	//		codec                     codec.ProtoCodecMarshaler
	//		msgEventListener          core.MsgEventListener
	//		client                    *client.ETHClient
	//		ibcHandler                *ibchandler.Ibchandler
	//		signer                    Signer
	//		connectionOpenedConfirmed bool
	//	}
	//	type args struct {
	//		ctx        core.QueryContext
	//		fromHeight uint64
	//	}
	//	tests := []struct {
	//		name    string
	//		fields  fields
	//		args    args
	//		want    core.PacketInfoList
	//		wantErr bool
	//	}{
	//		{
	//			name: "should find Received packet.",
	//			fields: fields{
	//				config:           stubChainConfig,
	//				pathEnd:          stubPathEnd,
	//				homePath:         ".relayer",
	//				chainID:          &big.Int{},
	//				codec:            nil,
	//				msgEventListener: nil,
	//				client: &client.ETHClient{
	//					Client: &ethclient.Client{},
	//				},
	//				ibcHandler: &ibchandler.Ibchandler{
	//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
	//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
	//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
	//				},
	//				signer:                    nil,
	//				connectionOpenedConfirmed: false,
	//			},
	//			args: args{
	//				ctx:        mockQueryContext,
	//				fromHeight: 100,
	//			},
	//			want:    nil,
	//			wantErr: false,
	//		},
	//	}
	//	for _, tt := range tests {
	//		t.Run(tt.name, func(t *testing.T) {
	//			chain := &Chain{
	//				config:                    tt.fields.config,
	//				pathEnd:                   tt.fields.pathEnd,
	//				homePath:                  tt.fields.homePath,
	//				chainID:                   tt.fields.chainID,
	//				codec:                     tt.fields.codec,
	//				msgEventListener:          tt.fields.msgEventListener,
	//				client:                    tt.fields.client,
	//				ibcHandler:                tt.fields.ibcHandler,
	//				signer:                    tt.fields.signer,
	//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
	//			}
	//			got, err := chain.findReceivedPackets(tt.args.ctx, tt.args.fromHeight)
	//			if (err != nil) != tt.wantErr {
	//				t.Errorf("Chain.findReceivedPackets() error = %v, wantErr %v", err, tt.wantErr)
	//				return
	//			}
	//			if !reflect.DeepEqual(got, tt.want) {
	//				t.Errorf("Chain.findReceivedPackets() = %v, want %v", got, tt.want)
	//				if diff := cmp.Diff(got, tt.want); diff != "" {
	//					t.Errorf("Chain.findReceivedPackets() differs: (-got +want)n%s", diff)
	//				}
	//			}
	//		})
	//	}
}

// TODO ethclient が FilterLogs してしまうため実施不可
func TestChain_findRecvPacketEvents(t *testing.T) {
	t.Skip("cannot not possible UT is not possible because ethclient uses FilterLogs.")
	//
	//	stubChainConfig := ChainConfig{
	//		ChainId:    "********",
	//		EthChainId: 5151,
	//		RpcAddr:    "http://**********:8451",
	//		Signer: &codectypes.Any{
	//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
	//			Value:                nil,
	//			XXX_NoUnkeyedLiteral: struct{}{},
	//			XXX_unrecognized:     nil,
	//			XXX_sizecache:        0,
	//		},
	//		IbcAddress:            "******************************************",
	//		InitialSendCheckpoint: 1,
	//		InitialRecvCheckpoint: 1,
	//		EnableDebugTrace:      false,
	//		AverageBlockTimeMsec:  2000,
	//		MaxRetryForInclusion:  3,
	//		GasEstimateRate: &Fraction{
	//			Numerator:   1,
	//			Denominator: 1,
	//		},
	//		MaxGasLimit: *********,
	//		TxType:      "auto",
	//	}
	//	stubPathEnd := &core.PathEnd{
	//		ChainID:      "********",
	//		ClientID:     "hb-ibft2-0",
	//		ConnectionID: "connection-0",
	//		ChannelID:    "channel-0",
	//		PortID:       "account-sync",
	//		Order:        "unordered",
	//		Version:      "account-sync-1",
	//	}
	//	stubHeight := clienttypes.Height{
	//		RevisionNumber: 0,
	//		RevisionHeight: 100,
	//	}
	//
	//	ctrl := gomock.NewController(t)
	//	mockQueryContext := core.NewMockQueryContext(ctrl)
	//	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
	//	mockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
	//
	//	type fields struct {
	//		config                    ChainConfig
	//		pathEnd                   *core.PathEnd
	//		homePath                  string
	//		chainID                   *big.Int
	//		codec                     codec.ProtoCodecMarshaler
	//		msgEventListener          core.MsgEventListener
	//		client                    *client.ETHClient
	//		ibcHandler                *ibchandler.Ibchandler
	//		signer                    Signer
	//		connectionOpenedConfirmed bool
	//	}
	//	type args struct {
	//		ctx        core.QueryContext
	//		fromHeight uint64
	//	}
	//	tests := []struct {
	//		name    string
	//		fields  fields
	//		args    args
	//		want    []*ibchandler.IbchandlerRecvPacket
	//		wantErr bool
	//	}{
	//		{
	//			name: "should find Received packet.",
	//			fields: fields{
	//				config:           stubChainConfig,
	//				pathEnd:          stubPathEnd,
	//				homePath:         ".relayer",
	//				chainID:          &big.Int{},
	//				codec:            core.MakeCodec(),
	//				msgEventListener: nil,
	//				client: &client.ETHClient{
	//					Client: &ethclient.Client{},
	//				},
	//				ibcHandler: &ibchandler.Ibchandler{
	//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
	//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
	//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
	//				},
	//				signer:                    nil,
	//				connectionOpenedConfirmed: false,
	//			},
	//			args: args{
	//				ctx:        mockQueryContext,
	//				fromHeight: 0,
	//			},
	//			want:    nil,
	//			wantErr: false,
	//		},
	//	}
	//	for _, tt := range tests {
	//		t.Run(tt.name, func(t *testing.T) {
	//			chain := &Chain{
	//				config:                    tt.fields.config,
	//				pathEnd:                   tt.fields.pathEnd,
	//				homePath:                  tt.fields.homePath,
	//				chainID:                   tt.fields.chainID,
	//				codec:                     tt.fields.codec,
	//				msgEventListener:          tt.fields.msgEventListener,
	//				client:                    tt.fields.client,
	//				ibcHandler:                tt.fields.ibcHandler,
	//				signer:                    tt.fields.signer,
	//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
	//			}
	//			got, err := chain.findRecvPacketEvents(tt.args.ctx, tt.args.fromHeight)
	//			if (err != nil) != tt.wantErr {
	//				t.Errorf("Chain.findRecvPacketEvents() error = %v, wantErr %v", err, tt.wantErr)
	//				return
	//			}
	//			if !reflect.DeepEqual(got, tt.want) {
	//				t.Errorf("Chain.findRecvPacketEvents() = %v, want %v", got, tt.want)
	//				if diff := cmp.Diff(got, tt.want); diff != "" {
	//					t.Errorf("Chain.findRecvPacketEvents() differs: (-got +want)n%s", diff)
	//				}
	//			}
	//		})
	//	}
}

// TODO ethclient が FilterLogs してしまうため実施不可
func TestChain_findWriteAckEvents(t *testing.T) {
	t.Skip("cannot not possible UT is not possible because ethclient uses FilterLogs.")
	//
	//	stubChainConfig := ChainConfig{
	//		ChainId:    "********",
	//		EthChainId: 5151,
	//		RpcAddr:    "http://**********:8451",
	//		Signer: &codectypes.Any{
	//			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
	//			Value:                nil,
	//			XXX_NoUnkeyedLiteral: struct{}{},
	//			XXX_unrecognized:     nil,
	//			XXX_sizecache:        0,
	//		},
	//		IbcAddress:            "******************************************",
	//		InitialSendCheckpoint: 1,
	//		InitialRecvCheckpoint: 1,
	//		EnableDebugTrace:      false,
	//		AverageBlockTimeMsec:  2000,
	//		MaxRetryForInclusion:  3,
	//		GasEstimateRate: &Fraction{
	//			Numerator:   1,
	//			Denominator: 1,
	//		},
	//		MaxGasLimit: *********,
	//		TxType:      "auto",
	//	}
	//	stubPathEnd := &core.PathEnd{
	//		ChainID:      "********",
	//		ClientID:     "hb-ibft2-0",
	//		ConnectionID: "connection-0",
	//		ChannelID:    "channel-0",
	//		PortID:       "account-sync",
	//		Order:        "unordered",
	//		Version:      "account-sync-1",
	//	}
	//	stubHeight := clienttypes.Height{
	//		RevisionNumber: 0,
	//		RevisionHeight: 100,
	//	}
	//
	//	ctrl := gomock.NewController(t)
	//	mockQueryContext := core.NewMockQueryContext(ctrl)
	//	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
	//	mockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
	//
	//	type fields struct {
	//		config                    ChainConfig
	//		pathEnd                   *core.PathEnd
	//		homePath                  string
	//		chainID                   *big.Int
	//		codec                     codec.ProtoCodecMarshaler
	//		msgEventListener          core.MsgEventListener
	//		client                    *client.ETHClient
	//		ibcHandler                *ibchandler.Ibchandler
	//		signer                    Signer
	//		connectionOpenedConfirmed bool
	//	}
	//	type args struct {
	//		ctx        core.QueryContext
	//		fromHeight uint64
	//	}
	//	tests := []struct {
	//		name    string
	//		fields  fields
	//		args    args
	//		want    []*ibchandler.IbchandlerWriteAcknowledgement
	//		wantErr bool
	//	}{
	//		{
	//			name: "should find WriteAck.",
	//			fields: fields{
	//				config:           stubChainConfig,
	//				pathEnd:          stubPathEnd,
	//				homePath:         ".relayer",
	//				chainID:          &big.Int{},
	//				codec:            core.MakeCodec(),
	//				msgEventListener: nil,
	//				client: &client.ETHClient{
	//					Client: &ethclient.Client{},
	//				},
	//				ibcHandler: &ibchandler.Ibchandler{
	//					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
	//					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
	//					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
	//				},
	//				signer:                    nil,
	//				connectionOpenedConfirmed: false,
	//			},
	//			args: args{
	//				ctx:        mockQueryContext,
	//				fromHeight: 0,
	//			},
	//			want:    nil,
	//			wantErr: false,
	//		},
	//	}
	//	for _, tt := range tests {
	//		t.Run(tt.name, func(t *testing.T) {
	//			chain := &Chain{
	//				config:                    tt.fields.config,
	//				pathEnd:                   tt.fields.pathEnd,
	//				homePath:                  tt.fields.homePath,
	//				chainID:                   tt.fields.chainID,
	//				codec:                     tt.fields.codec,
	//				msgEventListener:          tt.fields.msgEventListener,
	//				client:                    tt.fields.client,
	//				ibcHandler:                tt.fields.ibcHandler,
	//				signer:                    tt.fields.signer,
	//				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
	//			}
	//			got, err := chain.findWriteAckEvents(tt.args.ctx, tt.args.fromHeight)
	//			if (err != nil) != tt.wantErr {
	//				t.Errorf("Chain.findWriteAckEvents() error = %v, wantErr %v", err, tt.wantErr)
	//				return
	//			}
	//			if !reflect.DeepEqual(got, tt.want) {
	//				t.Errorf("Chain.findWriteAckEvents() = %v, want %v", got, tt.want)
	//				if diff := cmp.Diff(got, tt.want); diff != "" {
	//					t.Errorf("Chain.findWriteAckEvents() differs: (-got +want)n%s", diff)
	//				}
	//			}
	//		})
	//	}
}

func TestChain_GetChannelLogger(t *testing.T) {

	stubChainConfig := ChainConfig{
		ChainId:    "********",
		EthChainId: 5151,
		RpcAddr:    "http://**********:8451",
		Signer: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.signers.hd.SignerConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		IbcAddress:            "******************************************",
		InitialSendCheckpoint: 1,
		InitialRecvCheckpoint: 1,
		EnableDebugTrace:      false,
		AverageBlockTimeMsec:  2000,
		MaxRetryForInclusion:  3,
		GasEstimateRate: &Fraction{
			Numerator:   1,
			Denominator: 1,
		},
		MaxGasLimit: *********,
		TxType:      "auto",
	}
	stubPathEnd := &core.PathEnd{
		ChainID:      "********",
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-1",
	}
	stubHeight := clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}

	ctrl := gomock.NewController(t)
	mockQueryContext := core.NewMockQueryContext(ctrl)
	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
	mockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
	type fields struct {
		config                    ChainConfig
		pathEnd                   *core.PathEnd
		homePath                  string
		chainID                   *big.Int
		codec                     codec.ProtoCodecMarshaler
		msgEventListener          core.MsgEventListener
		client                    *client.ETHClient
		ibcHandler                *ibchandler.Ibchandler
		signer                    Signer
		connectionOpenedConfirmed bool
	}
	tests := []struct {
		name   string
		fields fields
		want   *log.DcjpyLogger
	}{
		{
			name: "should return ChannelLogger.",
			fields: fields{
				config:           stubChainConfig,
				pathEnd:          stubPathEnd,
				homePath:         ".relayer",
				chainID:          &big.Int{},
				codec:            core.MakeCodec(),
				msgEventListener: nil,
				client: &client.ETHClient{
					Client: &ethclient.Client{},
				},
				ibcHandler: &ibchandler.Ibchandler{
					IbchandlerCaller:     ibchandler.IbchandlerCaller{},
					IbchandlerTransactor: ibchandler.IbchandlerTransactor{},
					IbchandlerFilterer:   ibchandler.IbchandlerFilterer{},
				},
				signer:                    nil,
				connectionOpenedConfirmed: false,
			},
			want: &log.DcjpyLogger{
				Logger: &slog.Logger{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			chain := &Chain{
				config:                    tt.fields.config,
				pathEnd:                   tt.fields.pathEnd,
				homePath:                  tt.fields.homePath,
				chainID:                   tt.fields.chainID,
				codec:                     tt.fields.codec,
				msgEventListener:          tt.fields.msgEventListener,
				client:                    tt.fields.client,
				ibcHandler:                tt.fields.ibcHandler,
				signer:                    tt.fields.signer,
				connectionOpenedConfirmed: tt.fields.connectionOpenedConfirmed,
			}
			got := chain.GetChannelLogger()
			if got == nil {
				t.Errorf("Chain.GetChannelLogger() = %v, want %v", got, tt.want)
			}
		})
	}
}

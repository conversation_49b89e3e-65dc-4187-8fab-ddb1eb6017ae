//go:generate mockgen -source=$GOFILE -package=hd -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package hd

import (
	fmt "fmt"
	"math/big"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/relay/ethereum"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/wallet"
)

var _ ethereum.SignerConfig = (*SignerConfig)(nil)

func (c *SignerConfig) Validate() error {
	if _, err := wallet.GetPrvKeyFromMnemonicAndHDWPath(c.Mnemonic, c.Path); err != nil {
		return fmt.Errorf("invalid mnemonic and/or path for HD wallet: %v", err)
	}
	return nil
}

func (c *SignerConfig) Build(chainID *big.Int) (ethereum.Signer, error) {
	return NewSigner(chainID, c.<PERSON>, c.Path)
}

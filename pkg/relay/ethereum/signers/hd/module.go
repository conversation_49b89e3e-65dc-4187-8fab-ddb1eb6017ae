//go:generate mockgen -source=$GOFILE -package=hd -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package hd

import (
	codectypes "github.com/cosmos/cosmos-sdk/codec/types"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/config"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/relay/ethereum"
	"github.com/spf13/cobra"
)

type Module struct{}

var _ config.ModuleI = (*Module)(nil)

const ModuleName = "ethereum.signers.hd"

// Name returns the name of the module
func (Module) Name() string {
	return ModuleName
}

// RegisterInterfaces registers the module interfaces to protobuf Any.
func (Module) RegisterInterfaces(registry codectypes.InterfaceRegistry) {
	registry.RegisterImplementations(
		(*ethereum.SignerConfig)(nil),
		&SignerConfig{},
	)
}

// GetCmd returns the command
func (Module) GetCmd(ctx *config.Context) *cobra.Command {
	return nil
}

//go:generate mockgen -source=$GOFILE -package=hd -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package hd

import (
	"reflect"
	"testing"

	codectypes "github.com/cosmos/cosmos-sdk/codec/types"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/config"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
	"github.com/spf13/cobra"
)

func TestModule_Name(t *testing.T) {
	tests := []struct {
		name string
		m    Module
		want string
	}{
		{
			name: "should return ModuleName",
			m:    Module{},
			want: "ethereum.signers.hd",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := Module{}
			if got := m.Name(); got != tt.want {
				t.Errorf("Module.Name() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestModule_RegisterInterfaces(t *testing.T) {
	type args struct {
		registry codectypes.InterfaceRegistry
	}
	tests := []struct {
		name string
		m    Module
		args args
	}{
		{
			name: "should register Interfaces.",
			m:    Module{},
			args: args{
				registry: core.MakeCodec().InterfaceRegistry(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := Module{}
			m.RegisterInterfaces(tt.args.registry)
		})
	}
}

func TestModule_GetCmd(t *testing.T) {

	stubModules := []config.ModuleI{
		//ethereum.Module{},
		Module{},
		//ibft2.Module{},
	}

	stubGlobalConfig := config.GlobalConfig{
		Timeout:        "10s",
		LightCacheSize: 20,
		LoggerConfig: config.LoggerConfig{
			Level:  "INFO",
			Format: "text",
			Output: "stdout",
		},
	}
	stubCodec := core.MakeCodec()
	for _, module := range stubModules {
		module.RegisterInterfaces(stubCodec.InterfaceRegistry())
	}

	type args struct {
		ctx *config.Context
	}
	tests := []struct {
		name string
		m    Module
		args args
		want *cobra.Command
	}{
		{
			name: "should return nil.",
			m:    Module{},
			args: args{
				ctx: &config.Context{
					Modules: nil,
					Codec:   stubCodec,
					Config: &config.Config{
						Global:     stubGlobalConfig,
						Chains:     []core.ChainProverConfig{},
						Paths:      core.Paths{},
						ConfigPath: ".relayer",
					},
				},
			},
			want: nil,
		},
		//// TODO 自パッケージ配下で自身の Modules は指定できないため実施不可
		//{
		//	name: "should return Cmd.",
		//	m:    Module{},
		//	args: args{
		//		ctx: &config.Context{
		//			Modules: stubModules,
		//			Codec:   stubCodec,
		//			Config: &config.Config{
		//				Global: stubGlobalConfig,
		//				Chains: []core.ChainProverConfig{
		//				},
		//				Paths: map[string]*core.Path{
		//					"account-sync": {
		//						Src: &core.PathEnd{
		//							ChainID:      "1111",
		//							ClientID:     "hb-ibft2-0",
		//							ConnectionID: "connection-0",
		//							ChannelID:    "channel-0",
		//							PortID:       "account-sync",
		//							Order:        "unordered",
		//							Version:      "account-sync-0",
		//						},
		//						Dst: &core.PathEnd{
		//							ChainID:      "2222",
		//							ClientID:     "hb-ibft2-1",
		//							ConnectionID: "connection-1",
		//							ChannelID:    "channel-1",
		//							PortID:       "account-sync",
		//							Order:        "unordered",
		//							Version:      "account-sync-1",
		//						},
		//						Strategy: &core.StrategyCfg{
		//							Type:     "naive",
		//							SrcNoack: false,
		//							DstNoack: false,
		//						},
		//					},
		//				},
		//				ConfigPath: ".relayer",
		//			},
		//		},
		//	},
		//	want: &cobra.Command{
		//		Use:                    "",
		//		Aliases:                nil,
		//		SuggestFor:             nil,
		//		Short:                  "",
		//		GroupID:                "",
		//		Long:                   "",
		//		Example:                "",
		//		ValidArgs:              nil,
		//		ValidArgsFunction:      nil,
		//		Args:                   nil,
		//		ArgAliases:             nil,
		//		BashCompletionFunction: "",
		//		Deprecated:             "",
		//		Annotations:            nil,
		//		Version:                "",
		//		PersistentPreRun:       nil,
		//		PersistentPreRunE:      nil,
		//		PreRun:                 nil,
		//		PreRunE:                nil,
		//		Run:                    nil,
		//		RunE:                   nil,
		//		PostRun:                nil,
		//		PostRunE:               nil,
		//		PersistentPostRun:      nil,
		//		PersistentPostRunE:     nil,
		//		FParseErrWhitelist: cobra.FParseErrWhitelist{
		//			UnknownFlags: false,
		//		},
		//		CompletionOptions: cobra.CompletionOptions{
		//			DisableDefaultCmd:   false,
		//			DisableNoDescFlag:   false,
		//			DisableDescriptions: false,
		//			HiddenDefaultCmd:    false,
		//		},
		//		TraverseChildren:           false,
		//		Hidden:                     false,
		//		SilenceErrors:              false,
		//		SilenceUsage:               false,
		//		DisableFlagParsing:         false,
		//		DisableAutoGenTag:          false,
		//		DisableFlagsInUseLine:      false,
		//		DisableSuggestions:         false,
		//		SuggestionsMinimumDistance: 0,
		//	},
		//},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := Module{}
			if got := m.GetCmd(tt.args.ctx); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Module.GetCmd() = %v, want %v", got, tt.want)
			}
		})
	}
}

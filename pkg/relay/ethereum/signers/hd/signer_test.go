//go:generate mockgen -source=$GOFILE -package=hd -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package hd

import (
	"crypto/ecdsa"
	"math/big"
	"reflect"
	"testing"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/wallet"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/google/go-cmp/cmp"
)

func TestNewSigner(t *testing.T) {

	stubMnemonic := "math razor capable expose worth grape metal sunset metal sudden usage scheme"
	stubPath := "m/44'/60'/0'/0/0"
	stubChainID := big.NewInt(int64(5151))
	stubKey, _ := wallet.GetPrvKeyFromMnemonicAndHDWPath(stubMnemonic, stubPath)

	type args struct {
		chainID  *big.Int
		mnemonic string
		path     string
	}
	tests := []struct {
		name    string
		args    args
		want    *Signer
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "should error when mnemonic or path is invalid.",
			args: args{
				chainID:  stubChainID,
				mnemonic: "",
				path:     stubPath,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return signer.",
			args: args{
				chainID:  stubChainID,
				mnemonic: stubMnemonic,
				path:     stubPath,
			},
			want: &Signer{
				signer: types.LatestSignerForChainID(stubChainID),
				key:    stubKey,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewSigner(tt.args.chainID, tt.args.mnemonic, tt.args.path)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewSigner() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("NewSigner() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

func TestSigner_Address(t *testing.T) {

	stubMnemonic := "math razor capable expose worth grape metal sunset metal sudden usage scheme"
	stubPath := "m/44'/60'/0'/0/0"
	stubChainID := big.NewInt(int64(5151))
	stubKey, _ := wallet.GetPrvKeyFromMnemonicAndHDWPath(stubMnemonic, stubPath)

	type fields struct {
		signer types.Signer
		key    *ecdsa.PrivateKey
	}
	tests := []struct {
		name   string
		fields fields
		want   common.Address
	}{
		{
			name: "should return address.",
			fields: fields{
				signer: types.LatestSignerForChainID(stubChainID),
				key:    stubKey,
			},
			want: common.HexToAddress("******************************************"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Signer{
				signer: tt.fields.signer,
				key:    tt.fields.key,
			}
			if got := s.Address(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Signer.Address() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Signer.Address() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

func TestSigner_Sign(t *testing.T) {

	stubMnemonic := "math razor capable expose worth grape metal sunset metal sudden usage scheme"
	stubPath := "m/44'/60'/0'/0/0"
	stubChainID := big.NewInt(int64(5151))
	stubKey, _ := wallet.GetPrvKeyFromMnemonicAndHDWPath(stubMnemonic, stubPath)

	//stubTx := types.NewTx(&types.LegacyTx{
	//	Nonce:    0,
	//	GasPrice: big.NewInt(100),
	//	Gas:      0,
	//	To:       &common.Address{},
	//	Value:    big.NewInt(100),
	//	Data:     nil,
	//})

	type fields struct {
		signer types.Signer
		key    *ecdsa.PrivateKey
	}
	type args struct {
		address common.Address
		tx      *types.Transaction
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *types.Transaction
		wantErr bool
	}{
		{
			name: "should return unauthorized address error.",
			fields: fields{
				signer: types.LatestSignerForChainID(stubChainID),
				key:    stubKey,
			},
			args: args{
				address: common.Address{},
				tx:      &types.Transaction{},
			},
			want:    nil,
			wantErr: true,
		},
		//// TODO types.Transaction.inner が unexported なのでテスト値設定できずなので実施不可
		//{
		//	name: "should return failed to sign error.",
		//	fields: fields{
		//		signer: types.LatestSignerForChainID(stubChainID),
		//		key:    stubKey,
		//	},
		//	args: args{
		//		address: common.HexToAddress("******************************************"),
		//		tx: types.NewTx(&types.LegacyTx{
		//			Nonce:    0,
		//			GasPrice: big.NewInt(100),
		//			Gas:      0,
		//			To:       &common.Address{},
		//			Value:    big.NewInt(100),
		//			Data:     nil,
		//		}),
		//	},
		//	want:    nil,
		//	wantErr: true,
		//},
		//// TODO types.Transaction.inner が unexported なのでテスト値設定できずなので実施不可
		//{
		//	name: "should return a new transaction with the given signature.",
		//	fields: fields{
		//		signer: types.LatestSignerForChainID(stubChainID),
		//		key:    stubKey,
		//	},
		//	args: args{
		//		address: common.HexToAddress("******************************************"),
		//		tx:      stubTx,
		//	},
		//	want:    stubTx,
		//	wantErr: false,
		//},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Signer{
				signer: tt.fields.signer,
				key:    tt.fields.key,
			}
			got, err := s.Sign(tt.args.address, tt.args.tx)
			if (err != nil) != tt.wantErr {
				t.Errorf("Signer.Sign() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("Signer.Sign() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

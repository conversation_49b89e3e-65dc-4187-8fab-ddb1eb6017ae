// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: relayer/chains/ethereum/signers/hd/config.proto

package hd

import (
	fmt "fmt"
	_ "github.com/cosmos/gogoproto/gogoproto"
	proto "github.com/cosmos/gogoproto/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type SignerConfig struct {
	Mnemonic string `protobuf:"bytes,1,opt,name=mnemonic,proto3" json:"mnemonic,omitempty"`
	Path     string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
}

func (m *SignerConfig) Reset()         { *m = SignerConfig{} }
func (m *SignerConfig) String() string { return proto.CompactTextString(m) }
func (*SignerConfig) ProtoMessage()    {}
func (*SignerConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_a221898db767a8da, []int{0}
}
func (m *SignerConfig) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SignerConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SignerConfig.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SignerConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SignerConfig.Merge(m, src)
}
func (m *SignerConfig) XXX_Size() int {
	return m.Size()
}
func (m *SignerConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_SignerConfig.DiscardUnknown(m)
}

var xxx_messageInfo_SignerConfig proto.InternalMessageInfo

func init() {
	proto.RegisterType((*SignerConfig)(nil), "relayer.chains.ethereum.signers.hd.SignerConfig")
}

func init() {
	proto.RegisterFile("relayer/chains/ethereum/signers/hd/config.proto", fileDescriptor_a221898db767a8da)
}

var fileDescriptor_a221898db767a8da = []byte{
	// 228 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x8f, 0xbb, 0x4e, 0x03, 0x31,
	0x10, 0x45, 0xd7, 0x08, 0x21, 0x58, 0x51, 0x59, 0x14, 0x51, 0x0a, 0x0b, 0xa5, 0xa2, 0x59, 0x4f,
	0x41, 0x4f, 0x01, 0x0d, 0x35, 0x74, 0x74, 0x7e, 0x4c, 0x6c, 0x43, 0xfc, 0x90, 0xd7, 0x5b, 0xe4,
	0x2f, 0xf8, 0xac, 0x94, 0x29, 0x29, 0x61, 0xf7, 0x47, 0x10, 0xde, 0x84, 0x2a, 0xdd, 0x9d, 0xd1,
	0x3d, 0xa3, 0x39, 0x2d, 0x64, 0xdc, 0x88, 0x2d, 0x66, 0x50, 0x56, 0xb8, 0xd0, 0x03, 0x16, 0x8b,
	0x19, 0x07, 0x0f, 0xbd, 0x33, 0x01, 0x73, 0x0f, 0x56, 0x83, 0x8a, 0x61, 0xed, 0x0c, 0x4f, 0x39,
	0x96, 0x48, 0x57, 0x07, 0x80, 0xcf, 0x00, 0x3f, 0x02, 0xfc, 0x00, 0x70, 0xab, 0x97, 0x37, 0x26,
	0x9a, 0x58, 0xeb, 0xf0, 0x97, 0x66, 0x72, 0xf5, 0xd0, 0x5e, 0xbf, 0xd6, 0xce, 0x53, 0xbd, 0x47,
	0x97, 0xed, 0xa5, 0x0f, 0xe8, 0x63, 0x70, 0x6a, 0x41, 0x6e, 0xc9, 0xdd, 0xd5, 0xcb, 0xff, 0x4c,
	0x69, 0x7b, 0x9e, 0x44, 0xb1, 0x8b, 0xb3, 0xba, 0xaf, 0xf9, 0x71, 0xbd, 0xfb, 0x61, 0xcd, 0x6e,
	0x64, 0x64, 0x3f, 0x32, 0xf2, 0x3d, 0x32, 0xf2, 0x39, 0xb1, 0x66, 0x3f, 0xb1, 0xe6, 0x6b, 0x62,
	0xcd, 0xdb, 0xb3, 0x71, 0xc5, 0x0e, 0x92, 0xab, 0xe8, 0x41, 0xa3, 0x1a, 0x72, 0xc6, 0xd2, 0x6d,
	0x84, 0x04, 0xad, 0xa4, 0xe9, 0xb4, 0x7a, 0x4f, 0xdb, 0xee, 0xe8, 0x9a, 0x3e, 0xcc, 0xec, 0x7d,
	0x4a, 0x57, 0x5e, 0xd4, 0x77, 0xef, 0x7f, 0x03, 0x00, 0x00, 0xff, 0xff, 0x3d, 0xed, 0x79, 0x1f,
	0x1b, 0x01, 0x00, 0x00,
}

func (m *SignerConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SignerConfig) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SignerConfig) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Path) > 0 {
		i -= len(m.Path)
		copy(dAtA[i:], m.Path)
		i = encodeVarintConfig(dAtA, i, uint64(len(m.Path)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Mnemonic) > 0 {
		i -= len(m.Mnemonic)
		copy(dAtA[i:], m.Mnemonic)
		i = encodeVarintConfig(dAtA, i, uint64(len(m.Mnemonic)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintConfig(dAtA []byte, offset int, v uint64) int {
	offset -= sovConfig(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *SignerConfig) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Mnemonic)
	if l > 0 {
		n += 1 + l + sovConfig(uint64(l))
	}
	l = len(m.Path)
	if l > 0 {
		n += 1 + l + sovConfig(uint64(l))
	}
	return n
}

func sovConfig(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozConfig(x uint64) (n int) {
	return sovConfig(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *SignerConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConfig
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SignerConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SignerConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Mnemonic", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConfig
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConfig
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Mnemonic = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Path", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthConfig
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthConfig
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Path = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipConfig(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConfig
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipConfig(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowConfig
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthConfig
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupConfig
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthConfig
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthConfig        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowConfig          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupConfig = fmt.Errorf("proto: unexpected end of group")
)

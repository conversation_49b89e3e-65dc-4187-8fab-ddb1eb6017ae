//go:generate mockgen -source=$GOFILE -package=hd -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package hd

import (
	"math/big"
	"reflect"
	"testing"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/relay/ethereum"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/wallet"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/google/go-cmp/cmp"
)

func TestSignerConfig_Validate(t *testing.T) {
	type fields struct {
		Mnemonic string
		Path     string
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "invalid mnemonic and/or path for HD wallet.",
			fields: fields{
				Mnemonic: "",
				Path:     "",
			},
			wantErr: true,
		},
		{
			name: "validate SignerConfig.",
			fields: fields{
				Mnemonic: "math razor capable expose worth grape metal sunset metal sudden usage scheme",
				Path:     "m/44'/60'/0'/0/0",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &SignerConfig{
				Mnemonic: tt.fields.Mnemonic,
				Path:     tt.fields.Path,
			}
			if err := c.Validate(); (err != nil) != tt.wantErr {
				t.Errorf("SignerConfig.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestSignerConfig_Build(t *testing.T) {

	stubMnemonic := "math razor capable expose worth grape metal sunset metal sudden usage scheme"
	stubPath := "m/44'/60'/0'/0/0"
	stubChainID := big.NewInt(int64(5151))
	stubKey, _ := wallet.GetPrvKeyFromMnemonicAndHDWPath(stubMnemonic, stubPath)

	type fields struct {
		Mnemonic string
		Path     string
	}
	type args struct {
		chainID *big.Int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    ethereum.Signer
		wantErr bool
	}{
		{
			name: "should return Singer instance.",
			fields: fields{
				Mnemonic: stubMnemonic,
				Path:     stubPath,
			},
			args: args{
				chainID: stubChainID,
			},
			want: &Signer{
				types.LatestSignerForChainID(stubChainID),
				stubKey,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &SignerConfig{
				Mnemonic: tt.fields.Mnemonic,
				Path:     tt.fields.Path,
			}
			got, err := c.Build(tt.args.chainID)
			if (err != nil) != tt.wantErr {
				t.Errorf("SignerConfig.Build() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("SignerConfig.Build() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: relayer/provers/ibft2/config/config.proto

package module

import (
	fmt "fmt"
	_ "github.com/cosmos/gogoproto/gogoproto"
	proto "github.com/cosmos/gogoproto/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type ProverConfig struct {
	TrustLevelNumerator   uint32 `protobuf:"varint,1,opt,name=trust_level_numerator,json=trustLevelNumerator,proto3" json:"trust_level_numerator,omitempty"`
	TrustLevelDenominator uint32 `protobuf:"varint,2,opt,name=trust_level_denominator,json=trustLevelDenominator,proto3" json:"trust_level_denominator,omitempty"`
	TrustingPeriod        uint64 `protobuf:"varint,3,opt,name=trusting_period,json=trustingPeriod,proto3" json:"trusting_period,omitempty"`
}

func (m *ProverConfig) Reset()         { *m = ProverConfig{} }
func (m *ProverConfig) String() string { return proto.CompactTextString(m) }
func (*ProverConfig) ProtoMessage()    {}
func (*ProverConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_912b2ba2cd96b01e, []int{0}
}
func (m *ProverConfig) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ProverConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ProverConfig.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ProverConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProverConfig.Merge(m, src)
}
func (m *ProverConfig) XXX_Size() int {
	return m.Size()
}
func (m *ProverConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_ProverConfig.DiscardUnknown(m)
}

var xxx_messageInfo_ProverConfig proto.InternalMessageInfo

func init() {
	proto.RegisterType((*ProverConfig)(nil), "relayer.provers.ibft2.config.ProverConfig")
}

func init() {
	proto.RegisterFile("relayer/provers/ibft2/config/config.proto", fileDescriptor_912b2ba2cd96b01e)
}

var fileDescriptor_912b2ba2cd96b01e = []byte{
	// 273 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x90, 0xcf, 0x4a, 0xc3, 0x40,
	0x10, 0x87, 0xb3, 0x2a, 0x1e, 0x82, 0x7f, 0x20, 0x5a, 0x2c, 0x22, 0x4b, 0xf1, 0x62, 0x3d, 0x34,
	0x0b, 0x15, 0xbc, 0x0a, 0xea, 0x51, 0xa4, 0xf4, 0x28, 0x48, 0x48, 0xb2, 0xd3, 0x75, 0x25, 0xc9,
	0x84, 0xe9, 0xa6, 0xd0, 0xb7, 0xf0, 0x1d, 0x7c, 0x99, 0x1e, 0x7b, 0xf4, 0xa8, 0xc9, 0x8b, 0x48,
	0xa7, 0x29, 0xf5, 0xe0, 0x69, 0x97, 0xf9, 0xbe, 0xef, 0xf2, 0xf3, 0xaf, 0x09, 0xb2, 0x78, 0x0e,
	0xa4, 0x4a, 0xc2, 0x19, 0xd0, 0x54, 0xd9, 0x64, 0xe2, 0x86, 0x2a, 0xc5, 0x62, 0x62, 0x4d, 0xfb,
	0x84, 0x25, 0xa1, 0xc3, 0xe0, 0xa2, 0x55, 0xc3, 0x56, 0x0d, 0x59, 0x0d, 0xd7, 0xce, 0xf9, 0xa9,
	0x41, 0x83, 0x2c, 0xaa, 0xd5, 0x6f, 0xdd, 0x5c, 0x7e, 0x0a, 0xff, 0x60, 0xc4, 0xfa, 0x03, 0x6b,
	0xc1, 0xd0, 0xef, 0x38, 0xaa, 0xa6, 0x2e, 0xca, 0x60, 0x06, 0x59, 0x54, 0x54, 0x39, 0x50, 0xec,
	0x90, 0xba, 0xa2, 0x27, 0xfa, 0x87, 0xe3, 0x13, 0x86, 0x4f, 0x2b, 0xf6, 0xbc, 0x41, 0xc1, 0xad,
	0x7f, 0xf6, 0xb7, 0xd1, 0x50, 0x60, 0x6e, 0x0b, 0xae, 0x76, 0xb8, 0xea, 0x6c, 0xab, 0xc7, 0x2d,
	0x0c, 0xae, 0xfc, 0x63, 0x06, 0xb6, 0x30, 0x51, 0x09, 0x64, 0x51, 0x77, 0x77, 0x7b, 0xa2, 0xbf,
	0x37, 0x3e, 0xda, 0x9c, 0x47, 0x7c, 0xbd, 0x7f, 0x5d, 0xfc, 0x48, 0x6f, 0x51, 0x4b, 0xb1, 0xac,
	0xa5, 0xf8, 0xae, 0xa5, 0xf8, 0x68, 0xa4, 0xb7, 0x6c, 0xa4, 0xf7, 0xd5, 0x48, 0xef, 0xe5, 0xce,
	0x58, 0xf7, 0x56, 0x25, 0x61, 0x8a, 0xb9, 0xd2, 0x90, 0x56, 0x44, 0xe0, 0x06, 0x59, 0x9c, 0x28,
	0x9d, 0x26, 0x66, 0xa0, 0xd3, 0xf7, 0x72, 0x3e, 0xf8, 0x7f, 0xc5, 0x1c, 0x75, 0x95, 0x41, 0xb2,
	0xcf, 0x5b, 0xdc, 0xfc, 0x06, 0x00, 0x00, 0xff, 0xff, 0x0f, 0x7d, 0x8d, 0x8e, 0x6c, 0x01, 0x00,
	0x00,
}

func (m *ProverConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ProverConfig) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ProverConfig) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.TrustingPeriod != 0 {
		i = encodeVarintConfig(dAtA, i, uint64(m.TrustingPeriod))
		i--
		dAtA[i] = 0x18
	}
	if m.TrustLevelDenominator != 0 {
		i = encodeVarintConfig(dAtA, i, uint64(m.TrustLevelDenominator))
		i--
		dAtA[i] = 0x10
	}
	if m.TrustLevelNumerator != 0 {
		i = encodeVarintConfig(dAtA, i, uint64(m.TrustLevelNumerator))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintConfig(dAtA []byte, offset int, v uint64) int {
	offset -= sovConfig(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *ProverConfig) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TrustLevelNumerator != 0 {
		n += 1 + sovConfig(uint64(m.TrustLevelNumerator))
	}
	if m.TrustLevelDenominator != 0 {
		n += 1 + sovConfig(uint64(m.TrustLevelDenominator))
	}
	if m.TrustingPeriod != 0 {
		n += 1 + sovConfig(uint64(m.TrustingPeriod))
	}
	return n
}

func sovConfig(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozConfig(x uint64) (n int) {
	return sovConfig(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *ProverConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowConfig
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ProverConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ProverConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrustLevelNumerator", wireType)
			}
			m.TrustLevelNumerator = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TrustLevelNumerator |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrustLevelDenominator", wireType)
			}
			m.TrustLevelDenominator = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TrustLevelDenominator |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrustingPeriod", wireType)
			}
			m.TrustingPeriod = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TrustingPeriod |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipConfig(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthConfig
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipConfig(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowConfig
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowConfig
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthConfig
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupConfig
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthConfig
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthConfig        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowConfig          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupConfig = fmt.Errorf("proto: unexpected end of group")
)

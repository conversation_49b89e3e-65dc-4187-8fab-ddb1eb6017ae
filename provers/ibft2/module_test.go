package module

import (
	"testing"

	codectypes "github.com/cosmos/cosmos-sdk/codec/types"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/config"
)

func TestModule_Name(t *testing.T) {
	t.Run("should return 'ibft2-prover'", func(t *testing.T) {
		m := Module{}
		expected := "ibft2-prover"
		if m.Name() != expected {
			t.Errorf("got = %v, expected = %v", m.Name(), expected)
		}
	})
}

func TestModule_RegisterInterfaces(t *testing.T) {
	t.Run("should register interfaces", func(t *testing.T) {
		m := Module{}

		registry := codectypes.NewInterfaceRegistry()
		m.RegisterInterfaces(registry)

		if err := registry.EnsureRegistered(&ProverConfig{}); err != nil {
			t.Errorf("got = %v", err)
		}
		if err := registry.EnsureRegistered(&ClientState{}); err != nil {
			t.Errorf("got = %v", err)
		}
		if err := registry.EnsureRegistered(&ConsensusState{}); err != nil {
			t.Errorf("got = %v", err)
		}
	})
}

func TestModule_Cmd(t *testing.T) {
	t.Run("should return nil", func(t *testing.T) {
		m := Module{}
		got := m.GetCmd(&config.Context{})
		if got != nil {
			t.Errorf("got = %v", got)
		}
	})
}

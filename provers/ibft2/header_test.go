package module

import (
	"testing"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/rlp"
)

func TestHeader_ClientType(t *testing.T) {
	t.Run("should return 'ibft2'", func(t *testing.T) {
		h := Header{}
		ct := h.ClientType()
		expected := "ibft2"
		if ct != expected {
			t.<PERSON>("got = %v, expected = %v", ct, expected)
		}
	})
}

func TestHeader_GetHeight(t *testing.T) {
	t.Run("can get height from block header", func(t *testing.T) {
		// Reference: https://ethereum.stackexchange.com/questions/67055/block-header-hash-verification
		blockEnc := common.FromHex("f90213a01e77d8f1267348b516ebc4f4da1e2aa59f85f0cbd853949500ffac8bfc38ba14a01dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347942a65aca4d5fc5b5c859090a6c34d164135398226a00b5e4386680f43c224c5c037efc0b645c8e1c3f6b30da0eec07272b4e6f8cd89a056e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421a056e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421b901000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000086057a418a7c3e83061a80832fefd880845622efdc96d583010202844765746885676f312e35856c696e7578a03fbea7af642a4e20cd93a945a1f5e23bd72fc5261153e09102cf718980aeff38886af23caae95692ef")
		h := Header{
			BesuHeaderRlp: blockEnc,
		}
		height := h.GetHeight()
		if height.GetRevisionNumber() != 0 {
			t.Errorf("got = %v, expected = %v", height.GetRevisionNumber(), 0)
		}
		if height.GetRevisionHeight() != 400000 {
			t.<PERSON><PERSON><PERSON>("got = %v, expected = %v", height.GetRevisionHeight(), 400000)
		}
	})

	t.Run("should panic", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		h := Header{}
		h.GetHeight()
	})
}

func TestHeader_ValidateBasic(t *testing.T) {
	t.Run("can pass the validation", func(t *testing.T) {
		// Reference: https://ethereum.stackexchange.com/questions/67055/block-header-hash-verification
		blockEnc := common.FromHex("f90213a01e77d8f1267348b516ebc4f4da1e2aa59f85f0cbd853949500ffac8bfc38ba14a01dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347942a65aca4d5fc5b5c859090a6c34d164135398226a00b5e4386680f43c224c5c037efc0b645c8e1c3f6b30da0eec07272b4e6f8cd89a056e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421a056e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421b901000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000086057a418a7c3e83061a80832fefd880845622efdc96d583010202844765746885676f312e35856c696e7578a03fbea7af642a4e20cd93a945a1f5e23bd72fc5261153e09102cf718980aeff38886af23caae95692ef")
		stateEnc, err := rlp.EncodeToBytes(
			[][][]byte{
				{
					[]byte{
						1, 2, 3, 4,
					},
				},
			},
		)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		h := Header{
			BesuHeaderRlp:     blockEnc,
			AccountStateProof: stateEnc,
		}
		err = h.ValidateBasic()
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})

	t.Run("should return an error when decodeEthHeader() fails", func(t *testing.T) {
		stateEnc, err := rlp.EncodeToBytes(
			[][][]byte{
				{
					[]byte{
						1, 2, 3, 4,
					},
				},
			},
		)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		h := Header{
			AccountStateProof: stateEnc,
		}
		err = h.ValidateBasic()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error when decodeAccountProof() fails", func(t *testing.T) {
		// Reference: https://ethereum.stackexchange.com/questions/67055/block-header-hash-verification
		blockEnc := common.FromHex("f90213a01e77d8f1267348b516ebc4f4da1e2aa59f85f0cbd853949500ffac8bfc38ba14a01dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347942a65aca4d5fc5b5c859090a6c34d164135398226a00b5e4386680f43c224c5c037efc0b645c8e1c3f6b30da0eec07272b4e6f8cd89a056e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421a056e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421b901000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000086057a418a7c3e83061a80832fefd880845622efdc96d583010202844765746885676f312e35856c696e7578a03fbea7af642a4e20cd93a945a1f5e23bd72fc5261153e09102cf718980aeff38886af23caae95692ef")
		h := Header{
			BesuHeaderRlp: blockEnc,
		}
		err := h.ValidateBasic()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func TestHeader_decodeEthHeader(t *testing.T) {
	t.Run("can decode block header", func(t *testing.T) {
		// Reference: https://ethereum.stackexchange.com/questions/67055/block-header-hash-verification
		blockEnc := common.FromHex("f90213a01e77d8f1267348b516ebc4f4da1e2aa59f85f0cbd853949500ffac8bfc38ba14a01dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347942a65aca4d5fc5b5c859090a6c34d164135398226a00b5e4386680f43c224c5c037efc0b645c8e1c3f6b30da0eec07272b4e6f8cd89a056e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421a056e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421b901000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000086057a418a7c3e83061a80832fefd880845622efdc96d583010202844765746885676f312e35856c696e7578a03fbea7af642a4e20cd93a945a1f5e23bd72fc5261153e09102cf718980aeff38886af23caae95692ef")
		h := Header{
			BesuHeaderRlp: blockEnc,
		}
		headerDecoded, err := h.decodeEthHeader()
		if err != nil {
			t.Errorf("got = %v", err)
		}
		if headerDecoded.Difficulty.Uint64() != uint64(6022643743806) {
			t.Errorf("got = %v, expected = %v", headerDecoded.Difficulty.Uint64(), 6022643743806)
		}
		if headerDecoded.ParentHash.Hex() != "0x1e77d8f1267348b516ebc4f4da1e2aa59f85f0cbd853949500ffac8bfc38ba14" {
			t.Errorf("got = %v, expected = %v", headerDecoded.ParentHash.Hex(), "0x1e77d8f1267348b516ebc4f4da1e2aa59f85f0cbd853949500ffac8bfc38ba14")
		}
		if headerDecoded.TxHash.Hex() != "0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421" {
			t.Errorf("got = %v, expected = %v", headerDecoded.TxHash.Hex(), "0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421")
		}
		if headerDecoded.Number.Uint64() != uint64(400000) {
			t.Errorf("got = %v, expected = %v", headerDecoded.Number.Uint64(), 400000)
		}
	})

	t.Run("should return an error", func(t *testing.T) {
		h := Header{}
		_, err := h.decodeEthHeader()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func TestHeader_decodeAccountProof(t *testing.T) {
	t.Run("can decode byte arrays", func(t *testing.T) {
		stateEnc, err := rlp.EncodeToBytes(
			[][][]byte{
				{
					[]byte{
						1, 2, 3, 4,
					},
				},
			},
		)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		h := Header{
			AccountStateProof: stateEnc,
		}
		_, err = h.decodeAccountProof()
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})

	t.Run("should return an error when decode fails", func(t *testing.T) {
		h := Header{}
		_, err := h.decodeAccountProof()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func TestHeader_ethHeightToPB(t *testing.T) {
	t.Run("should return Height'", func(t *testing.T) {
		h := ethHeightToPB(5)
		numExpected := uint64(0)
		if h.RevisionNumber != numExpected {
			t.Errorf("got = %v, expected = %v", h.RevisionNumber, numExpected)
		}
		heiExpected := uint64(5)
		if h.RevisionHeight != heiExpected {
			t.Errorf("got = %v, expected = %v", h.RevisionHeight, heiExpected)
		}
	})
}

package module

import (
	"testing"

	sdk "github.com/cosmos/cosmos-sdk/types"
	types "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
)

func TestIbft2_ClientType(t *testing.T) {
	t.Run("should return 'hb-ibft2'", func(t *testing.T) {
		cs := ClientState{
			ChainId:         "1111",
			IbcStoreAddress: []byte{},
			LatestHeight: types.Height{
				RevisionNumber: 1,
				RevisionHeight: 2,
			},
		}

		ct := cs.ClientType()
		expected := "hb-ibft2"
		if ct != expected {
			t.Errorf("got = %v, expected = %v", ct, expected)
		}
	})
}

func TestIbft2_GetLatestHeight(t *testing.T) {
	t.Run("should return the latest height", func(t *testing.T) {
		cs := ClientState{
			ChainId:         "1111",
			IbcStoreAddress: []byte{},
			LatestHeight: types.Height{
				RevisionNumber: 1,
				RevisionHeight: 2,
			},
		}

		lh := cs.GetLatestHeight()
		numExpected := uint64(1)
		if lh.GetRevisionNumber() != numExpected {
			t.Errorf("got = %v, expected = %v", lh.GetRevisionNumber(), numExpected)
		}
		heiExpected := uint64(2)
		if lh.GetRevisionHeight() != heiExpected {
			t.Errorf("got = %v, expected = %v", lh.GetRevisionHeight(), heiExpected)
		}
	})
}

func TestIbft2_Validate(t *testing.T) {
	t.Run("should return nil", func(t *testing.T) {
		cs := ClientState{
			ChainId:         "1111",
			IbcStoreAddress: []byte{},
			LatestHeight: types.Height{
				RevisionNumber: 1,
				RevisionHeight: 2,
			},
		}

		err := cs.Validate()
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})
}

func TestIbft2_Status(t *testing.T) {
	t.Run("should panic", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cs := ClientState{}

		cs.Status(sdk.Context{}, nil, nil)
	})
}

func TestIbft2_ExportMetadata(t *testing.T) {
	t.Run("should panic", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cs := ClientState{}
		cs.ExportMetadata(nil)
	})
}

func TestIbft2_ZeroCustomFields(t *testing.T) {
	t.Run("should panic", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cs := ClientState{}
		cs.ZeroCustomFields()
	})
}

func TestIbft2_GetTimestampAtHeight(t *testing.T) {
	t.Run("should panic", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cs := ClientState{}
		cs.GetTimestampAtHeight(sdk.Context{}, nil, nil, nil)
	})
}

func TestIbft2_Initialize(t *testing.T) {
	t.Run("should panic", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cs := ClientState{}
		cs.Initialize(sdk.Context{}, nil, nil, nil)
	})
}

func TestIbft2_VerifyMembership(t *testing.T) {
	t.Run("should panic", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cs := ClientState{}
		cs.VerifyMembership(sdk.Context{}, nil, nil, nil, 0, 0, []byte{}, nil, []byte{})
	})
}

func TestIbft2_VerifyNonMembership(t *testing.T) {
	t.Run("should panic", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cs := ClientState{}
		cs.VerifyNonMembership(sdk.Context{}, nil, nil, nil, 0, 0, []byte{}, nil)
	})
}

func TestIbft2_VerifyClientMessage(t *testing.T) {
	t.Run("should panic", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cs := ClientState{}
		cs.VerifyClientMessage(sdk.Context{}, nil, nil, nil)
	})
}

func TestIbft2_CheckForMisbehaviour(t *testing.T) {
	t.Run("should panic", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cs := ClientState{}
		cs.CheckForMisbehaviour(sdk.Context{}, nil, nil, nil)
	})
}

func TestIbft2_UpdateStateOnMisbehaviour(t *testing.T) {
	t.Run("should panic", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cs := ClientState{}
		cs.UpdateStateOnMisbehaviour(sdk.Context{}, nil, nil, nil)
	})
}

func TestIbft2_UpdateState(t *testing.T) {
	t.Run("should panic", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cs := ClientState{}
		cs.UpdateState(sdk.Context{}, nil, nil, nil)
	})
}

func TestIbft2_CheckSubstituteAndUpdateState(t *testing.T) {
	t.Run("should panic", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cs := ClientState{}
		cs.CheckSubstituteAndUpdateState(sdk.Context{}, nil, nil, nil, nil)
	})
}

func TestIbft2_VerifyUpgradeAndUpdateState(t *testing.T) {
	t.Run("should panic", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cs := ClientState{}
		cs.VerifyUpgradeAndUpdateState(sdk.Context{}, nil, nil, nil, nil, []byte{}, []byte{})
	})
}

func TestIbft2_ConsensusState_ClientType(t *testing.T) {
	t.Run("should panic", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cs := ConsensusState{}
		cs.ClientType()
	})
}

func TestIbft2_ConsensusState_GetTimestamp(t *testing.T) {
	t.Run("should panic", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cs := ConsensusState{}
		cs.GetTimestamp()
	})
}

func TestIbft2_ConsensusState_ValidateBasic(t *testing.T) {
	t.Run("should panic", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cs := ConsensusState{}
		cs.ValidateBasic()
	})
}

package module

import (
	"bytes"
	"context"
	"crypto/ecdsa"
	"testing"
	"time"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/relay/ethereum"
	"go.uber.org/mock/gomock"

	"github.com/ethereum/go-ethereum/crypto"
)

func TestProver_NewProver(t *testing.T) {
	t.Run("should return a prover", func(t *testing.T) {
		chain := &ethereum.Chain{}
		config := ProverConfig{}
		prover := NewProver(chain, config)
		if prover == nil {
			t.Errorf("did not return a prover")
		}
	})
}

func TestProver_Init(t *testing.T) {
	t.Run("should return nil", func(t *testing.T) {
		p := Prover{}
		err := p.Init("", time.Duration(1), nil, false)
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})
}

func TestProver_SetRelayInfo(t *testing.T) {
	t.Run("should return nil", func(t *testing.T) {
		p := Prover{}
		err := p.SetRelayInfo(nil, nil, nil)
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})
}

func TestProver_SetupForRelay(t *testing.T) {
	t.Run("should return nil", func(t *testing.T) {
		p := Prover{}
		err := p.SetupForRelay(context.TODO())
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})
}

func TestProver_CreateInitialLightClientState(t *testing.T) {
	t.Skip("cannot make a mock of EthClient")
}

func TestProver_GetLatestFinalizedHeader(t *testing.T) {
	// cannot implement a normal scnario because of codec dependencies
	t.Run("should return an error when header is invalid type", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		chain := core.NewMockFinalityAwareChain(ctrl)
		header := core.NewMockHeader(ctrl)
		p := Prover{}
		_, err := p.SetupHeadersForUpdate(chain, header)
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func TestProver_SetupHeadersForUpdate(t *testing.T) {
	t.Skip("cannot make a mock of EthClient")
}

func TestProver_ProveState(t *testing.T) {
	t.Skip("cannot make a mock of EthClient")
}

func TestProver_ProveHostConsensusState(t *testing.T) {
	t.Skip("cannot marshal because of dependency")
}

func TestProver_CheckRefreshRequired(t *testing.T) {
	t.Run("should return false and nil", func(t *testing.T) {
		p := Prover{}
		b, err := p.CheckRefreshRequired(nil)
		if b == true {
			t.Errorf("got = %v, expected = %v", b, false)
		}
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})
}

func TestProver_newHeight(t *testing.T) {
	t.Run("should return new height", func(t *testing.T) {
		p := Prover{}
		h := p.newHeight(5)
		numExpected := uint64(0)
		heiExpected := uint64(5)
		if h.RevisionHeight != heiExpected {
			t.Errorf("got = %v, expected = %v", h.RevisionHeight, heiExpected)
		}
		if h.RevisionNumber != numExpected {
			t.Errorf("got = %v, expected = %v", h.RevisionNumber, numExpected)
		}
	})
}

func TestProver_buildStateProof(t *testing.T) {
	t.Skip("cannot make a mock of EthClient")
}

func TestProver_getHeader(t *testing.T) {
	t.Skip("cannot make a mock of EthClient")
}

func Test_validateAndGetOrderedSeals(t *testing.T) {
	t.Skip("cannot make a mock of EthClient")
}

func Test_recoverSeals(t *testing.T) {
	t.Skip("no test data for this method")
}

func Test_ecrecover(t *testing.T) {
	t.Run("can get public key address", func(t *testing.T) {
		privateKey, err := crypto.HexToECDSA("fad9c8855b740a0b7ed4c221dbad0f33a83a49cad6b3fe8d5817ac83d38b6a19")
		if err != nil {
			t.Errorf("got = %v", err)
		}

		publicKey := privateKey.Public()
		publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
		if !ok {
			t.Errorf("error casting public key to ECDSA")
		}
		originalAddress := crypto.PubkeyToAddress(*publicKeyECDSA)

		data := []byte("hello")
		hash := crypto.Keccak256Hash(data)

		signature, err := crypto.Sign(hash.Bytes(), privateKey)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		address, err := ecrecover(hash.Bytes(), signature)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		if !bytes.Equal(address.Bytes(), originalAddress.Bytes()) {
			t.Errorf("got = %v, expected = %v", address.Bytes(), originalAddress.Bytes())
		}
	})

	t.Run("should return an error when SigToPub() fails", func(t *testing.T) {
		privateKey, err := crypto.HexToECDSA("fad9c8855b740a0b7ed4c221dbad0f33a83a49cad6b3fe8d5817ac83d38b6a19")
		if err != nil {
			t.Errorf("got = %v", err)
		}

		data := []byte("hello")
		hash := crypto.Keccak256Hash(data)

		_, err = crypto.Sign(hash.Bytes(), privateKey)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		_, err = ecrecover(data, []byte(""))
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func Test_parseIBFT2Extra(t *testing.T) {
	t.Skip("no test data for this method")
}

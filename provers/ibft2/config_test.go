//go:generate mockgen -source=$GOFILE -package=module -destination=./mock_$GOFILE

package module

import (
	"context"
	"testing"
	"time"

	"github.com/cosmos/cosmos-sdk/codec"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/client"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/relay/ethereum"

	sdk "github.com/cosmos/cosmos-sdk/types"
	transfertypes "github.com/cosmos/ibc-go/v7/modules/apps/transfer/types"
	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	conntypes "github.com/cosmos/ibc-go/v7/modules/core/03-connection/types"
	chantypes "github.com/cosmos/ibc-go/v7/modules/core/04-channel/types"
	ibcexported "github.com/cosmos/ibc-go/v7/modules/core/exported"
)

var _ core.Chain = (*TestChain)(nil)

type TestChain struct{}

func (c *TestChain) Config() core.ChainConfig {
	panic("not supported")
}
func (c *TestChain) Init(homePath string, timeout time.Duration, codec codec.ProtoCodecMarshaler, debug bool) error {
	panic("not supported")
}
func (c *TestChain) SetupForRelay(ctx context.Context) error {
	panic("not supported")
}
func (c *TestChain) ChainID() string {
	panic("not supported")
}
func (c *TestChain) LatestHeight() (ibcexported.Height, error) {
	panic("not supported")
}
func (c *TestChain) Timestamp(height ibcexported.Height) (time.Time, error) {
	panic("not supported")
}
func (c *TestChain) AverageBlockTime() time.Duration {
	panic("not supported")
}
func (c *TestChain) GetAddress() (sdk.AccAddress, error) {
	panic("not supported")
}
func (c *TestChain) Codec() codec.ProtoCodecMarshaler {
	panic("not supported")
}
func (c *TestChain) Client() *client.ETHClient {
	panic("not supported")
}
func (c *TestChain) SetRelayInfo(p *core.PathEnd, _ *core.ProvableChain, _ *core.PathEnd) error {
	panic("not supported")
}
func (c *TestChain) Path() *core.PathEnd {
	panic("not supported")
}
func (c *TestChain) RegisterMsgEventListener(listener core.MsgEventListener) {}

func (c *TestChain) QueryClientConsensusState(ctx core.QueryContext, dstClientConsHeight ibcexported.Height) (*clienttypes.QueryConsensusStateResponse, error) {
	panic("not supported")
}
func (c *TestChain) QueryClientState(ctx core.QueryContext) (*clienttypes.QueryClientStateResponse, error) {
	panic("not supported")
}
func (c *TestChain) QueryConnection(ctx core.QueryContext) (*conntypes.QueryConnectionResponse, error) {
	panic("not supported")
}
func (c *TestChain) QueryChannel(ctx core.QueryContext) (chanRes *chantypes.QueryChannelResponse, err error) {
	panic("not supported")
}
func (c *TestChain) QueryUnreceivedPackets(ctx core.QueryContext, seqs []uint64) ([]uint64, error) {
	panic("not supported")
}
func (c *TestChain) QueryUnfinalizedRelayPackets(ctx core.QueryContext, counterparty core.LightClientICS04Querier) (core.PacketInfoList, error) {
	panic("not supported")
}
func (c *TestChain) QueryUnreceivedAcknowledgements(ctx core.QueryContext, seqs []uint64) ([]uint64, error) {
	panic("not supported")
}
func (c *TestChain) QueryUnfinalizedRelayAcknowledgements(ctx core.QueryContext, counterparty core.LightClientICS04Querier) (core.PacketInfoList, error) {
	panic("not supported")
}
func (c *TestChain) QueryBalance(ctx core.QueryContext, address sdk.AccAddress) (sdk.Coins, error) {
	panic("not supported")
}
func (c *TestChain) QueryDenomTraces(ctx core.QueryContext, offset uint64, limit uint64) (*transfertypes.QueryDenomTracesResponse, error) {
	panic("not supported")
}
func (c *TestChain) GetMsgResult(id core.MsgID) (core.MsgResult, error) {
	panic("not supported")
}
func (c *TestChain) SendMsgs(msgs []sdk.Msg) ([]core.MsgID, error) {
	panic("not supported")
}

func TestConfig_Build(t *testing.T) {
	t.Run("return a new prover when a chain is ethereum.Chain", func(t *testing.T) {
		chain := &ethereum.Chain{}
		pc := &ProverConfig{}
		prover, err := pc.Build(chain)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		if prover == nil {
			t.Errorf("did not return a prover")
		}
	})
	t.Run("return an error when a chain is not ethereum.Chain", func(t *testing.T) {
		chain := &TestChain{}
		pc := &ProverConfig{}
		_, err := pc.Build(chain)
		if err == nil {
			t.Errorf("did not return error")
		}
	})
}

func TestConfig_Validate(t *testing.T) {
	t.Run("should return nil", func(t *testing.T) {
		pc := &ProverConfig{}
		err := pc.Validate()
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})
}

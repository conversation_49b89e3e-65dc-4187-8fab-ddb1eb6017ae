package utils

import (
	"encoding/json"
	"testing"

	"github.com/cosmos/cosmos-sdk/codec"
	"github.com/cosmos/cosmos-sdk/codec/types"
	"github.com/cosmos/cosmos-sdk/testutil/testdata"
	"github.com/stretchr/testify/require"

	proto "github.com/cosmos/gogoproto/proto"
)

type Dog interface {
	proto.Message

	GetName() string
	GetSize_() string
}
type Cat interface {
	proto.Message
}

var _ Dog = (*testdata.Dog)(nil)
var _ Cat = (*testdata.Cat)(nil)

// Reference: https://github.com/cosmos/cosmos-sdk/blob/main/codec/proto_codec_test.go
func initCodec() codec.ProtoCodecMarshaler {
	interfaceRegistry := types.NewInterfaceRegistry()
	interfaceRegistry.RegisterImplementations((*Dog)(nil), &testdata.Dog{})
	return codec.NewProtoCodec(interfaceRegistry)
}

const dogJson = `{
	"@type": "/testpb.Dog",
	"name": "test-dog",
	"size": "10"
}`

func TestMarshaler_MarshalJSONAny(t *testing.T) {
	t.Run("can stringfy a struct into json", func(t *testing.T) {
		codec := initCodec()
		dog := testdata.Dog{Name: "test-dog", Size_: "10"}
		parsed, err := MarshalJSONAny(codec, &dog)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		require.JSONEq(t, dogJson, string(parsed))
	})

	t.Run("return an error when parse fails", func(t *testing.T) {
		codec := initCodec()
		cat := testdata.Cat{Moniker: "test"}
		_, err := MarshalJSONAny(codec, &cat)
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error when NewAnyWithValue() fails", func(t *testing.T) {
		interfaceRegistry := types.NewInterfaceRegistry()
		codec := codec.NewProtoCodec(interfaceRegistry)

		_, err := MarshalJSONAny(codec, nil)
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func TestMarshaler_UnmarshalJSONAny(t *testing.T) {
	t.Run("can parse a json", func(t *testing.T) {
		codec := initCodec()

		var dog Dog
		err := UnmarshalJSONAny(codec, &dog, json.RawMessage(dogJson))
		if err != nil {
			t.Errorf("got = %v", err)
		}

		name := dog.GetName()
		expectedName := "test-dog"
		if name != expectedName {
			t.Errorf("got = %v, expected = %v", name, expectedName)
		}
		size := dog.GetSize_()
		expectedSize := "10"
		if size != expectedSize {
			t.Errorf("got = %v, expected = %v", size, expectedSize)
		}
	})

	t.Run("should return an error when parse fails", func(t *testing.T) {
		codec := initCodec()

		var cat Cat
		err := UnmarshalJSONAny(codec, &cat, json.RawMessage(dogJson))
		if err == nil {
			t.Errorf("should return an error")
		}
	})

	t.Run("should return an error when no registered implementations", func(t *testing.T) {
		interfaceRegistry := types.NewInterfaceRegistry()
		codec := codec.NewProtoCodec(interfaceRegistry)

		var cat Cat
		err := UnmarshalJSONAny(codec, &cat, json.RawMessage(dogJson))
		if err == nil {
			t.Errorf("should return an error")
		}
	})
}

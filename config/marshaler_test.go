//go:generate mockgen -source=$GOFILE -package=mock_config -destination=./mock_config/mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package config

import (
	"reflect"
	"testing"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"

	"github.com/stretchr/testify/require"
)

// chains is not testable because of Protocol Buffers
const configJson = `{
  "global": {
    "timeout": "11s",
    "light-cache-size": 11,
    "logger": {
      "level": "INFO",
      "format": "text",
      "output": "stdout"
    }
  },
  "chains": [],
  "paths": {
    "account-sync": {
      "src": {
        "chain-id": "1111",
        "client-id": "hb-ibft2-0",
        "connection-id": "connection-0",
        "channel-id": "channel-0",
        "port-id": "account-sync",
        "order": "unordered",
        "version": "account-sync-0"
      },
      "dst": {
        "chain-id": "2111",
        "client-id": "hb-ibft2-1",
        "connection-id": "connection-1",
        "channel-id": "channel-1",
        "port-id": "account-sync",
        "order": "unordered",
        "version": "account-sync-1"
      },
      "strategy": {
        "type": "naive",
        "src-noack": false,
        "dst-noack": false
      }
    },
    "balance-sync": {
      "src": {
        "chain-id": "1111",
        "client-id": "hb-ibft2-0",
        "connection-id": "connection-0",
        "channel-id": "channel-0",
        "port-id": "balance-sync",
        "order": "unordered",
        "version": "balance-sync-0"
      },
      "dst": {
        "chain-id": "1222",
        "client-id": "hb-ibft2-1",
        "connection-id": "connection-1",
        "channel-id": "channel-1",
        "port-id": "balance-sync",
        "order": "unordered",
        "version": "balance-sync-1"
      },
      "strategy": {
        "type": "naive",
        "src-noack": false,
        "dst-noack": false
      }
    },
    "token-transfer": {
      "src": {
        "chain-id": "1111",
        "client-id": "hb-ibft2-0",
        "connection-id": "connection-0",
        "channel-id": "channel-0",
        "port-id": "token-transfer",
        "order": "unordered",
        "version": "token-transfer-0"
      },
      "dst": {
        "chain-id": "1222",
        "client-id": "hb-ibft2-1",
        "connection-id": "connection-1",
        "channel-id": "channel-1",
        "port-id": "token-transfer",
        "order": "unordered",
        "version": "token-transfer-1"
      },
      "strategy": {
        "type": "naive",
        "src-noack": false,
        "dst-noack": false
      }
    }
  }
}`

func TestMarshaler_MarshalJSON(t *testing.T) {
	t.Run("should return the same json after unmarshal and marshal", func(t *testing.T) {
		codec := core.MakeCodec()
		bz := []byte(configJson)
		config := &Config{}

		err := UnmarshalJSON(codec, bz, config)
		if err != nil {
			t.Errorf("got = %v", err)
		}

		jsonBytes, err := MarshalJSON(*config)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		require.JSONEq(t, configJson, string(jsonBytes))
	})
}

func TestMarshaler_UnmarshalJSON(t *testing.T) {
	// helper
	compareValues := func(t *testing.T, result, expected any) {
		if result != expected {
			t.Errorf("got = %v, expected = %v", result, expected)
		}
	}
	t.Run("should map key-value to struct", func(t *testing.T) {
		pathTests := map[string]core.Path{
			"account-sync": {
				Src: &core.PathEnd{
					ChainID:      "1111",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Dst: &core.PathEnd{
					ChainID:      "2111",
					ClientID:     "hb-ibft2-1",
					ConnectionID: "connection-1",
					ChannelID:    "channel-1",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				Strategy: &core.StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			"balance-sync": {
				Src: &core.PathEnd{
					ChainID:      "1111",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "balance-sync",
					Order:        "unordered",
					Version:      "balance-sync-0",
				},
				Dst: &core.PathEnd{
					ChainID:      "1222",
					ClientID:     "hb-ibft2-1",
					ConnectionID: "connection-1",
					ChannelID:    "channel-1",
					PortID:       "balance-sync",
					Order:        "unordered",
					Version:      "balance-sync-1",
				},
				Strategy: &core.StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			"token-transfer": {
				Src: &core.PathEnd{
					ChainID:      "1111",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "token-transfer",
					Order:        "unordered",
					Version:      "token-transfer-0",
				},
				Dst: &core.PathEnd{
					ChainID:      "1222",
					ClientID:     "hb-ibft2-1",
					ConnectionID: "connection-1",
					ChannelID:    "channel-1",
					PortID:       "token-transfer",
					Order:        "unordered",
					Version:      "token-transfer-1",
				},
				Strategy: &core.StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
		}

		codec := core.MakeCodec()
		bz := []byte(configJson)
		config := &Config{}

		err := UnmarshalJSON(codec, bz, config)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		// global
		global := config.Global
		compareValues(t, global.Timeout, "11s")
		compareValues(t, global.LightCacheSize, 11)
		compareValues(t, global.LoggerConfig.Level, "INFO")
		compareValues(t, global.LoggerConfig.Format, "text")
		compareValues(t, global.LoggerConfig.Output, "stdout")
		// chains
		chains := config.Chains
		compareValues(t, len(chains), 0)
		// paths
		paths := config.Paths
		for k, v := range pathTests {
			path, err := paths.Get(k)
			if err != nil {
				t.Errorf("got = %v", err)
			}
			if !reflect.DeepEqual(*path, v) {
				t.Errorf("got = %v, expected = %v", *path, v)
			}
		}
	})

	t.Run("should return an error when json is invalid", func(t *testing.T) {
		codec := core.MakeCodec()
		bz := []byte("invalid")
		config := &Config{}

		err := UnmarshalJSON(codec, bz, config)
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

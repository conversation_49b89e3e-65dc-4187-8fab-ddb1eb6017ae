//go:generate mockgen -source=$GOFILE -package=mock_config -destination=./mock_config/mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package config

import (
	"testing"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
)

func TestCore_initCoreConfig(t *testing.T) {
	t.Run("can call initCoreConfig", func(t *testing.T) {
		defer func() {
			if err := recover(); err != nil {
				t.Errorf("did panic")
			}
		}()
		initCoreConfig(nil)
	})
}

func TestCore_UpdateConfigID(t *testing.T) {
	// helper
	compareValues := func(
		t *testing.T,
		c *CoreConfig,
		pathName,
		srcClientId,
		srcConnectionId,
		srcChannelId,
		dstClientId,
		dstConnectionId,
		dstChannelId string) {
		path, err := c.config.Paths.Get(pathName)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		if path.Src.ClientID != srcClientId {
			t.Errorf("got = %v, expected = %v", path.Src.ClientID, srcClientId)
		}
		if path.Src.ConnectionID != srcConnectionId {
			t.Errorf("got = %v, expected = %v", path.Src.ConnectionID, srcConnectionId)
		}
		if path.Src.ChannelID != srcChannelId {
			t.Errorf("got = %v, expected = %v", path.Src.ChannelID, srcChannelId)
		}
		if path.Dst.ClientID != dstClientId {
			t.Errorf("got = %v, expected = %v", path.Dst.ClientID, dstClientId)
		}
		if path.Dst.ConnectionID != dstConnectionId {
			t.Errorf("got = %v, expected = %v", path.Dst.ConnectionID, dstConnectionId)
		}
		if path.Dst.ChannelID != dstChannelId {
			t.Errorf("got = %v, expected = %v", path.Dst.ChannelID, dstChannelId)
		}
	}

	srcChainId := "1111"
	dstChainId := "2111"

	type updateFields struct {
		srcClientId     string
		srcConnectionId string
		srcChannelId    string
		dstClientId     string
		dstConnectionId string
		dstChannelId    string
	}

	beforeUpdate := &updateFields{
		srcClientId:     "hb-ibft2-0",
		srcConnectionId: "connection-0",
		srcChannelId:    "channel-0",
		dstClientId:     "hb-ibft2-1",
		dstConnectionId: "connection-1",
		dstChannelId:    "channel-1",
	}
	afterUpdate := &updateFields{
		srcClientId:     "src-client-id-after-update",
		srcConnectionId: "src-connection-id-after-update",
		srcChannelId:    "src-channel-id-after-update",
		dstClientId:     "dst-client-id-after-update",
		dstConnectionId: "dst-connection-id-after-update",
		dstChannelId:    "dst-channel-id-after-update",
	}
	t.Run("can update ids", func(t *testing.T) {
		coreConfig := &CoreConfig{
			config: &Config{
				ConfigPath: "/dev/null", // 書き込まないようにするための設定
				Paths: map[string]*core.Path{
					"account-sync": {
						Src: &core.PathEnd{
							ChainID:      srcChainId,
							ClientID:     beforeUpdate.srcClientId,
							ConnectionID: beforeUpdate.srcConnectionId,
							ChannelID:    beforeUpdate.srcChannelId,
							PortID:       "account-sync",
							Order:        "unordered",
							Version:      "account-sync-0",
						},
						Dst: &core.PathEnd{
							ChainID:      dstChainId,
							ClientID:     beforeUpdate.dstClientId,
							ConnectionID: beforeUpdate.dstConnectionId,
							ChannelID:    beforeUpdate.dstChannelId,
							PortID:       "account-sync",
							Order:        "unordered",
							Version:      "account-sync-1",
						},
						Strategy: &core.StrategyCfg{
							Type:     "naive",
							SrcNoack: false,
							DstNoack: false,
						},
					},
					"balance-sync": {
						Src: &core.PathEnd{
							ChainID:      srcChainId,
							ClientID:     beforeUpdate.srcClientId,
							ConnectionID: beforeUpdate.srcConnectionId,
							ChannelID:    beforeUpdate.srcChannelId,
							PortID:       "balance-sync",
							Order:        "unordered",
							Version:      "balance-sync-0",
						},
						Dst: &core.PathEnd{
							ChainID:      dstChainId,
							ClientID:     beforeUpdate.dstClientId,
							ConnectionID: beforeUpdate.dstConnectionId,
							ChannelID:    beforeUpdate.dstChannelId,
							PortID:       "balance-sync",
							Order:        "unordered",
							Version:      "balance-sync-1",
						},
						Strategy: &core.StrategyCfg{
							Type:     "naive",
							SrcNoack: false,
							DstNoack: false,
						},
					},
				},
			},
		}

		// update src ids
		pathNames := []string{"account-sync", "balance-sync"}
		for _, pathName := range pathNames {
			err := coreConfig.UpdateConfigID(pathName, srcChainId, core.ConfigIDClient, afterUpdate.srcClientId)
			if err != nil {
				t.Errorf("got = %v", err)
			}
			compareValues(t, coreConfig, pathName, afterUpdate.srcClientId, beforeUpdate.srcConnectionId, beforeUpdate.srcChannelId, beforeUpdate.dstClientId, beforeUpdate.dstConnectionId, beforeUpdate.dstChannelId)

			err = coreConfig.UpdateConfigID(pathName, srcChainId, core.ConfigIDConnection, afterUpdate.srcConnectionId)
			if err != nil {
				t.Errorf("got = %v", err)
			}
			compareValues(t, coreConfig, pathName, afterUpdate.srcClientId, afterUpdate.srcConnectionId, beforeUpdate.srcChannelId, beforeUpdate.dstClientId, beforeUpdate.dstConnectionId, beforeUpdate.dstChannelId)

			err = coreConfig.UpdateConfigID(pathName, srcChainId, core.ConfigIDChannel, afterUpdate.srcChannelId)
			if err != nil {
				t.Errorf("got = %v", err)
			}
			compareValues(t, coreConfig, pathName, afterUpdate.srcClientId, afterUpdate.srcConnectionId, afterUpdate.srcChannelId, beforeUpdate.dstClientId, beforeUpdate.dstConnectionId, beforeUpdate.dstChannelId)

			// update dst ids
			err = coreConfig.UpdateConfigID(pathName, dstChainId, core.ConfigIDClient, afterUpdate.dstClientId)
			if err != nil {
				t.Errorf("got = %v", err)
			}
			compareValues(t, coreConfig, pathName, afterUpdate.srcClientId, afterUpdate.srcConnectionId, afterUpdate.srcChannelId, afterUpdate.dstClientId, beforeUpdate.dstConnectionId, beforeUpdate.dstChannelId)

			err = coreConfig.UpdateConfigID(pathName, dstChainId, core.ConfigIDConnection, afterUpdate.dstConnectionId)
			if err != nil {
				t.Errorf("got = %v", err)
			}
			compareValues(t, coreConfig, pathName, afterUpdate.srcClientId, afterUpdate.srcConnectionId, afterUpdate.srcChannelId, afterUpdate.dstClientId, afterUpdate.dstConnectionId, beforeUpdate.dstChannelId)

			err = coreConfig.UpdateConfigID(pathName, dstChainId, core.ConfigIDChannel, afterUpdate.dstChannelId)
			if err != nil {
				t.Errorf("got = %v", err)
			}
			compareValues(t, coreConfig, pathName, afterUpdate.srcClientId, afterUpdate.srcConnectionId, afterUpdate.srcChannelId, afterUpdate.dstClientId, afterUpdate.dstConnectionId, afterUpdate.dstChannelId)
		}
	})

	t.Run("return an error when a pathname is not found", func(t *testing.T) {
		coreConfig := &CoreConfig{
			config: &Config{
				Paths: map[string]*core.Path{},
			},
		}
		err := coreConfig.UpdateConfigID("none", srcChainId, core.ConfigIDClient, afterUpdate.srcClientId)
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("return an error when a chainid is not found", func(t *testing.T) {
		coreConfig := &CoreConfig{
			config: &Config{
				Paths: map[string]*core.Path{
					"account-sync": {
						Src: &core.PathEnd{
							ChainID:      srcChainId,
							ClientID:     beforeUpdate.srcClientId,
							ConnectionID: beforeUpdate.srcConnectionId,
							ChannelID:    beforeUpdate.srcChannelId,
							PortID:       "account-sync",
							Order:        "unordered",
							Version:      "account-sync-0",
						},
						Dst: &core.PathEnd{
							ChainID:      dstChainId,
							ClientID:     beforeUpdate.dstClientId,
							ConnectionID: beforeUpdate.dstConnectionId,
							ChannelID:    beforeUpdate.dstChannelId,
							PortID:       "account-sync",
							Order:        "unordered",
							Version:      "account-sync-1",
						},
						Strategy: &core.StrategyCfg{
							Type:     "naive",
							SrcNoack: false,
							DstNoack: false,
						},
					},
				},
			},
		}
		err := coreConfig.UpdateConfigID("account-sync", "none", core.ConfigIDClient, afterUpdate.srcClientId)
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("return an error when OverWriteConfig fails", func(t *testing.T) {
		coreConfig := &CoreConfig{
			config: &Config{
				ConfigPath: "", // 失敗するようにするための設定
				Paths: map[string]*core.Path{
					"account-sync": {
						Src: &core.PathEnd{
							ChainID:      srcChainId,
							ClientID:     beforeUpdate.srcClientId,
							ConnectionID: beforeUpdate.srcConnectionId,
							ChannelID:    beforeUpdate.srcChannelId,
							PortID:       "account-sync",
							Order:        "unordered",
							Version:      "account-sync-0",
						},
						Dst: &core.PathEnd{
							ChainID:      dstChainId,
							ClientID:     beforeUpdate.dstClientId,
							ConnectionID: beforeUpdate.dstConnectionId,
							ChannelID:    beforeUpdate.dstChannelId,
							PortID:       "account-sync",
							Order:        "unordered",
							Version:      "account-sync-1",
						},
						Strategy: &core.StrategyCfg{
							Type:     "naive",
							SrcNoack: false,
							DstNoack: false,
						},
					},
				},
			},
		}
		err := coreConfig.UpdateConfigID("account-sync", srcChainId, core.ConfigIDClient, afterUpdate.srcClientId)
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

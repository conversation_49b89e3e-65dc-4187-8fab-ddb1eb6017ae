// Code generated by MockGen. DO NOT EDIT.
// Source: module.go
//
// Generated by this command:
//
//	mockgen -source=module.go -package=config -destination=./mock_module.go
//

// Package config is a generated GoMock package.
package config

import (
	reflect "reflect"

	types "github.com/cosmos/cosmos-sdk/codec/types"
	cobra "github.com/spf13/cobra"
	gomock "go.uber.org/mock/gomock"
)

// MockModuleI is a mock of ModuleI interface.
type MockModuleI struct {
	ctrl     *gomock.Controller
	recorder *MockModuleIMockRecorder
}

// MockModuleIMockRecorder is the mock recorder for MockModuleI.
type MockModuleIMockRecorder struct {
	mock *MockModuleI
}

// NewMockModuleI creates a new mock instance.
func NewMockModuleI(ctrl *gomock.Controller) *MockModuleI {
	mock := &MockModuleI{ctrl: ctrl}
	mock.recorder = &MockModuleIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockModuleI) EXPECT() *MockModuleIMockRecorder {
	return m.recorder
}

// GetCmd mocks base method.
func (m *MockModuleI) GetCmd(ctx *Context) *cobra.Command {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCmd", ctx)
	ret0, _ := ret[0].(*cobra.Command)
	return ret0
}

// GetCmd indicates an expected call of GetCmd.
func (mr *MockModuleIMockRecorder) GetCmd(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCmd", reflect.TypeOf((*MockModuleI)(nil).GetCmd), ctx)
}

// Name mocks base method.
func (m *MockModuleI) Name() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Name")
	ret0, _ := ret[0].(string)
	return ret0
}

// Name indicates an expected call of Name.
func (mr *MockModuleIMockRecorder) Name() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Name", reflect.TypeOf((*MockModuleI)(nil).Name))
}

// RegisterInterfaces mocks base method.
func (m *MockModuleI) RegisterInterfaces(registry types.InterfaceRegistry) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RegisterInterfaces", registry)
}

// RegisterInterfaces indicates an expected call of RegisterInterfaces.
func (mr *MockModuleIMockRecorder) RegisterInterfaces(registry any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterInterfaces", reflect.TypeOf((*MockModuleI)(nil).RegisterInterfaces), registry)
}

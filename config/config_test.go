//go:generate mockgen -source=$GOFILE -package=core -destination=./core/mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package config

import (
	"encoding/json"
	"fmt"
	"reflect"
	"testing"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
	"go.uber.org/mock/gomock"
)

func TestConfig_defaultConfig(t *testing.T) {
	// helper
	compareValues := func(t *testing.T, result, expected any) {
		if result != expected {
			t.Errorf("got = %v, expected = %v", result, expected)
		}
	}

	t.Run("should return default config", func(t *testing.T) {
		path := "/dev/null"
		config := defaultConfig(path)

		compareValues(t, config.Global.Timeout, "10s")
		compareValues(t, config.Global.LightCacheSize, 20)
		compareValues(t, config.Global.LoggerConfig.Level, "Info")
		compareValues(t, config.Global.LoggerConfig.Format, "json")
		compareValues(t, config.Global.LoggerConfig.Output, "stdout")
		compareValues(t, len(config.Chains), 0)
		compareValues(t, len(config.Paths), 0)
		compareValues(t, config.ConfigPath, path)
	})
}

func TestConfig_newDefaultGlobalConfig(t *testing.T) {
	// helper
	compareValues := func(t *testing.T, result, expected any) {
		if result != expected {
			t.Errorf("got = %v, expected = %v", result, expected)
		}
	}

	t.Run("should return default global config", func(t *testing.T) {
		config := newDefaultGlobalConfig()

		compareValues(t, config.Timeout, "10s")
		compareValues(t, config.LightCacheSize, 20)
		compareValues(t, config.LoggerConfig.Level, "Info")
		compareValues(t, config.LoggerConfig.Format, "json")
		compareValues(t, config.LoggerConfig.Output, "stdout")
	})
}

func TestConfig_InitCoreConfig(t *testing.T) {
	t.Skip("DO NOT CALL BECAUSE 'core.initCoreConfig()' CAN BE CALLED ONLY ONCE")
	// t.Run("no panic error", func(t *testing.T) {
	// 	defer func() {
	// 		if err := recover(); err != nil {
	// 			t.Errorf("did panic, %v", err)
	// 		}
	// 	}()

	// 	path := "/dev/null"
	// 	config := defaultConfig(path)
	// 	config.InitCoreConfig()
	// })
}

func TestConfig_GetChain(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// create mock instances
	chainMock1 := core.NewMockChain(ctrl)
	chainMock2 := core.NewMockChain(ctrl)
	proverMock := core.NewMockProver(ctrl)

	// set expectations
	chainId1 := "chain-1"
	chainId2 := "chain-2"
	chainMock1.EXPECT().ChainID().Return(chainId1).AnyTimes()
	chainMock2.EXPECT().ChainID().Return(chainId2).AnyTimes()

	provableChain1 := core.NewProvableChain(chainMock1, proverMock)
	provableChain2 := core.NewProvableChain(chainMock2, proverMock)
	provableChains := Chains{
		provableChain1,
		provableChain2,
	}

	config := &Config{
		chains: provableChains,
	}

	t.Run("should return a chain with the id", func(t *testing.T) {
		chain, err := config.GetChain(chainId1)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		if chain != provableChain1 {
			t.Errorf("got = %v, expected = %v", chain, provableChain1)
		}

		chain, err = provableChains.Get(chainId2)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		if chain != provableChain2 {
			t.Errorf("got = %v, expected = %v", chain, provableChain2)
		}
	})

	t.Run("should return an error when a chain is not found", func(t *testing.T) {
		_, err := config.GetChain("none")
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func TestConfig_GetChains(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// create mock instances
	chainMock1 := core.NewMockChain(ctrl)
	chainMock2 := core.NewMockChain(ctrl)
	proverMock := core.NewMockProver(ctrl)

	// set expectations
	chainId1 := "chain-1"
	chainId2 := "chain-2"
	chainMock1.EXPECT().ChainID().Return(chainId1).AnyTimes()
	chainMock2.EXPECT().ChainID().Return(chainId2).AnyTimes()

	provableChain1 := core.NewProvableChain(chainMock1, proverMock)
	provableChain2 := core.NewProvableChain(chainMock2, proverMock)
	provableChains := Chains{
		provableChain1,
		provableChain2,
	}

	config := &Config{
		chains: provableChains,
	}

	t.Run("should return chains with the ids", func(t *testing.T) {
		chains, err := config.GetChains(chainId1, chainId2)
		if err != nil {
			t.Errorf("got = %v", err)
		}

		expected := 2
		if len(chains) != expected {
			t.Errorf("got = %v, expected = %v", len(chains), expected)
		}
		got := chains[chainId1]
		if got != provableChain1 {
			t.Errorf("got = %v, expected = %v", got, provableChain1)
		}
		got = chains[chainId2]
		if got != provableChain2 {
			t.Errorf("got = %v, expected = %v", got, provableChain2)
		}
	})

	t.Run("should return a chain when a chain is found", func(t *testing.T) {
		chains, err := config.GetChains(chainId1)
		if err != nil {
			t.Errorf("got = %v", err)
		}

		expected := 1
		if len(chains) != expected {
			t.Errorf("got = %v, expected = %v", len(chains), expected)
		}
		got := chains[chainId1]
		if got != provableChain1 {
			t.Errorf("got = %v, expected = %v", got, provableChain1)
		}
	})

	t.Run("should return an error when a chain is not found", func(t *testing.T) {
		_, err := config.GetChains(chainId1, "none")
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func TestConfig_AddChain(t *testing.T) {
	t.Run("should return an error when failing to build a config", func(t *testing.T) {
		chainProverConfig := core.ChainProverConfig{}
		codec := core.MakeCodec()
		cfg := &Config{}

		err := cfg.AddChain(codec, chainProverConfig)
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func TestConfig_AddChain_Skipped(t *testing.T) {
	t.Skip("can add a chain to the config. 'core.NewChainProverConfig' and 'core.Init' are not mockable.")
	t.Skip("should return an error when chainId is duplicated. 'core.NewChainProverConfig' and 'core.Init' are not mockable.")
}

func TestConfig_AddPath(t *testing.T) {
	t.Run("should add another chain", func(t *testing.T) {
		config := &Config{
			Paths: map[string]*core.Path{
				"account-sync": {
					Src: &core.PathEnd{
						ChainID:      "1111",
						ClientID:     "hb-ibft2-0",
						ConnectionID: "connection-0",
						ChannelID:    "channel-0",
						PortID:       "account-sync",
						Order:        "unordered",
						Version:      "account-sync-0",
					},
					Dst: &core.PathEnd{
						ChainID:      "2222",
						ClientID:     "hb-ibft2-1",
						ConnectionID: "connection-1",
						ChannelID:    "channel-1",
						PortID:       "account-sync",
						Order:        "unordered",
						Version:      "account-sync-1",
					},
					Strategy: &core.StrategyCfg{
						Type:     "naive",
						SrcNoack: false,
						DstNoack: false,
					},
				},
			},
		}

		added := &core.Path{
			Src: &core.PathEnd{
				ChainID:      "1111",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-0",
			},
			Dst: &core.PathEnd{
				ChainID:      "2222",
				ClientID:     "hb-ibft2-1",
				ConnectionID: "connection-1",
				ChannelID:    "channel-1",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			Strategy: &core.StrategyCfg{
				Type:     "naive",
				SrcNoack: false,
				DstNoack: false,
			},
		}

		err := config.AddPath("test-path", added)
		if err != nil {
			t.Errorf("got = %v", err)
		}

		expected := 2
		if len(config.Paths) != expected {
			t.Errorf("got = %v, expected = %v", len(config.Paths), expected)
		}
	})
}

func TestConfig_DeleteChain(t *testing.T) {
	t.Run("can remove a chain", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// Chains
		chainProverConfigs := []core.ChainProverConfig{
			{},
		}

		// chains
		chainMock := core.NewMockChain(ctrl)
		proverMock := core.NewMockProver(ctrl)

		chainId1 := "chain-1"
		chainMock.EXPECT().ChainID().Return(chainId1).AnyTimes()

		provableChain := core.NewProvableChain(chainMock, proverMock)
		provableChains := Chains{
			provableChain,
		}

		config := &Config{
			Chains: chainProverConfigs,
			chains: provableChains,
		}
		newConfig := config.DeleteChain(chainId1)
		if len(newConfig.Chains) != 0 {
			t.Errorf("got = %v, expected = %v", len(newConfig.Chains), 0)
		}
		if len(newConfig.chains) != 0 {
			t.Errorf("got = %v, expected = %v", len(newConfig.chains), 0)
		}
	})

	t.Run("should do nothing when a chain not found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// Chains
		chainProverConfigs := []core.ChainProverConfig{
			{},
		}

		// chains
		chainMock := core.NewMockChain(ctrl)
		proverMock := core.NewMockProver(ctrl)

		chainId1 := "chain-1"
		chainMock.EXPECT().ChainID().Return(chainId1).AnyTimes()

		provableChain := core.NewProvableChain(chainMock, proverMock)
		provableChains := Chains{
			provableChain,
		}

		config := &Config{
			Chains: chainProverConfigs,
			chains: provableChains,
		}
		newConfig := config.DeleteChain("none")
		if len(newConfig.Chains) != 1 {
			t.Errorf("got = %v, expected = %v", len(newConfig.Chains), 1)
		}
		if len(newConfig.chains) != 1 {
			t.Errorf("got = %v, expected = %v", len(newConfig.chains), 1)
		}
	})
}

func TestConfig_ChainsFromPath(t *testing.T) {
	t.Run("can get chains and chainIds", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// create mock instances
		chainMock1 := core.NewMockChain(ctrl)
		chainMock2 := core.NewMockChain(ctrl)
		proverMock := core.NewMockProver(ctrl)

		chainId1 := "chain-1"
		chainId2 := "chain-2"
		chainMock1.EXPECT().ChainID().Return(chainId1).AnyTimes()
		chainMock2.EXPECT().ChainID().Return(chainId2).AnyTimes()

		provableChain1 := core.NewProvableChain(chainMock1, proverMock)
		provableChain2 := core.NewProvableChain(chainMock2, proverMock)
		provableChains := Chains{
			provableChain1,
			provableChain2,
		}

		config := &Config{
			Paths: map[string]*core.Path{
				"account-sync": {
					Src: &core.PathEnd{
						ChainID:      chainId1,
						ClientID:     "hb-ibft2-0",
						ConnectionID: "connection-0",
						ChannelID:    "channel-0",
						PortID:       "account-sync",
						Order:        "unordered",
						Version:      "account-sync-0",
					},
					Dst: &core.PathEnd{
						ChainID:      chainId2,
						ClientID:     "hb-ibft2-1",
						ConnectionID: "connection-1",
						ChannelID:    "channel-1",
						PortID:       "account-sync",
						Order:        "unordered",
						Version:      "account-sync-1",
					},
					Strategy: &core.StrategyCfg{
						Type:     "naive",
						SrcNoack: false,
						DstNoack: false,
					},
				},
			},
			chains: provableChains,
		}
		_, _, _, err := config.ChainsFromPath("none")
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
	t.Run("should return an error when a path is not found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// create mock instances
		chainMock1 := core.NewMockChain(ctrl)
		chainMock2 := core.NewMockChain(ctrl)
		proverMock := core.NewMockProver(ctrl)

		chainId1 := "chain-1"
		chainId2 := "chain-2"
		chainMock1.EXPECT().ChainID().Return(chainId1).AnyTimes()
		chainMock2.EXPECT().ChainID().Return(chainId2).AnyTimes()
		chainMock1.EXPECT().SetRelayInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		chainMock2.EXPECT().SetRelayInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		proverMock.EXPECT().SetRelayInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

		provableChain1 := core.NewProvableChain(chainMock1, proverMock)
		provableChain2 := core.NewProvableChain(chainMock2, proverMock)
		provableChains := Chains{
			provableChain1,
			provableChain2,
		}

		config := &Config{
			Paths: map[string]*core.Path{
				"account-sync": {
					Src: &core.PathEnd{
						ChainID:      chainId1,
						ClientID:     "hb-ibft2-0",
						ConnectionID: "connection-0",
						ChannelID:    "channel-0",
						PortID:       "account-sync",
						Order:        "unordered",
						Version:      "account-sync-0",
					},
					Dst: &core.PathEnd{
						ChainID:      chainId2,
						ClientID:     "hb-ibft2-1",
						ConnectionID: "connection-1",
						ChannelID:    "channel-1",
						PortID:       "account-sync",
						Order:        "unordered",
						Version:      "account-sync-1",
					},
					Strategy: &core.StrategyCfg{
						Type:     "naive",
						SrcNoack: false,
						DstNoack: false,
					},
				},
			},
			chains: provableChains,
		}
		path := "account-sync"
		chains, srcChainID, dstChainId, err := config.ChainsFromPath(path)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		if srcChainID != chainId1 {
			t.Errorf("got = %v, expected = %v", srcChainID, chainId1)
		}
		if dstChainId != chainId2 {
			t.Errorf("got = %v, expected = %v", dstChainId, chainId2)
		}
		if chains[srcChainID].ChainID() != chainId1 {
			t.Errorf("got = %v, expected = %v", chains[srcChainID].ChainID(), chainId1)
		}
		if chains[dstChainId].ChainID() != chainId2 {
			t.Errorf("got = %v, expected = %v", chains[dstChainId].ChainID(), chainId2)
		}
	})

	t.Run("should return an error when a chain is not found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// create mock instances
		chainMock1 := core.NewMockChain(ctrl)
		chainMock2 := core.NewMockChain(ctrl)
		proverMock := core.NewMockProver(ctrl)

		chainId1 := "chain-1"
		chainId2 := "chain-2"
		chainMock1.EXPECT().ChainID().Return(chainId1).AnyTimes()
		chainMock2.EXPECT().ChainID().Return(chainId2).AnyTimes()

		provableChain1 := core.NewProvableChain(chainMock1, proverMock)
		provableChain2 := core.NewProvableChain(chainMock2, proverMock)
		provableChains := Chains{
			provableChain1,
			provableChain2,
		}

		config := &Config{
			Paths: map[string]*core.Path{
				"account-sync": {
					Src: &core.PathEnd{
						ChainID:      chainId1,
						ClientID:     "hb-ibft2-0",
						ConnectionID: "connection-0",
						ChannelID:    "channel-0",
						PortID:       "account-sync",
						Order:        "unordered",
						Version:      "account-sync-0",
					},
					Dst: &core.PathEnd{
						ChainID:      "none",
						ClientID:     "hb-ibft2-1",
						ConnectionID: "connection-1",
						ChannelID:    "channel-1",
						PortID:       "account-sync",
						Order:        "unordered",
						Version:      "account-sync-1",
					},
					Strategy: &core.StrategyCfg{
						Type:     "naive",
						SrcNoack: false,
						DstNoack: false,
					},
				},
			},
			chains: provableChains,
		}
		_, _, _, err := config.ChainsFromPath("account-sync")
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error when src SetRelayInfo fails", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// create mock instances
		chainMock1 := core.NewMockChain(ctrl)
		chainMock2 := core.NewMockChain(ctrl)
		proverMock := core.NewMockProver(ctrl)

		chainId1 := "chain-1"
		chainId2 := "chain-2"
		chainMock1.EXPECT().ChainID().Return(chainId1).AnyTimes()
		// エラーを返す設定
		chainMock1.EXPECT().SetRelayInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("test error")).AnyTimes()
		chainMock2.EXPECT().ChainID().Return(chainId2).AnyTimes()
		chainMock2.EXPECT().SetRelayInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		proverMock.EXPECT().SetRelayInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

		provableChain1 := core.NewProvableChain(chainMock1, proverMock)
		provableChain2 := core.NewProvableChain(chainMock2, proverMock)
		provableChains := Chains{
			provableChain1,
			provableChain2,
		}

		config := &Config{
			Paths: map[string]*core.Path{
				"account-sync": {
					Src: &core.PathEnd{
						ChainID:      chainId1,
						ClientID:     "hb-ibft2-0",
						ConnectionID: "connection-0",
						ChannelID:    "channel-0",
						PortID:       "account-sync",
						Order:        "unordered",
						Version:      "account-sync-0",
					},
					Dst: &core.PathEnd{
						ChainID:      chainId2,
						ClientID:     "hb-ibft2-1",
						ConnectionID: "connection-1",
						ChannelID:    "channel-1",
						PortID:       "account-sync",
						Order:        "unordered",
						Version:      "account-sync-1",
					},
					Strategy: &core.StrategyCfg{
						Type:     "naive",
						SrcNoack: false,
						DstNoack: false,
					},
				},
			},
			chains: provableChains,
		}
		_, _, _, err := config.ChainsFromPath("account-sync")
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error when dst SetRelayInfo fails", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// create mock instances
		chainMock1 := core.NewMockChain(ctrl)
		chainMock2 := core.NewMockChain(ctrl)
		proverMock := core.NewMockProver(ctrl)

		chainId1 := "chain-1"
		chainId2 := "chain-2"
		chainMock1.EXPECT().ChainID().Return(chainId1).AnyTimes()
		chainMock1.EXPECT().SetRelayInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		// エラーを返す設定
		chainMock2.EXPECT().ChainID().Return(chainId2).AnyTimes()
		chainMock2.EXPECT().SetRelayInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("test error")).AnyTimes()
		proverMock.EXPECT().SetRelayInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

		provableChain1 := core.NewProvableChain(chainMock1, proverMock)
		provableChain2 := core.NewProvableChain(chainMock2, proverMock)
		provableChains := Chains{
			provableChain1,
			provableChain2,
		}

		config := &Config{
			Paths: map[string]*core.Path{
				"account-sync": {
					Src: &core.PathEnd{
						ChainID:      chainId1,
						ClientID:     "hb-ibft2-0",
						ConnectionID: "connection-0",
						ChannelID:    "channel-0",
						PortID:       "account-sync",
						Order:        "unordered",
						Version:      "account-sync-0",
					},
					Dst: &core.PathEnd{
						ChainID:      chainId2,
						ClientID:     "hb-ibft2-1",
						ConnectionID: "connection-1",
						ChannelID:    "channel-1",
						PortID:       "account-sync",
						Order:        "unordered",
						Version:      "account-sync-1",
					},
					Strategy: &core.StrategyCfg{
						Type:     "naive",
						SrcNoack: false,
						DstNoack: false,
					},
				},
			},
			chains: provableChains,
		}
		_, _, _, err := config.ChainsFromPath("account-sync")
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func TestInitChains(t *testing.T) {
	t.Run("can initilize chains", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// create mock instances
		chainMock1 := core.NewMockChain(ctrl)
		chainMock2 := core.NewMockChain(ctrl)
		proverMock := core.NewMockProver(ctrl)
		chainMock1.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		chainMock2.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		proverMock.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

		provableChain1 := core.NewProvableChain(chainMock1, proverMock)
		provableChain2 := core.NewProvableChain(chainMock2, proverMock)
		provableChains := Chains{
			provableChain1,
			provableChain2,
		}
		config := &Config{
			Global: newDefaultGlobalConfig(),
			chains: provableChains,
		}
		ctx := &Context{
			Config: config,
		}
		homePath := "/dev/null"

		err := InitChains(ctx, homePath, false)
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})

	t.Run("return an error when duration parsing fails", func(t *testing.T) {
		config := &Config{
			Global: newDefaultGlobalConfig(),
		}
		config.Global.Timeout = "fail"
		ctx := &Context{
			Config: config,
		}
		homePath := "/dev/null"

		err := InitChains(ctx, homePath, false)
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("return an error when chain.Init fails", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// create mock instances
		chainMock1 := core.NewMockChain(ctrl)
		proverMock := core.NewMockProver(ctrl)
		chainMock1.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("test error")).AnyTimes()
		proverMock.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

		provableChain1 := core.NewProvableChain(chainMock1, proverMock)
		provableChains := Chains{
			provableChain1,
		}
		config := &Config{
			Global: newDefaultGlobalConfig(),
			chains: provableChains,
		}
		ctx := &Context{
			Config: config,
		}
		homePath := "/dev/null"

		err := InitChains(ctx, homePath, false)
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func TestConfig_InitConfig(t *testing.T) {
	t.Skip("DO NOT CALL BECAUSE 'core.initCoreConfig()' CAN BE CALLED ONLY ONCE")
	// t.Run("should set default config when the path does not exist", func(t *testing.T) {
	// 	cfg := &Config{}
	// 	ctx := &Context{
	// 		Config: cfg,
	// 	}
	// 	tmpPath := t.TempDir()
	// 	err := cfg.InitConfig(ctx, tmpPath, "none", false)
	// 	if err != nil {
	// 		t.Errorf("got = %v", err)
	// 	}
	// 	expected := fmt.Sprintf("%s/%s", tmpPath, "none")
	// 	if cfg.ConfigPath != expected {
	// 		t.Errorf("got = %v, expected := %v", cfg.ConfigPath, expected)
	// 	}

	// 	expCfg := defaultConfig(expected)
	// 	if !reflect.DeepEqual(cfg, expCfg) {
	// 		t.Errorf("got = %v, expected := %v", cfg, expCfg)
	// 	}
	// })
}

func TestConfig_CreateConfig(t *testing.T) {
	t.Run("should do nothing when the path already exists", func(t *testing.T) {
		config := &Config{
			ConfigPath: "/dev/null",
		}
		err := config.CreateConfig()
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})

	t.Run("should create a config file when the path does not exist", func(t *testing.T) {
		tmpPath := t.TempDir()
		config := &Config{
			ConfigPath: tmpPath + "/config/tmp",
		}
		err := config.CreateConfig()
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})

	t.Run("return an error when creating a file fails", func(t *testing.T) {
		config := &Config{
			ConfigPath: "",
		}
		err := config.CreateConfig()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func TestConfig_OverWriteConfig(t *testing.T) {
	t.Run("can write a config file", func(t *testing.T) {
		config := &Config{
			ConfigPath: "/dev/null",
		}
		err := config.OverWriteConfig()
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})

	t.Run("return an error when not writing a file", func(t *testing.T) {
		config := &Config{
			ConfigPath: "",
		}
		err := config.OverWriteConfig()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func Test_defaultConfigBytes(t *testing.T) {
	t.Run("should return the bytes of default config", func(t *testing.T) {
		path := ""
		configBytes := defaultConfigBytes(path)
		expected := defaultConfig(path)

		configParsed := &Config{}
		err := json.Unmarshal(configBytes, configParsed)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		if !reflect.DeepEqual(&expected, configParsed) {
			t.Errorf("got = %v, expected = %v", configParsed, &expected)
		}
	})
}

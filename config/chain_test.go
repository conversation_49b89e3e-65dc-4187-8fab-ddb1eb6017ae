//go:generate mockgen -source=$GOFILE -package=core -destination=./core/mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package config

import (
	"testing"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
	"go.uber.org/mock/gomock"
)

func TestChains_Get(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// create mock instances
	chainMock1 := core.NewMockChain(ctrl)
	chainMock2 := core.NewMockChain(ctrl)
	proverMock := core.NewMockProver(ctrl)

	// set expectations
	chainId1 := "chain-1"
	chainId2 := "chain-2"
	chainMock1.EXPECT().ChainID().Return(chainId1).AnyTimes()
	chainMock2.EXPECT().ChainID().Return(chainId2).AnyTimes()

	provableChain1 := core.NewProvableChain(chainMock1, proverMock)
	provableChain2 := core.NewProvableChain(chainMock2, proverMock)
	provableChains := Chains{
		provableChain1,
		provableChain2,
	}

	t.Run("should return a chain with the id", func(t *testing.T) {
		chain, err := provableChains.Get(chainId1)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		if chain != provableChain1 {
			t.Errorf("got = %v, expected = %v", chain, provableChain1)
		}

		chain, err = provableChains.Get(chainId2)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		if chain != provableChain2 {
			t.Errorf("got = %v, expected = %v", chain, provableChain2)
		}
	})

	t.Run("should return an error when a chain is not found", func(t *testing.T) {
		_, err := provableChains.Get("none")
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func TestChains_Gets(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// create mock instances
	chainMock1 := core.NewMockChain(ctrl)
	chainMock2 := core.NewMockChain(ctrl)
	proverMock := core.NewMockProver(ctrl)

	// set expectations
	chainId1 := "chain-1"
	chainId2 := "chain-2"
	chainMock1.EXPECT().ChainID().Return(chainId1).AnyTimes()
	chainMock2.EXPECT().ChainID().Return(chainId2).AnyTimes()

	provableChain1 := core.NewProvableChain(chainMock1, proverMock)
	provableChain2 := core.NewProvableChain(chainMock2, proverMock)
	provableChains := Chains{
		provableChain1,
		provableChain2,
	}

	t.Run("should return chains with the ids", func(t *testing.T) {
		chains, err := provableChains.Gets(chainId1, chainId2)
		if err != nil {
			t.Errorf("got = %v", err)
		}

		expected := 2
		if len(chains) != expected {
			t.Errorf("got = %v, expected = %v", len(chains), expected)
		}
		got := chains[chainId1]
		if got != provableChain1 {
			t.Errorf("got = %v, expected = %v", got, provableChain1)
		}
		got = chains[chainId2]
		if got != provableChain2 {
			t.Errorf("got = %v, expected = %v", got, provableChain2)
		}
	})

	t.Run("should return a chain when a chain is found", func(t *testing.T) {
		chains, err := provableChains.Gets(chainId1)
		if err != nil {
			t.Errorf("got = %v", err)
		}

		expected := 1
		if len(chains) != expected {
			t.Errorf("got = %v, expected = %v", len(chains), expected)
		}
		got := chains[chainId1]
		if got != provableChain1 {
			t.Errorf("got = %v, expected = %v", got, provableChain1)
		}
	})

	t.Run("should return an error when a chain is not found", func(t *testing.T) {
		_, err := provableChains.Gets(chainId1, "none")
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

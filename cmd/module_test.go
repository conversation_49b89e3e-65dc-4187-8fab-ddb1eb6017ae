//go:generate mockgen -source=$GOFILE -package=mock -destination=./mock/mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package cmd

import (
	"slices"
	"testing"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/config"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
	"go.uber.org/mock/gomock"
)

func Test_modulesCmd(t *testing.T) {
	t.Run("should return a cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := modulesCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "modules" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "modules")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 1 {
			t.<PERSON><PERSON><PERSON>("got = %v, expected = %v", len(subcmds), 1)
		}
		paths := []string{}
		for _, c := range subcmds {
			paths = append(paths, c.CommandPath())
		}
		if !slices.Contains(paths, "modules show") {
			t.Errorf("subcmds did not contains 'modules show'")
		}
	})

	t.Run("should return an error when executed with no subcmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := modulesCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func Test_showModulesCmd(t *testing.T) {
	t.Run("should return cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := showModulesCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "show" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "show")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 0 {
			t.Errorf("got = %v, expected = %v", len(subcmds), 0)
		}
	})

	t.Run("should be executable", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		module := config.NewMockModuleI(ctrl)
		module.EXPECT().Name().Return("test").Times(1)

		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec, Modules: []config.ModuleI{module}}
		cmd := showModulesCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		err := cmd.Execute()
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})
}

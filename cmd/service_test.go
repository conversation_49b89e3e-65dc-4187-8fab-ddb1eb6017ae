//go:generate gotests -w -all $GOFILE
package cmd

import (
	"slices"
	"testing"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/config"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
)

func Test_serviceCmd(t *testing.T) {
	t.Run("should return a cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := serviceCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "service" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "service")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 1 {
			t.Errorf("got = %v, expected = %v", len(subcmds), 1)
		}
		paths := []string{}
		for _, c := range subcmds {
			paths = append(paths, c.CommandPath())
		}
		if !slices.Contains(paths, "service start") {
			t.Errorf("subcmds did not contains 'service start'")
		}
	})

	t.Run("should return an error when executed with no subcmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := serviceCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func Test_startCmd(t *testing.T) {
	t.Run("should return cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := startCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "start" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "start")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 0 {
			t.Errorf("got = %v, expected = %v", len(subcmds), 0)
		}
	})

	t.Run("should return an error with zero arg", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := startCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error with two args", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := startCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		cmd.SetArgs([]string{"path", "extra"})
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

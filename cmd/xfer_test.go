//go:generate gotests -w -all $GOFILE
package cmd

import (
	"testing"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/config"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
)

func TestXfer_xfersend(t *testing.T) {
	t.Run("should return a cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := xfersend(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "transfer" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "transfer")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 0 {
			t.Errorf("got = %v, expected = %v", len(subcmds), 1)
		}
	})

	// transfer [path-name] [src-chain-id] [dst-chain-id] [amount] [dst-addr]
	t.Run("should return an error with four args", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := xfersend(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		cmd.SetArgs([]string{"account-sync", "chain-1", "chain-2", "100"})
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	// SKIP: cannot set chains
	// t.Run("should be executable", func(t *testing.T) {
	// 	&config.Config{
	// 			Paths: map[string]*core.Path{
	// 			chains: ..., // cannot set
	// 		},
	// 	}
	// })

	t.Run("should return an error with six args", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := xfersend(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		cmd.SetArgs([]string{"account-sync", "chain-1", "chain-2", "100", "0x1234", "extra"})
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

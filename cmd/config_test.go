//go:generate gotests -w -all $GOFILE
package cmd

import (
	"slices"
	"testing"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/config"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
)

func Test_configCmd(t *testing.T) {
	t.Run("should return a cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := configCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "config" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "config")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 2 {
			t.Errorf("got = %v, expected = %v", len(subcmds), 2)
		}
		paths := []string{}
		for _, c := range subcmds {
			paths = append(paths, c.CommandPath())
		}
		if !slices.Contains(paths, "config init") {
			t.<PERSON>("subcmds did not contains 'config init'")
		}
		if !slices.Contains(paths, "config show") {
			t.Errorf("subcmds did not contains 'config show'")
		}
	})

	t.Run("should return an error when executed with no subcmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := configCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func Test_configInitCmd(t *testing.T) {
	t.Run("should return cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := configInitCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "init" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "init")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 0 {
			t.Errorf("got = %v, expected = %v", len(subcmds), 0)
		}
	})

	t.Run("should be executable", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{ConfigPath: "/dev/null"}, Codec: codec}
		cmd := configShowCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		err := cmd.Execute()
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})

	t.Run("should return an error when path does not exist", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{ConfigPath: ""}, Codec: codec}
		cmd := configShowCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func Test_configShowCmd(t *testing.T) {
	t.Run("should return cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := configShowCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "show" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "show")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 0 {
			t.Errorf("got = %v, expected = %v", len(subcmds), 0)
		}
	})

	t.Run("should be executable", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{ConfigPath: "/dev/null"}, Codec: codec}
		cmd := configShowCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		err := cmd.Execute()
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})

	t.Run("should return an error when path does not exist", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{ConfigPath: "none"}, Codec: codec}
		cmd := configShowCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

//go:generate gotests -w -all $GOFILE
package cmd

import (
	"slices"
	"testing"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/config"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
)

func Test_pathsCmd(t *testing.T) {

	t.Run("should return a cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := pathsCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "paths" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "paths")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 3 {
			t.<PERSON>rrorf("got = %v, expected = %v", len(subcmds), 3)
		}
		paths := []string{}
		for _, c := range subcmds {
			paths = append(paths, c.Command<PERSON>())
		}
		if !slices.Contains(paths, "paths list") {
			t.Errorf("subcmds did not contains 'paths list'")
		}
		if !slices.Contains(paths, "paths add") {
			t.Errorf("subcmds did not contains 'paths add'")
		}
		if !slices.Contains(paths, "paths edit") {
			t.Errorf("subcmds did not contains 'paths edit'")
		}
	})

	t.Run("should return an error when executed with no subcmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := pathsCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func Test_pathsListCmd(t *testing.T) {
	t.Run("should return cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := pathsListCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "list" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "list")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 0 {
			t.Errorf("got = %v, expected = %v", len(subcmds), 0)
		}
	})

	t.Run("should return an error without flags", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := pathsAddCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error with two flags", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := pathsAddCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		cmd.SetArgs([]string{"--json", "--yaml"})
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func Test_pathsAddCmd(t *testing.T) {

	t.Run("should return cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := pathsAddCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "add" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "add")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 0 {
			t.Errorf("got = %v, expected = %v", len(subcmds), 0)
		}
	})

	t.Run("should return an error with two args", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := pathsAddCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		cmd.SetArgs([]string{"chain-1", "chain-2"})
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error with four args", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := pathsAddCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		cmd.SetArgs([]string{"chain-1", "chain-2", "path", "extra"})
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func Test_pathsEditCmd(t *testing.T) {
	t.Run("should return cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := pathsEditCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "edit" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "edit")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 0 {
			t.Errorf("got = %v, expected = %v", len(subcmds), 0)
		}
	})

	t.Run("should return an error with three args", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := pathsEditCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		cmd.SetArgs([]string{"path", "src", "key"})
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error with five args", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := pathsEditCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		cmd.SetArgs([]string{"path", "src", "key", "value", "extra"})
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	// SKIP: cannot check the result
	// t.Run("should be executable", func(t *testing.T) {})
}

func Test_fileInputPathAdd(t *testing.T) {
	t.Run("should be fails as no file exists", func(t *testing.T) {
		config := &config.Config{}
		err := fileInputPathAdd(config, "/dev/null", "test")
		if err == nil {
			t.Errorf("got = %v", err)
		}
	})
}

func Test_userInputPathAdd(t *testing.T) {
	t.Skip("cannot test because of readStdin()")
}

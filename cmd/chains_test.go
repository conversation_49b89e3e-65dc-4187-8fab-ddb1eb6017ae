//go:generate gotests -w -all $GOFILE
package cmd

import (
	"slices"
	"testing"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/config"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
)

func TestChains_chainsCmd(t *testing.T) {
	t.Run("should return the chains command with subs of add-dir", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := chainsCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true
		if cmd.CommandPath() != "chains" {
			t.<PERSON>rf("got = %v, expected = %v", cmd.CommandPath(), "chains")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 1 {
			t.<PERSON>rrorf("got = %v, expected = %v", len(subcmds), 1)
		}
		paths := []string{}
		for _, c := range subcmds {
			paths = append(paths, c.CommandPath())
		}
		if !slices.Contains(paths, "chains add-dir") {
			t.<PERSON><PERSON>("subcmds did not contains 'chains add-dir'")
		}
	})

	t.Run("should return an error when executed with no subcmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := chainsCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func TestChains_chainsAddDirCmd(t *testing.T) {
	t.Run("should return a cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := chainsAddDirCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true
		if cmd.CommandPath() != "add-dir" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "add-dir")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 0 {
			t.Errorf("got = %v, expected = %v", len(subcmds), 1)
		}
	})

	t.Run("should return an error when executed with no args", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := chainsAddDirCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should be executable when executed with one arg", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{ConfigPath: "/dev/null"}, Codec: codec}
		cmd := chainsAddDirCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		tmpPath := t.TempDir()

		cmd.SetArgs([]string{tmpPath})
		err := cmd.Execute()
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})

	t.Run("should return an error when executed with two args", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := chainsAddDirCmd(ctx)
		cmd.SetArgs([]string{"test", "test"})
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error when dir does not exist", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := chainsAddDirCmd(ctx)
		cmd.SetArgs([]string{"none"})
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func TestChains_filesAdd(t *testing.T) {
	t.Run("should return nil when no files found", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}

		tmpPath := t.TempDir()
		err := filesAdd(ctx, tmpPath)
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})

	t.Run("should return an error when no dir found", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}

		err := filesAdd(ctx, "none")
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

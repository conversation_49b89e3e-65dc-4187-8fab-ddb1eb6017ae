//go:generate gotests -w -all $GOFILE
package cmd

import (
	"fmt"
	"os"
	"testing"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/config"
	"github.com/spf13/cobra"
	"go.uber.org/mock/gomock"
)

func TestRoot_Execute(t *testing.T) {
	t.Run("can execute", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		module := config.NewMockModuleI(ctrl)
		// must be called once
		module.EXPECT().GetCmd(gomock.Any()).Return(nil).Times(1)
		module.EXPECT().RegisterInterfaces(gomock.Any()).Times(1)

		Execute(module)
	})
}

func TestRoot_readStdin(t *testing.T) {
	t.Run("should return an error", func(t *testing.T) {
		message := " test message "
		fmt.Fprintln(os.Stdin, message)
		_, err := readStdin()
		if err == nil {
			t.<PERSON><PERSON>rf("got = %v", err)
		}
	})
}

func TestRoot_initLogger(t *testing.T) {
	t.Run("can initiate logger with stdout", func(t *testing.T) {
		ctx := &config.Context{
			Config: &config.Config{
				Global: config.GlobalConfig{
					Timeout:        "10s",
					LightCacheSize: 20,
					LoggerConfig: config.LoggerConfig{
						Level:  "Info",
						Format: "json",
						Output: "stdout",
					},
				},
			},
		}
		err := initLogger(ctx)
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})

	t.Run("can initiate logger with stderr", func(t *testing.T) {
		ctx := &config.Context{
			Config: &config.Config{
				Global: config.GlobalConfig{
					Timeout:        "10s",
					LightCacheSize: 20,
					LoggerConfig: config.LoggerConfig{
						Level:  "Info",
						Format: "json",
						Output: "stderr",
					},
				},
			},
		}
		err := initLogger(ctx)
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})

	t.Run("shoud return an error when output is invalid", func(t *testing.T) {
		ctx := &config.Context{
			Config: &config.Config{
				Global: config.GlobalConfig{
					Timeout:        "10s",
					LightCacheSize: 20,
					LoggerConfig: config.LoggerConfig{
						Level:  "Info",
						Format: "json",
						Output: "none",
					},
				},
			},
		}
		err := initLogger(ctx)
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func TestRoot_noCommand(t *testing.T) {
	t.Run("should return an error", func(t *testing.T) {
		cmd := &cobra.Command{}
		err := noCommand(cmd, []string{})
		if err == nil {
			t.Errorf("got = %v", err)
		}
	})
}

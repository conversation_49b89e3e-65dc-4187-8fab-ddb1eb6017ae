//go:generate gotests -w -all $GOFILE
package cmd

import (
	"slices"
	"testing"

	"github.com/decurret-lab/dcbg-dcjpy-relayer/config"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/core"
)

func Test_queryCmd(t *testing.T) {

	t.Run("should return a cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "query" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "query")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 7 {
			t.<PERSON>rrorf("got = %v, expected = %v", len(subcmds), 7)
		}
		paths := []string{}
		for _, c := range subcmds {
			paths = append(paths, c.CommandPath())
		}
		if !slices.Contains(paths, "query client") {
			t.<PERSON>rf("subcmds did not contains 'query client'")
		}
		if !slices.Contains(paths, "query connection") {
			t.Errorf("subcmds did not contains 'query connection'")
		}
		if !slices.Contains(paths, "query channel") {
			t.Errorf("subcmds did not contains 'query channel'")
		}
		if !slices.Contains(paths, "query balance") {
			t.Errorf("subcmds did not contains 'query balance'")
		}
		if !slices.Contains(paths, "query unrelayed-packets") {
			t.Errorf("subcmds did not contains 'query unrelayed-packets'")
		}
		if !slices.Contains(paths, "query unrelayed-acknowledgements") {
			t.Errorf("subcmds did not contains 'query unrelayed-acknowledgements'")
		}
	})

	t.Run("should return an error when executed with no subcmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := modulesCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func Test_queryClientCmd(t *testing.T) {
	t.Run("should return cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryClientCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "client" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "client")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 0 {
			t.Errorf("got = %v, expected = %v", len(subcmds), 0)
		}
	})

	t.Run("should return an error with one arg", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryClientCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		cmd.SetArgs([]string{"path"})
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error with three args", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryClientCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		cmd.SetArgs([]string{"path", "chain-1", "extra"})
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func Test_queryConnection(t *testing.T) {
	t.Run("should return cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryConnection(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "connection" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "connection")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 0 {
			t.Errorf("got = %v, expected = %v", len(subcmds), 0)
		}
	})

	t.Run("should return an error with one arg", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryConnection(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		cmd.SetArgs([]string{"path"})
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error with three args", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryConnection(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		cmd.SetArgs([]string{"path", "chain-1", "extra"})
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func Test_queryChannel(t *testing.T) {
	t.Run("should return cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryChannel(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "channel" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "channel")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 0 {
			t.Errorf("got = %v, expected = %v", len(subcmds), 0)
		}
	})

	t.Run("should return an error with one arg", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryChannel(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		cmd.SetArgs([]string{"path"})
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error with three args", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryChannel(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		cmd.SetArgs([]string{"path", "chain-1", "extra"})
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func Test_queryBalanceCmd(t *testing.T) {
	t.Run("should return cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryBalanceCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "balance" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "balance")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 0 {
			t.Errorf("got = %v, expected = %v", len(subcmds), 0)
		}
	})

	t.Run("should return an error with one arg", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryBalanceCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		cmd.SetArgs([]string{"path"})
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error with three args", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryBalanceCmd(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		cmd.SetArgs([]string{"path", "chain-1", "extra"})
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func Test_queryUnrelayedPackets(t *testing.T) {
	t.Run("should return cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryUnrelayedPackets(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "unrelayed-packets" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "unrelayed-packets")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 0 {
			t.Errorf("got = %v, expected = %v", len(subcmds), 0)
		}
	})

	t.Run("should return an error with zero arg", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryUnrelayedPackets(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error with two args", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryUnrelayedPackets(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		cmd.SetArgs([]string{"path", "extra"})
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func Test_queryUnrelayedAcknowledgements(t *testing.T) {
	t.Run("should return cmd", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryUnrelayedAcknowledgements(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		if cmd.CommandPath() != "unrelayed-acknowledgements" {
			t.Errorf("got = %v, expected = %v", cmd.CommandPath(), "unrelayed-acknowledgements")
		}
		subcmds := cmd.Commands()
		if len(subcmds) != 0 {
			t.Errorf("got = %v, expected = %v", len(subcmds), 0)
		}
	})

	t.Run("should return an error with zero arg", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryUnrelayedAcknowledgements(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error with two args", func(t *testing.T) {
		codec := core.MakeCodec()
		ctx := &config.Context{Config: &config.Config{}, Codec: codec}
		cmd := queryUnrelayedAcknowledgements(ctx)
		cmd.SilenceErrors = true
		cmd.SilenceUsage = true

		cmd.SetArgs([]string{"path", "extra"})
		err := cmd.Execute()
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

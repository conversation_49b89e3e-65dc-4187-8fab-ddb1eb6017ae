//go:generate gotests -w -all $GOFILE
package cmd

import (
	"testing"
	"time"

	"github.com/spf13/cobra"
)

func Test_heightFlag(t *testing.T) {
	t.Run("can add flag", func(t *testing.T) {
		cmd := &cobra.Command{}
		cmd = heightFlag(cmd)
		f := cmd.Flag("height")
		if f == nil {
			t.<PERSON>("did not add flag")
		}
	})

	t.Run("should panic when redefine flag", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.<PERSON>("did not panic")
			}
		}()

		cmd := &cobra.Command{}
		cmd = heightFlag(cmd)
		heightFlag(cmd)
	})
}

func Test_fileFlag(t *testing.T) {
	t.Run("can add flag", func(t *testing.T) {
		cmd := &cobra.Command{}
		cmd = fileFlag(cmd)
		f := cmd.Flag("file")
		if f == nil {
			t.<PERSON>("did not add flag")
		}
	})

	t.Run("should panic when redefine flag", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.<PERSON><PERSON>("did not panic")
			}
		}()

		cmd := &cobra.Command{}
		cmd = fileFlag(cmd)
		fileFlag(cmd)
	})
}

func Test_yamlFlag(t *testing.T) {
	t.Run("can add flag", func(t *testing.T) {
		cmd := &cobra.Command{}
		cmd = yamlFlag(cmd)
		f := cmd.Flag("yaml")
		if f == nil {
			t.Errorf("did not add flag")
		}
	})

	t.Run("should panic when redefine flag", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cmd := &cobra.Command{}
		cmd = yamlFlag(cmd)
		yamlFlag(cmd)
	})
}

func Test_jsonFlag(t *testing.T) {
	t.Run("can add flag", func(t *testing.T) {
		cmd := &cobra.Command{}
		cmd = jsonFlag(cmd)
		f := cmd.Flag("json")
		if f == nil {
			t.Errorf("did not add flag")
		}
	})

	t.Run("should panic when redefine flag", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cmd := &cobra.Command{}
		cmd = jsonFlag(cmd)
		jsonFlag(cmd)
	})
}

func Test_timeoutFlag(t *testing.T) {
	t.Run("can add flag", func(t *testing.T) {
		cmd := &cobra.Command{}
		cmd = timeoutFlag(cmd)
		f := cmd.Flag("timeout")
		if f == nil {
			t.Errorf("did not add flag")
		}
	})

	t.Run("should panic when redefine flag", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cmd := &cobra.Command{}
		cmd = timeoutFlag(cmd)
		timeoutFlag(cmd)
	})
}

func Test_getTimeout(t *testing.T) {
	t.Run("can get timeout", func(t *testing.T) {
		cmd := &cobra.Command{}
		cmd = timeoutFlag(cmd)
		v, err := getTimeout(cmd)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		if v != time.Duration(1*time.Second) {
			t.Errorf("got = %v, expected = %v", v, time.Duration(1*time.Second))
		}
	})

	t.Run("should return an error when no flag exists", func(t *testing.T) {
		cmd := &cobra.Command{}
		_, err := getTimeout(cmd)
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func Test_timeoutFlags(t *testing.T) {
	t.Run("can add flag", func(t *testing.T) {
		cmd := &cobra.Command{}
		cmd = timeoutFlags(cmd)
		f := cmd.Flag("timeout-height-offset")
		if f == nil {
			t.Errorf("did not add flag")
		}
		f = cmd.Flag("timeout-time-offset")
		if f == nil {
			t.Errorf("did not add flag")
		}
	})

	t.Run("should panic when redefine flag", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cmd := &cobra.Command{}
		cmd = timeoutFlags(cmd)
		timeoutFlags(cmd)
	})
}

func Test_ibcDenomFlags(t *testing.T) {
	t.Run("can add flag", func(t *testing.T) {
		cmd := &cobra.Command{}
		cmd = ibcDenomFlags(cmd)
		f := cmd.Flag("ibc-denoms")
		if f == nil {
			t.Errorf("did not add flag")
		}
	})

	t.Run("should panic when redefine flag", func(t *testing.T) {
		defer func() {
			if err := recover(); err == nil {
				t.Errorf("did not panic")
			}
		}()

		cmd := &cobra.Command{}
		cmd = ibcDenomFlags(cmd)
		ibcDenomFlags(cmd)
	})
}

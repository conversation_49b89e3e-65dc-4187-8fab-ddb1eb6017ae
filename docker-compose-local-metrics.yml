services:

  relayer-handshake:
    image: relayer-base
    container_name: handshake
    profiles: [ "ibc" ]
    pull_policy: never
    build:
      context: .
      dockerfile: ./docker/dcjpy/Dockerfile.local
      args:
        GOLANG_VER: 1.21.7
        ALPINE_VER: 3.19
    volumes:
      - .:/opt/relayer
      - ./build/handshake:/opt/relayer/build # レースコンディション回避
    env_file:
      - ./docker/dcjpy/env/handshake.env
      - ./docker/dcjpy/env/common.env
      - ./docker/dcjpy/env/ibc-address.env
    tty: true
    command: >
      bash -c "
      make debug-build &&
      /opt/relayer/scripts/handshake/ibc-handshake account-sync,balance-sync,token-transfer
      "
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    healthcheck:
      test: [ "CMD", "/opt/relayer/scripts/healthcheck", "account-sync" ]
      interval: 10s
      timeout: 10s
      retries: 10
      start_period: 30s

  relayer-account-sync:
    image: relayer-base
    container_name: account-sync
    profiles: [ "ibc" ]
    pull_policy: never
    build:
      context: .
      dockerfile: ./docker/dcjpy/Dockerfile.local
      args:
        GOLANG_VER: 1.21.7
        ALPINE_VER: 3.19
    volumes:
      - .:/opt/relayer
      - ./build/account-sync:/opt/relayer/build # レースコンディション回避
    env_file:
      - ./docker/dcjpy/env/account-sync.env
      - ./docker/dcjpy/env/common.env
      - ./docker/dcjpy/env/ibc-address.env
    ports:
      - '12223:12223'
    tty: true
    command: air -c docker/air/.starter_air.toml
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    healthcheck:
      test: [ "CMD", "/opt/relayer/scripts/healthcheck", "account-sync" ]
      interval: 10s
      timeout: 10s
      retries: 10
      start_period: 30s
    depends_on:
      relayer-handshake:
        condition: service_healthy

  relayer-balance-sync:
    image: relayer-base
    container_name: balance-sync
    profiles: [ "ibc" ]
    pull_policy: never
    volumes:
      - .:/opt/relayer
      - ./build/balance-sync:/opt/relayer/build # レースコンディション回避
    env_file:
      - ./docker/dcjpy/env/balance-sync.env
      - ./docker/dcjpy/env/common.env
      - ./docker/dcjpy/env/ibc-address.env
    ports:
      - '22223:22223'
    tty: true
    command: air -c docker/air/.starter_air.toml
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    healthcheck:
      test: [ "CMD", "/opt/relayer/scripts/healthcheck", "balance-sync" ]
      interval: 10s
      timeout: 10s
      retries: 10
      start_period: 30s
    depends_on:
      relayer-handshake:
        condition: service_healthy

  relayer-token-transfer:
    image: relayer-base
    container_name: token-transfer
    profiles: [ "ibc" ]
    pull_policy: never
    volumes:
      - .:/opt/relayer
      - ./build/token-transfer:/opt/relayer/build # レースコンディション回避
    env_file:
      - ./docker/dcjpy/env/token-transfer.env
      - ./docker/dcjpy/env/common.env
      - ./docker/dcjpy/env/ibc-address.env
    ports:
      - '32223:32223'
    tty: true
    command: air -c docker/air/.starter_air.toml
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    healthcheck:
      test: [ "CMD", "/opt/relayer/scripts/healthcheck", "token-transfer" ]
      interval: 10s
      timeout: 10s
      retries: 3
      start_period: 30s
    depends_on:
      relayer-handshake:
        condition: service_healthy

  prometheus:
    image: prom/prometheus:v2.52.0
    container_name: prometheus
    ports:
      - '9090:9090'
    volumes:
      - './tests/localMetrics/prometheus.yml:/etc/prometheus/prometheus.yml'
      - 'prometheus-data:/prometheus'

volumes:
  prometheus-data: {}
syntax = "proto3";
package relayer.chains.ethereum.config;

import "gogoproto/gogo.proto";
import "google/protobuf/any.proto";

option go_package = "github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/relay/ethereum";
option (gogoproto.goproto_getters_all) = false;

message ChainConfig {
  string chain_id = 1;
  uint64 eth_chain_id = 2;
  string rpc_addr = 3;

  google.protobuf.Any signer = 4;

  string ibc_address = 5;

  uint64 initial_send_checkpoint = 6;
  uint64 initial_recv_checkpoint = 7;
  bool enable_debug_trace = 8;
  uint64 average_block_time_msec = 9;
  uint64 max_retry_for_inclusion = 10;

  // option for ibc-solidity ADR-001
  // if set, the relayer updates a LC contract directly if possible
  // if null, the relayer updates a LC contract via the handler
  Fraction gas_estimate_rate = 11;
  uint64 max_gas_limit = 12;
  string tx_type = 13;
}

message Fraction {
  uint64 numerator   = 1;
  uint64 denominator = 2;
}

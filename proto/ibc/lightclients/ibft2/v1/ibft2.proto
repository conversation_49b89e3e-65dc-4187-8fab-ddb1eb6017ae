syntax = "proto3";
package ibc.lightclients.ibft2.v1;

option go_package = "github.com/decurret-lab/dcbg-dcjpy-relayer/provers/ibft2/module";
import "ibc/core/client/v1/client.proto"; // COSMOS SDK を利用
import "google/protobuf/timestamp.proto";
import "google/protobuf/duration.proto";
import "gogoproto/gogo.proto";
option (gogoproto.goproto_getters_all) = false;

message ClientState {
  string chain_id = 1;
  bytes ibc_store_address = 2;
  ibc.core.client.v1.Height latest_height = 3 [(gogoproto.nullable) = false];
}

message ConsensusState {
  uint64 timestamp = 1;
  bytes root = 2;
  repeated bytes validators = 3;
}

message Header {
  bytes besu_header_rlp = 1;
  repeated bytes seals = 2;
  ibc.core.client.v1.Height trusted_height = 3 [(gogoproto.nullable) = false];
  bytes account_state_proof = 4;
}

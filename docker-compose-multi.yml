services:

  relayer-handshake:
    image: relayer-base
    profiles: [ "ibc" ]
    pull_policy: never
    build:
      context: .
      dockerfile: ./docker/dcjpy/Dockerfile.local
      args:
        GOLANG_VER: 1.21.7
        ALPINE_VER: 3.19
    volumes:
      - ./tests/localMulti/build/handshake:/opt/relayer/build
    env_file:
      - ./tests/localMulti/env/handshake.env
      - ./tests/localMulti/env/common.env
      - ./tests/localMulti/env/ibc-address.env
    tty: true
    command: >
      bash -c "
      make debug-build &&
      /opt/relayer/scripts/handshake/ibc-handshake account-sync,balance-sync,token-transfer
      "
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    healthcheck:
      test: [ "CMD", "/opt/relayer/scripts/healthcheck", "account-sync" ]
      interval: 10s
      timeout: 10s
      retries: 10
      start_period: 30s

  relayer-account-sync1:
    image: relayer-base
    profiles: [ "ibc" ]
    pull_policy: never
    build:
      context: .
      dockerfile: ./docker/dcjpy/Dockerfile.local
      args:
        GOLANG_VER: 1.21.7
        ALPINE_VER: 3.19
    volumes:
      - ./tests/localMulti/build/account-sync1:/opt/relayer/build
    env_file:
      - ./tests/localMulti/env/account-sync1.env
      - ./tests/localMulti/env/common.env
      - ./tests/localMulti/env/ibc-address.env
    tty: true
    command: air -c docker/air/.starter_air.toml
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    healthcheck:
      test: [ "CMD", "/opt/relayer/scripts/healthcheck", "account-sync" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    depends_on:
      relayer-handshake:
        condition: service_healthy

  relayer-account-sync2:
    image: relayer-base
    profiles: [ "ibc" ]
    pull_policy: never
    volumes:
      - ./tests/localMulti/build/account-sync2:/opt/relayer/build
    env_file:
      - ./tests/localMulti/env/account-sync2.env
      - ./tests/localMulti/env/common.env
      - ./tests/localMulti/env/ibc-address.env
    tty: true
    command: air -c docker/air/.starter_air.toml
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    healthcheck:
      test: [ "CMD", "/opt/relayer/scripts/healthcheck", "account-sync" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    depends_on:
      relayer-account-sync1:
        condition: service_healthy

  relayer-balance-sync1:
    image: relayer-base
    profiles: [ "ibc" ]
    pull_policy: never
    volumes:
      - ./tests/localMulti/build/balance-sync1:/opt/relayer/build
    env_file:
      - ./tests/localMulti/env/balance-sync1.env
      - ./tests/localMulti/env/common.env
      - ./tests/localMulti/env/ibc-address.env
    tty: true
    command: air -c docker/air/.starter_air.toml
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    healthcheck:
      test: [ "CMD", "/opt/relayer/scripts/healthcheck", "balance-sync" ]
      interval: 30s
      timeout: 10s
      retries: 10
      start_period: 30s
    depends_on:
      relayer-handshake:
        condition: service_healthy

  relayer-balance-sync2:
    image: relayer-base
    profiles: [ "ibc" ]
    pull_policy: never
    volumes:
      - ./tests/localMulti/build/balance-sync2:/opt/relayer/build
    env_file:
      - ./tests/localMulti/env/balance-sync2.env
      - ./tests/localMulti/env/common.env
      - ./tests/localMulti/env/ibc-address.env
    tty: true
    command: air -c docker/air/.starter_air.toml
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    healthcheck:
      test: [ "CMD", "/opt/relayer/scripts/healthcheck", "balance-sync" ]
      interval: 30s
      timeout: 10s
      retries: 10
      start_period: 30s
    depends_on:
      relayer-balance-sync1:
        condition: service_healthy

  relayer-token-transfer1:
    image: relayer-base
    profiles: [ "ibc" ]
    pull_policy: never
    volumes:
      - ./tests/localMulti/build/token-transfer1:/opt/relayer/build
    env_file:
      - ./tests/localMulti/env/token-transfer1.env
      - ./tests/localMulti/env/common.env
      - ./tests/localMulti/env/ibc-address.env
    tty: true
    command: air -c docker/air/.starter_air.toml
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    healthcheck:
      test: [ "CMD", "/opt/relayer/scripts/healthcheck", "token-transfer" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    depends_on:
      relayer-handshake:
        condition: service_healthy

  relayer-token-transfer2:
    image: relayer-base
    profiles: [ "ibc" ]
    pull_policy: never
    volumes:
      - ./tests/localMulti/build/token-transfer2:/opt/relayer/build
    env_file:
      - ./tests/localMulti/env/token-transfer2.env
      - ./tests/localMulti/env/common.env
      - ./tests/localMulti/env/ibc-address.env
    tty: true
    command: air -c docker/air/.starter_air.toml
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
    healthcheck:
      test: [ "CMD", "/opt/relayer/scripts/healthcheck", "token-transfer" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    depends_on:
      relayer-token-transfer1:
        condition: service_healthy
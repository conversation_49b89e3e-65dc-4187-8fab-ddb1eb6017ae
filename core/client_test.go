//go:generate mockgen -source=$GOFILE -package=core -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package core

import (
	"testing"

	sdk "github.com/cosmos/cosmos-sdk/types"
	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	"github.com/cosmos/ibc-go/v7/modules/core/exported"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/log"
	"go.uber.org/mock/gomock"
	"golang.org/x/exp/slog"
)

func TestCreateClients(t *testing.T) {
	t.Skip("cannot not possible because ibft2 dependency.")

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcMockChain := NewMockChain(ctrl)
	dstMockChain := NewMockChain(ctrl)
	srcMockProver := NewMockProver(ctrl)
	dstMockProver := NewMockProver(ctrl)

	//// set expectations
	//srcChainId := "src-chain-1"
	//dstChainId := "dst-chain-1"

	stubHeight := clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}
	//stubAddress := sdk.AccAddress{}

	//// ibft2
	//srcStubClientState := &ibft2.ClientState{
	//	ChainId:         srcChainId,
	//	IbcStoreAddress: stubAddress,
	//	LatestHeight:    stubHeight,
	//}
	//dstStubClientState := &ibft2.ClientState{
	//	ChainId:         dstChainId,
	//	IbcStoreAddress: stubAddress,
	//	LatestHeight:    stubHeight,
	//}
	//
	//srcStubConsensusState := &ibft2.ConsensusState{
	//	Timestamp:  uint64(1717078905),
	//	Root:       []byte{},
	//	Validators: [][]byte{},
	//}
	//
	//dstStubConsensusState := &ibft2.ConsensusState{
	//	Timestamp:  uint64(1717078905),
	//	Root:       []byte{},
	//	Validators: [][]byte{},
	//}

	type args struct {
		pathName  string
		src       *ProvableChain
		dst       *ProvableChain
		srcHeight exported.Height
		dstHeight exported.Height
	}
	tests := []struct {
		name string
		//prepareMock func()
		args    args
		wantErr bool
	}{
		{
			name: "should create clients",
			//prepareMock: func() {
			//
			//	srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			//	srcMockChain.EXPECT().ChainID().Return(srcChainId).AnyTimes()
			//	srcMockChain.EXPECT().GetAddress().Return(stubAddress, nil).AnyTimes()
			//
			//	dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			//	dstMockChain.EXPECT().ChainID().Return(dstChainId).AnyTimes()
			//	dstMockChain.EXPECT().GetAddress().Return(stubAddress, nil).AnyTimes()
			//
			//	srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			//	srcMockProver.EXPECT().CreateInitialLightClientState(gomock.Any()).Return(srcStubClientState, srcStubConsensusState, nil).AnyTimes()
			//
			//	dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			//	dstMockProver.EXPECT().CreateInitialLightClientState(gomock.Any()).Return(dstStubClientState, dstStubConsensusState, nil).AnyTimes()
			//},
			args: args{
				pathName: "",
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
				srcHeight: stubHeight,
				dstHeight: stubHeight,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//tt.prepareMock()
			if err := CreateClients(tt.args.pathName, tt.args.src, tt.args.dst, tt.args.srcHeight, tt.args.dstHeight); (err != nil) != tt.wantErr {
				t.Errorf("CreateClients() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestUpdateClients(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcMockChain := NewMockChain(ctrl)
	dstMockChain := NewMockChain(ctrl)
	srcMockProver := NewMockProver(ctrl)
	dstMockProver := NewMockProver(ctrl)
	//mockMockSyncHeaders := NewMockSyncHeaders(ctrl)

	// set expectations
	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	//stubHeight := clienttypes.Height{
	//	RevisionNumber: 0,
	//	RevisionHeight: 100,
	//}
	stubAddress := sdk.AccAddress{}

	type args struct {
		src *ProvableChain
		dst *ProvableChain
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		wantErr     bool
	}{
		{
			name: "should return failed to create sync headers for update client error.",
			prepareMock: func() {
				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAddress, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAddress, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(nil, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(nil, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
			},
			wantErr: true,
		},
		{
			name: "should update clients",
			prepareMock: func() {
				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAddress, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAddress, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(nil, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(nil, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
			},
			wantErr: false,
		},
		// TODO Mock では発生不可
		//{
		//	name: "should return failed to setup both headers for update client error.",
		//	prepareMock: func() {
		//		srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
		//		srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
		//		srcMockChain.EXPECT().GetAddress().Return(stubAddress, nil).AnyTimes()
		//
		//		dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
		//		dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
		//		dstMockChain.EXPECT().GetAddress().Return(stubAddress, nil).AnyTimes()
		//
		//		srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(nil, nil).AnyTimes()
		//		srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(nil, nil).AnyTimes()
		//		dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockMockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil, nil).AnyTimes()
		//	},
		//	args: args{
		//		src: &ProvableChain{
		//			Chain:  srcMockChain,
		//			Prover: srcMockProver,
		//		},
		//		dst: &ProvableChain{
		//			Chain:  dstMockChain,
		//			Prover: dstMockProver,
		//		},
		//	},
		//	wantErr: true,
		//},

	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			if err := UpdateClients(tt.args.src, tt.args.dst); (err != nil) != tt.wantErr {
				t.Errorf("UpdateClients() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestGetClientPairLogger(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcMockChain := NewMockChain(ctrl)
	dstMockChain := NewMockChain(ctrl)

	// set expectations
	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	type args struct {
		src Chain
		dst Chain
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		want        *log.DcjpyLogger
	}{
		{
			name: "should create clients pair logger",
			prepareMock: func() {
				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
			},
			args: args{
				src: srcMockChain,
				dst: dstMockChain,
			},
			want: &log.DcjpyLogger{
				Logger: &slog.Logger{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			got := GetClientPairLogger(tt.args.src, tt.args.dst)
			if got == nil {
				t.Errorf("GetClientPairLogger() = %v, want %v", got, tt.want)
			}
		})
	}
}

//go:generate mockgen -source=$GOFILE -package=mock_core -destination=./mock_core/mock_$GOFILE -self_package=github.com/decurret-lab/dcbg-dcjpy-relayer/core
//go:generate gotests -w -all $GOFILE
package core

import (
	"reflect"
	"testing"
)

// TODO 検出対象の packet に値が設置できないためテスト実施不可
//func TestGetPacketsFromEvents(t *testing.T) {
//
//	// Generating a Controller that manages mock calls
//	//ctrl := gomock.NewController(t)
//
//	// Generate a mock instance
//
//	// set expectations
//
//	type args struct {
//		events    []abci.Event
//		eventType string
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    []channeltypes.Packet
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//		{
//			name: "packet_data",
//			args: args{
//				events: []abci.Event{
//					{
//						Type: channeltypes.AttributeKeyData,
//						Attributes: []abci.EventAttribute{
//							{Key: "", Value: "", Index: false},
//						},
//					},
//				},
//				eventType: "packet_data",
//			},
//			want: []channeltypes.Packet{
//				{
//					Sequence:           strToUint64("100"),
//					SourcePort:         "account-sync",
//					SourceChannel:      "channel-0",
//					DestinationPort:    "account-sync",
//					DestinationChannel: "channel-0",
//					Data:               []byte{},
//					TimeoutHeight: types.Height{
//						RevisionNumber: 100,
//						RevisionHeight: 100,
//					},
//					TimeoutTimestamp: strToUint64("100"),
//				},
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := GetPacketsFromEvents(tt.args.events, tt.args.eventType)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("GetPacketsFromEvents() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("GetPacketsFromEvents() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
//// TODO 検出対象の packet に値が設置できないためテスト実施不可
//func TestFindPacketFromEventsBySequence(t *testing.T) {
//
//	type args struct {
//		events    []abci.Event
//		eventType string
//		seq       uint64
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    *channeltypes.Packet
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//		{},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := FindPacketFromEventsBySequence(tt.args.events, tt.args.eventType, tt.args.seq)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("FindPacketFromEventsBySequence() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("FindPacketFromEventsBySequence() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func Test_packetAcknowledgement_Data(t *testing.T) {
	type fields struct {
		srcPortID    string
		srcChannelID string
		dstPortID    string
		dstChannelID string
		sequence     uint64
		data         []byte
	}
	tests := []struct {
		name   string
		fields fields
		want   []byte
	}{
		{
			name: "account-sync",
			fields: fields{
				srcPortID:    "account-sync",
				srcChannelID: "channel-0",
				dstPortID:    "account-sync",
				dstChannelID: "channel-0",
				sequence:     strToUint64("1"),
				data:         []byte{100},
			},
			want: []byte{100},
		},
		{
			name: "balance-sync",
			fields: fields{
				srcPortID:    "balance-sync",
				srcChannelID: "channel-1",
				dstPortID:    "balance-sync",
				dstChannelID: "channel-1",
				sequence:     strToUint64("2"),
				data:         []byte{100, 101},
			},
			want: []byte{100, 101},
		},
		{
			name: "token-transfer",
			fields: fields{
				srcPortID:    "token-transfer",
				srcChannelID: "channel-2",
				dstPortID:    "token-transfer",
				dstChannelID: "channel-2",
				sequence:     strToUint64("3"),
				data:         []byte{100, 101, 102},
			},
			want: []byte{100, 101, 102},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ack := packetAcknowledgement{
				srcPortID:    tt.fields.srcPortID,
				srcChannelID: tt.fields.srcChannelID,
				dstPortID:    tt.fields.dstPortID,
				dstChannelID: tt.fields.dstChannelID,
				sequence:     tt.fields.sequence,
				data:         tt.fields.data,
			}
			if got := ack.Data(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("packetAcknowledgement.Data() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TODO 検出対象の packetAcknowledgement に値が設置できないためテスト実施不可
//func TestGetPacketAcknowledgementsFromEvents(t *testing.T) {
//	type args struct {
//		events []abci.Event
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    []packetAcknowledgement
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//		{
//			name: "account-sync",
//			args: args{
//				events: []abci.Event{
//					{
//						Type: channeltypes.AttributeKeyData,
//						Attributes: []abci.EventAttribute{
//							{Key: "", Value: "", Index: false},
//						},
//					},
//				},
//			},
//			want: []packetAcknowledgement{
//				{
//					srcPortID:    "account-sync",
//					srcChannelID: "channel-0",
//					dstPortID:    "account-sync",
//					dstChannelID: "channel-0",
//					sequence:     strToUint64("1"),
//					data:         []byte{100},
//				},
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := GetPacketAcknowledgementsFromEvents(tt.args.events)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("GetPacketAcknowledgementsFromEvents() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("GetPacketAcknowledgementsFromEvents() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
//// TODO 検出対象の packetAcknowledgement に値が設置できないためテスト実施不可
//func TestFindPacketAcknowledgementFromEventsBySequence(t *testing.T) {
//	type args struct {
//		events []abci.Event
//		seq    uint64
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    *packetAcknowledgement
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := FindPacketAcknowledgementFromEventsBySequence(tt.args.events, tt.args.seq)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("FindPacketAcknowledgementFromEventsBySequence() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("FindPacketAcknowledgementFromEventsBySequence() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func Test_assertIndex(t *testing.T) {
	type args struct {
		actual   int
		expected int
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "should assertion true.",
			args: args{
				actual:   1,
				expected: 1,
			},
			wantErr: false,
		},
		{
			name: "should assertion false. ",
			args: args{
				actual:   1,
				expected: 2,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := assertIndex(tt.args.actual, tt.args.expected); (err != nil) != tt.wantErr {
				t.Errorf("assertIndex() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_strToUint64(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name string
		args args
		want uint64
	}{
		{
			name: "Should convert a string representing an integer (in decimal or hexadecimal notation) to a number.",
			args: args{
				s: "0",
			},
			want: strToUint64("0"),
		},
		{
			name: "Should convert a string representing an integer (in decimal or hexadecimal notation) to a number.",
			args: args{
				s: "1",
			},
			want: strToUint64("1"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := strToUint64(tt.args.s); got != tt.want {
				t.Errorf("strToUint64() = %v, want %v", got, tt.want)
			}
		})
	}
}

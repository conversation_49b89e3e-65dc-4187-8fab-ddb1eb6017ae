//go:generate mockgen -source=$GOFILE -package=core -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package core

import "testing"

func Test_isMsgEventLog_is_MsgEventLog(t *testing.T) {

	tests := []struct {
		name string
		i    isMsgEventLog
	}{
		{
			name: "should must be embedded in implementation of the `MsgEventLog` interface.",
			i:    isMsgEventLog{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := isMsgEventLog{}
			i.is_MsgEventLog()
		})
	}
}

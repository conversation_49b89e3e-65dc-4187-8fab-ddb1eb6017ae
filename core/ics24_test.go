//go:generate mockgen -source=$GOFILE -package=core -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package core

import (
	"testing"
)

func TestPathEnd_Vclient(t *testing.T) {

	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "src account-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			wantErr: false,
		},
		{
			name: "dst account-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			wantErr: false,
		},
		{
			name: "src balance-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			wantErr: false,
		},
		{
			name: "dst balance-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			wantErr: false,
		},
		{
			name: "src token-transfer",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			},
			wantErr: false,
		},
		{
			name: "dst token-transfer",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if err := pe.Vclient(); (err != nil) != tt.wantErr {
				t.Errorf("PathEnd.Vclient() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}

	t.Run("return nil when ClientId is empty", func(t *testing.T) {
		pe := &PathEnd{
			ChainID:      "********",
			ClientID:     "",
			ConnectionID: "connection-0",
			ChannelID:    "channel-2",
			PortID:       "token-transfer",
			Order:        "unordered",
			Version:      "token-transfer-1",
		}
		err := pe.Vclient()
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})
}

func TestPathEnd_Vconn(t *testing.T) {

	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "src account-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			wantErr: false,
		},
		{
			name: "dst account-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			wantErr: false,
		},
		{
			name: "src balance-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			wantErr: false,
		},
		{
			name: "dst balance-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			wantErr: false,
		},
		{
			name: "src token-transfer",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			},
			wantErr: false,
		},
		{
			name: "dst token-transfer",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if err := pe.Vconn(); (err != nil) != tt.wantErr {
				t.Errorf("PathEnd.Vconn() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}

	t.Run("return nil when ConnectionID is empty", func(t *testing.T) {
		pe := &PathEnd{
			ChainID:      "********",
			ClientID:     "hb-ibft2-0",
			ConnectionID: "",
			ChannelID:    "channel-2",
			PortID:       "token-transfer",
			Order:        "unordered",
			Version:      "token-transfer-1",
		}
		err := pe.Vconn()
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})
}

func TestPathEnd_Vchan(t *testing.T) {
	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "src account-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			wantErr: false,
		},
		{
			name: "dst account-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			wantErr: false,
		},
		{
			name: "src balance-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			wantErr: false,
		},
		{
			name: "dst balance-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			wantErr: false,
		},
		{
			name: "src token-transfer",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			},
			wantErr: false,
		},
		{
			name: "dst token-transfer",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if err := pe.Vchan(); (err != nil) != tt.wantErr {
				t.Errorf("PathEnd.Vchan() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}

	t.Run("return nil when ChannelID is empty", func(t *testing.T) {
		pe := &PathEnd{
			ChainID:      "********",
			ClientID:     "hb-ibft2-0",
			ConnectionID: "connection-0",
			ChannelID:    "",
			PortID:       "token-transfer",
			Order:        "unordered",
			Version:      "token-transfer-1",
		}
		err := pe.Vchan()
		if err != nil {
			t.Errorf("got = %v", err)
		}
	})
}

func TestPathEnd_Vport(t *testing.T) {
	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "src account-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			wantErr: false,
		},
		{
			name: "dst account-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			wantErr: false,
		},
		{
			name: "src balance-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			wantErr: false,
		},
		{
			name: "dst balance-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			wantErr: false,
		},
		{
			name: "src token-transfer",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			},
			wantErr: false,
		},
		{
			name: "dst token-transfer",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if err := pe.Vport(); (err != nil) != tt.wantErr {
				t.Errorf("PathEnd.Vport() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPathEnd_Vversion(t *testing.T) {
	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "src account-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			wantErr: false,
		},
		{
			name: "dst account-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			wantErr: false,
		},
		{
			name: "src balance-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			wantErr: false,
		},
		{
			name: "dst balance-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			wantErr: false,
		},
		{
			name: "src token-transfer",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			},
			wantErr: false,
		},
		{
			name: "dst token-transfer",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if err := pe.Vversion(); (err != nil) != tt.wantErr {
				t.Errorf("PathEnd.Vversion() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPathEnd_String(t *testing.T) {
	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "src account-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			want: PathEnd{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			}.String(),
		},
		{
			name: "dst account-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			want: PathEnd{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			}.String(),
		},
		{
			name: "src balance-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			want: PathEnd{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			}.String(),
		},
		{
			name: "dst balance-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			want: PathEnd{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			}.String(),
		},
		{
			name: "src token-transfer",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			},
			want: PathEnd{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			}.String(),
		},
		{
			name: "dst token-transfer",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			},
			want: PathEnd{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			}.String(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if got := pe.String(); got != tt.want {
				t.Errorf("PathEnd.String() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPathEnd_Validate(t *testing.T) {
	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "src account-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			wantErr: false,
		},
		{
			name: "dst account-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			wantErr: false,
		},
		{
			name: "src balance-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			wantErr: false,
		},
		{
			name: "dst balance-sync",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			wantErr: false,
		},
		{
			name: "src token-transfer",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			},
			wantErr: false,
		},
		{
			name: "dst token-transfer",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if err := pe.Validate(); (err != nil) != tt.wantErr {
				t.Errorf("PathEnd.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}

	errTests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{{
		name: "return an error when ChainID is invalid",
		fields: fields{
			ChainID:      "********0000000000000000000",
			ClientID:     "0",
			ConnectionID: "connection-0",
			ChannelID:    "channel-0",
			PortID:       "account-sync",
			Order:        "unordered",
			Version:      "account-sync-1",
		},
		wantErr: true,
	},
		{
			name: "return an error when ConnectionID is invalid",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			wantErr: true,
		},
		{
			name: "return an error when ChannelID is invalid",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "0",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			wantErr: true,
		},
		{
			name: "return an error when PortID is invalid",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "0",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			wantErr: true,
		},
		{
			name: "return an error when Order is invalid",
			fields: fields{
				ChainID:      "********",
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "none",
				Version:      "token-transfer-1",
			},
			wantErr: true,
		},
	}
	for _, tt := range errTests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if err := pe.Validate(); (err != nil) != tt.wantErr {
				t.Errorf("did not return an error")
			}
		})
	}
}

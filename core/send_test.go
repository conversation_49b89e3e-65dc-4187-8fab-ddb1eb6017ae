//go:generate mockgen -source=$GOFILE -package=mock_core -destination=./mock_core/mock_$GOFILE -self_package=github.com/decurret-lab/dcbg-dcjpy-relayer/core
//go:generate gotests -w -all $GOFILE
package core

import (
	"reflect"
	"testing"
	"time"

	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	"github.com/google/go-cmp/cmp"
	"go.uber.org/mock/gomock"
)

func TestGetFinalizedMsgResult(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockSrcProver := NewMockProver(ctrl)
	mockHeaders := NewMockHeader(ctrl)
	mocMsgResult := NewMockMsgResult(ctrl)

	srcChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	wantHeight := clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}

	mockAverageBlockTime := time.Duration(2000) * time.Millisecond

	type args struct {
		chain ProvableChain
		msgID MsgID
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		want        MsgResult
		wantErr     bool
	}{
		{
			name: "should wait until message execution completes and return result.",
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().AverageBlockTime().Return(mockAverageBlockTime).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().GetMsgResult(gomock.Any()).Return(mocMsgResult, nil).AnyTimes()

				mockHeaders.EXPECT().GetHeight().Return(wantHeight).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().GetLatestFinalizedHeader().Return(mockHeaders, nil).AnyTimes()

				mocMsgResult.EXPECT().Status().Return(true, "ok").AnyTimes()
				mocMsgResult.EXPECT().BlockHeight().Return(wantHeight).AnyTimes()
			},
			args: args{
				chain: ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				msgID: nil,
			},
			want:    mocMsgResult,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			got, err := GetFinalizedMsgResult(tt.args.chain, tt.args.msgID)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFinalizedMsgResult() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("GetFinalizedMsgResult differs: (-got +want)n%s", diff)
				}
				t.Errorf("GetFinalizedMsgResult() = %v, want %v", got, tt.want)
			}
		})
	}
}

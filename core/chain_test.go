//go:generate mockgen -source=$GOFILE -package=core -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package core

import (
	"context"
	"fmt"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/cosmos/cosmos-sdk/codec"
	"github.com/cosmos/ibc-go/v7/modules/core/exported"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/log"
	gomock "go.uber.org/mock/gomock"
	"golang.org/x/exp/slog"
)

func TestMain(m *testing.M) {
	log.InitLogger("Debug", "text", os.Stdout)
	exitVal := m.Run()
	os.Exit(exitVal)
}

func TestNewProvableChain(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockChain := NewMockChain(ctrl)
	mockProver := NewMockProver(ctrl)

	type args struct {
		chain  Chain
		prover Prover
	}
	tests := []struct {
		name string
		args args
		want *ProvableChain
	}{
		// TableDrivenTests(https://go.dev/wiki/TableDrivenTests)
		{
			name: "should returns a new ProvableChain instance.",
			args: args{
				chain:  mockChain,
				prover: mockProver,
			},
			want: &ProvableChain{
				Chain:  mockChain,
				Prover: mockProver,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewProvableChain(tt.args.chain, tt.args.prover); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewProvableChain() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProvableChain_Init(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockChain := NewMockChain(ctrl)
	mockProver := NewMockProver(ctrl)
	mockCodec := MakeCodec()

	mockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	type fields struct {
		Chain  Chain
		Prover Prover
	}
	type args struct {
		homePath string
		timeout  time.Duration
		codec    codec.ProtoCodecMarshaler
		debug    bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TableDrivenTests(https://go.dev/wiki/TableDrivenTests)
		{
			name: "initializes the chain and the prover",
			fields: fields{
				Chain:  mockChain,
				Prover: mockProver,
			},
			args: args{
				homePath: "/dev/null",
				timeout:  0,
				codec:    mockCodec,
				debug:    false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pc := &ProvableChain{
				Chain:  tt.fields.Chain,
				Prover: tt.fields.Prover,
			}
			if err := pc.Init(tt.args.homePath, tt.args.timeout, tt.args.codec, tt.args.debug); (err != nil) != tt.wantErr {
				t.Errorf("ProvableChain.Init() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}

	t.Run("should return an error when Chain.Init fails", func(t *testing.T) {
		ctrl := gomock.NewController(t)

		mockChain := NewMockChain(ctrl)
		mockProver := NewMockProver(ctrl)
		mockCodec := MakeCodec()

		mockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("test error")).AnyTimes()
		mockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

		pc := &ProvableChain{
			Chain:  mockChain,
			Prover: mockProver,
		}
		err := pc.Init("/dev/null", 0, mockCodec, false)
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error when Prover.Init fails", func(t *testing.T) {
		ctrl := gomock.NewController(t)

		mockChain := NewMockChain(ctrl)
		mockProver := NewMockProver(ctrl)
		mockCodec := MakeCodec()

		mockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		mockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("test error")).AnyTimes()

		pc := &ProvableChain{
			Chain:  mockChain,
			Prover: mockProver,
		}
		err := pc.Init("/dev/null", 0, mockCodec, false)
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func TestProvableChain_SetRelayInfo(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockChain := NewMockChain(ctrl)
	mockProver := NewMockProver(ctrl)

	mockChainId := "chain-1"
	mockChain.EXPECT().ChainID().Return(mockChainId).AnyTimes()
	mockChain.EXPECT().SetRelayInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockProver.EXPECT().SetRelayInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	type fields struct {
		Chain  Chain
		Prover Prover
	}
	type args struct {
		path             *PathEnd
		counterparty     *ProvableChain
		counterpartyPath *PathEnd
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TableDrivenTests(https://go.dev/wiki/TableDrivenTests)
		{
			name: "should sets source's path and counterparty's info to the chain.",
			fields: fields{
				Chain:  mockChain,
				Prover: mockProver,
			},
			args: args{
				path: &PathEnd{
					ChainID:      "chain-0",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				counterparty: &ProvableChain{
					Chain:  mockChain,
					Prover: mockProver,
				},
				counterpartyPath: &PathEnd{
					ChainID:      "chain-0",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pc := &ProvableChain{
				Chain:  tt.fields.Chain,
				Prover: tt.fields.Prover,
			}
			if err := pc.SetRelayInfo(tt.args.path, tt.args.counterparty, tt.args.counterpartyPath); (err != nil) != tt.wantErr {
				t.Errorf("ProvableChain.SetRelayInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}

	t.Run("should return an error when Chain.SetRelayInfo fails", func(t *testing.T) {
		ctrl := gomock.NewController(t)

		mockChain := NewMockChain(ctrl)
		mockProver := NewMockProver(ctrl)

		mockChain.EXPECT().SetRelayInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("test error")).AnyTimes()
		mockProver.EXPECT().SetRelayInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

		pc := &ProvableChain{
			Chain:  mockChain,
			Prover: mockProver,
		}
		err := pc.SetRelayInfo(&PathEnd{}, &ProvableChain{}, &PathEnd{})
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error when Prover.SetRelayInfo fails", func(t *testing.T) {
		ctrl := gomock.NewController(t)

		mockChain := NewMockChain(ctrl)
		mockProver := NewMockProver(ctrl)

		mockChain.EXPECT().SetRelayInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		mockProver.EXPECT().SetRelayInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("test error")).AnyTimes()

		pc := &ProvableChain{
			Chain:  mockChain,
			Prover: mockProver,
		}
		err := pc.SetRelayInfo(&PathEnd{}, &ProvableChain{}, &PathEnd{})
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func TestProvableChain_SetupForRelay(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockChain := NewMockChain(ctrl)
	mockProver := NewMockProver(ctrl)

	mockChainId := "chain-1"
	mockChain.EXPECT().ChainID().Return(mockChainId).AnyTimes()
	mockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
	mockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()

	type fields struct {
		Chain  Chain
		Prover Prover
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TableDrivenTests(https://go.dev/wiki/TableDrivenTests)
		{
			name: "should performs chain-specific setup before starting the relay.",
			fields: fields{
				Chain:  mockChain,
				Prover: mockProver,
			},
			args: args{
				ctx: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pc := &ProvableChain{
				Chain:  tt.fields.Chain,
				Prover: tt.fields.Prover,
			}
			if err := pc.SetupForRelay(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("ProvableChain.SetupForRelay() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}

	t.Run("should return an error when Chain.SetupForRelay fails", func(t *testing.T) {
		ctrl := gomock.NewController(t)

		mockChain := NewMockChain(ctrl)
		mockProver := NewMockProver(ctrl)

		mockChain.EXPECT().SetupForRelay(gomock.Any()).Return(fmt.Errorf("test error")).AnyTimes()
		mockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()

		pc := &ProvableChain{
			Chain:  mockChain,
			Prover: mockProver,
		}
		err := pc.SetupForRelay(context.TODO())
		if err == nil {
			t.Errorf("did not return an error")
		}
	})

	t.Run("should return an error when Prover.SetupForRelay fails", func(t *testing.T) {
		ctrl := gomock.NewController(t)

		mockChain := NewMockChain(ctrl)
		mockProver := NewMockProver(ctrl)

		mockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		mockProver.EXPECT().SetupForRelay(gomock.Any()).Return(fmt.Errorf("test error")).AnyTimes()

		pc := &ProvableChain{
			Chain:  mockChain,
			Prover: mockProver,
		}
		err := pc.SetupForRelay(context.TODO())
		if err == nil {
			t.Errorf("did not return an error")
		}
	})
}

func TestNewQueryContext(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockQueryContext := NewMockQueryContext(ctrl)
	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
	mockQueryContext.EXPECT().Height().Return(nil).AnyTimes()

	type args struct {
		ctx    context.Context
		height exported.Height
	}
	tests := []struct {
		name string
		args args
		want QueryContext
	}{
		{
			name: "should returns a new context for querying states.",
			args: args{
				ctx:    mockQueryContext.Context(),
				height: mockQueryContext.Height(),
			},
			want: queryContext{
				ctx:    mockQueryContext.Context(),
				height: mockQueryContext.Height(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewQueryContext(tt.args.ctx, tt.args.height); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewQueryContext() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_queryContext_Context(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockQueryContext := NewMockQueryContext(ctrl)
	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
	mockQueryContext.EXPECT().Height().Return(nil).AnyTimes()

	type fields struct {
		ctx    context.Context
		height exported.Height
	}
	tests := []struct {
		name   string
		fields fields
		want   context.Context
	}{
		// TableDrivenTests(https://go.dev/wiki/TableDrivenTests)
		{
			name: "should returns `context.Context`",
			fields: fields{
				ctx:    mockQueryContext.Context(),
				height: mockQueryContext.Height(),
			},
			want: mockQueryContext.Context(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			qc := queryContext{
				ctx:    tt.fields.ctx,
				height: tt.fields.height,
			}
			if got := qc.Context(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("queryContext.Context() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_queryContext_Height(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockQueryContext := NewMockQueryContext(ctrl)
	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
	mockQueryContext.EXPECT().Height().Return(nil).AnyTimes()

	type fields struct {
		ctx    context.Context
		height exported.Height
	}
	tests := []struct {
		name   string
		fields fields
		want   exported.Height
	}{
		// TableDrivenTests(https://go.dev/wiki/TableDrivenTests)
		{
			name: "should returns a height of the target chain for querying a state.",
			fields: fields{
				ctx:    mockQueryContext.Context(),
				height: mockQueryContext.Height(),
			},
			want: mockQueryContext.Height(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			qc := queryContext{
				ctx:    tt.fields.ctx,
				height: tt.fields.height,
			}
			if got := qc.Height(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("queryContext.Height() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetChainLogger(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockQueryContext := NewMockQueryContext(ctrl)
	mockChainInfo := NewMockChainInfo(ctrl)

	// set expectations
	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
	mockQueryContext.EXPECT().Height().Return(nil).AnyTimes()

	mockChainId := "src-chain-1"
	mockLatestHeight := mockQueryContext.Height()
	mockTimestamp := time.Now()
	mockAverageBlockTime := time.Duration(2000) * time.Millisecond
	mockChainInfo.EXPECT().ChainID().Return(mockChainId).AnyTimes()
	mockChainInfo.EXPECT().LatestHeight().Return(nil, nil).AnyTimes()
	mockChainInfo.EXPECT().Timestamp(mockLatestHeight).Return(mockTimestamp, nil).AnyTimes()
	mockChainInfo.EXPECT().AverageBlockTime().Return(mockAverageBlockTime).AnyTimes()

	type args struct {
		chain ChainInfo
	}
	tests := []struct {
		name string
		args args
		want *log.DcjpyLogger
	}{
		// TableDrivenTests(https://go.dev/wiki/TableDrivenTests)
		{
			name: "should returns a chain info",
			args: args{
				chain: mockChainInfo,
			},
			want: &log.DcjpyLogger{
				Logger: &slog.Logger{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetChainLogger(tt.args.chain)
			if got == nil {
				t.Errorf("GetChainLogger() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetChainPairLogger(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockQueryContext := NewMockQueryContext(ctrl)
	mockSrcChainInfo := NewMockChainInfo(ctrl)
	mockDstChainInfo := NewMockChainInfo(ctrl)

	// set expectations
	mockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
	mockQueryContext.EXPECT().Height().Return(nil).AnyTimes()

	mockSrcChainId := "src-chain-1"
	mockDstChainId := "dst-chain-1"
	mockLatestHeight := mockQueryContext.Height()
	mockTimestamp := time.Now()
	mockAverageBlockTime := time.Duration(2000) * time.Millisecond

	mockSrcChainInfo.EXPECT().ChainID().Return(mockSrcChainId).AnyTimes()
	mockSrcChainInfo.EXPECT().LatestHeight().Return(nil, nil).AnyTimes()
	mockSrcChainInfo.EXPECT().Timestamp(mockLatestHeight).Return(mockTimestamp, nil).AnyTimes()
	mockSrcChainInfo.EXPECT().AverageBlockTime().Return(mockAverageBlockTime).AnyTimes()

	mockDstChainInfo.EXPECT().ChainID().Return(mockDstChainId).AnyTimes()
	mockDstChainInfo.EXPECT().LatestHeight().Return(nil, nil).AnyTimes()
	mockDstChainInfo.EXPECT().Timestamp(mockLatestHeight).Return(mockTimestamp, nil).AnyTimes()
	mockDstChainInfo.EXPECT().AverageBlockTime().Return(mockAverageBlockTime).AnyTimes()

	type args struct {
		src ChainInfo
		dst ChainInfo
	}
	tests := []struct {
		name string
		args args
		want *log.DcjpyLogger
	}{
		// TableDrivenTests(https://go.dev/wiki/TableDrivenTests)
		{
			name: "should returns a chain pair logger",
			args: args{
				src: mockSrcChainInfo,
				dst: mockDstChainInfo,
			},
			want: &log.DcjpyLogger{
				Logger: &slog.Logger{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetChainPairLogger(tt.args.src, tt.args.dst)
			if got == nil {
				t.Errorf("GetChainPairLogger() = %v, want %v", got, tt.want)
			}
		})
	}
}

//go:generate mockgen -source=$GOFILE -package=mock_core -destination=./mock_core/mock_$GOFILE -self_package=github.com/decurret-lab/dcbg-dcjpy-relayer/core
//go:generate gotests -w -all $GOFILE
package core

import (
	"reflect"
	"testing"

	"github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	chantypes "github.com/cosmos/ibc-go/v7/modules/core/04-channel/types"
)

func TestPacketInfoList_ExtractSequenceList(t *testing.T) {

	tests := []struct {
		name string
		ps   PacketInfoList
		want []uint64
	}{
		{
			name: "should return single sequence list.",
			ps: PacketInfoList{
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           1,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 100,
							RevisionHeight: 100,
						},
						TimeoutTimestamp: 100,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
			},
			want: []uint64{1},
		},

		{
			name: "should return multiple sequence list.",
			ps: PacketInfoList{
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           1,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 100,
							RevisionHeight: 100,
						},
						TimeoutTimestamp: 100,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           2,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 200,
							RevisionHeight: 200,
						},
						TimeoutTimestamp: 200,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           3,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 300,
							RevisionHeight: 300,
						},
						TimeoutTimestamp: 300,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
			},
			want: []uint64{1, 2, 3},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.ps.ExtractSequenceList(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PacketInfoList.ExtractSequenceList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPacketInfoList_Subtract(t *testing.T) {

	type args struct {
		seqs []uint64
	}
	tests := []struct {
		name string
		ps   PacketInfoList
		args args
		want PacketInfoList
	}{
		{
			name: "should return a list excluding one list",
			ps: PacketInfoList{
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           1,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 100,
							RevisionHeight: 100,
						},
						TimeoutTimestamp: 100,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           2,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 200,
							RevisionHeight: 200,
						},
						TimeoutTimestamp: 200,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           3,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 300,
							RevisionHeight: 300,
						},
						TimeoutTimestamp: 300,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
			},
			args: args{
				seqs: []uint64{1},
			},
			want: PacketInfoList{
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           2,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 200,
							RevisionHeight: 200,
						},
						TimeoutTimestamp: 200,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           3,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 300,
							RevisionHeight: 300,
						},
						TimeoutTimestamp: 300,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
			},
		},
		{
			name: "should return a list excluding two list",
			ps: PacketInfoList{
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           1,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 100,
							RevisionHeight: 100,
						},
						TimeoutTimestamp: 100,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           2,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 200,
							RevisionHeight: 200,
						},
						TimeoutTimestamp: 200,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           3,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 300,
							RevisionHeight: 300,
						},
						TimeoutTimestamp: 300,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
			},
			args: args{
				seqs: []uint64{1, 2},
			},
			want: PacketInfoList{
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           3,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 300,
							RevisionHeight: 300,
						},
						TimeoutTimestamp: 300,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.ps.Subtract(tt.args.seqs); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PacketInfoList.Subtract() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPacketInfoList_Filter(t *testing.T) {
	type args struct {
		seqs []uint64
	}
	tests := []struct {
		name string
		ps   PacketInfoList
		args args
		want PacketInfoList
	}{
		// TODO: Add test cases.
		{
			name: "should return a list filter one list",
			ps: PacketInfoList{
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           1,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 100,
							RevisionHeight: 100,
						},
						TimeoutTimestamp: 100,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           2,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 200,
							RevisionHeight: 200,
						},
						TimeoutTimestamp: 200,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           3,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 300,
							RevisionHeight: 300,
						},
						TimeoutTimestamp: 300,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
			},
			args: args{
				seqs: []uint64{1},
			},
			want: PacketInfoList{
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           1,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 100,
							RevisionHeight: 100,
						},
						TimeoutTimestamp: 100,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
			},
		},
		{
			name: "should return a list filter two list",
			ps: PacketInfoList{
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           1,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 100,
							RevisionHeight: 100,
						},
						TimeoutTimestamp: 100,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           2,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 200,
							RevisionHeight: 200,
						},
						TimeoutTimestamp: 200,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           3,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 300,
							RevisionHeight: 300,
						},
						TimeoutTimestamp: 300,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
			},
			args: args{
				seqs: []uint64{2, 3},
			},
			want: PacketInfoList{
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           2,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 200,
							RevisionHeight: 200,
						},
						TimeoutTimestamp: 200,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
				&PacketInfo{
					Packet: chantypes.Packet{
						Sequence:           3,
						SourcePort:         "account-sync",
						SourceChannel:      "channel-0",
						DestinationPort:    "account-sync",
						DestinationChannel: "channel-0",
						Data:               []byte{},
						TimeoutHeight: types.Height{
							RevisionNumber: 300,
							RevisionHeight: 300,
						},
						TimeoutTimestamp: 300,
					},
					Acknowledgement: []byte{},
					EventHeight:     clienttypes.Height{},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.ps.Filter(tt.args.seqs); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PacketInfoList.Filter() = %v, want %v", got, tt.want)
			}
		})
	}
}

//go:generate mockgen -source=$GOFILE -package=core -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package core

import (
	"time"

	"github.com/cosmos/gogoproto/proto"
	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
)

// MsgID represents an identifier of `sdk.Msg` that has been sent to a chain by `Chain::SendMsgs`.
type MsgID interface {
	proto.Message
	Is_MsgID()
}

// MsgResult represents a execution result of `sdk.Msg` that has been sent to a chain by `Chain::SendMsgs`.
type MsgResult interface {
	// BlockHeight returns the height that the message is included.
	BlockHeight() clienttypes.Height

	// Status returns true if the execution of the message is successful.
	// If it fails, this function returns false with the second return value that provides additional information.
	Status() (bool, string)

	// Events returns events emitted by the message.
	Events() []MsgEventLog
}

// MsgEventLog represents an event emitted by `sdk.Msg` that has been sent to a chain by `Chain::SendMsgs`.
type MsgEventLog interface {
	is_MsgEventLog()
}

// isMsgEventLog must be embedded in implementation of the `MsgEventLog` interface.
// It should be noted that the `MsgEventLog` interface cannot be implemented outside this package because `isMsgEventLog` is unexported.
type isMsgEventLog struct{}

func (isMsgEventLog) is_MsgEventLog() {}

var (
	_ MsgEventLog = isMsgEventLog{}
	_ MsgEventLog = (*EventGenerateClientIdentifier)(nil)
	_ MsgEventLog = (*EventGenerateConnectionIdentifier)(nil)
	_ MsgEventLog = (*EventGenerateChannelIdentifier)(nil)
	_ MsgEventLog = (*EventSendPacket)(nil)
	_ MsgEventLog = (*EventRecvPacket)(nil)
	_ MsgEventLog = (*EventWriteAcknowledgement)(nil)
	_ MsgEventLog = (*EventAcknowledgePacket)(nil)
	_ MsgEventLog = (*EventUnknown)(nil)
)

// EventGenerateClientIdentifier is an implementation of `MsgEventLog` that represents the client id generated by `createClient` operation.
type EventGenerateClientIdentifier struct {
	isMsgEventLog

	ID string
}

// EventGenerateConnectionIdentifier is an implementation of `MsgEventLog` that represents the connection id generated by `connOpenInit` or `connOpenTry` operation.
type EventGenerateConnectionIdentifier struct {
	isMsgEventLog

	ID string
}

// EventGenerateChannelIdentifier is an implementation of `MsgEventLog` that represents the channel id generated by `chanOpenInit` or `chanOpenTry` operation.
type EventGenerateChannelIdentifier struct {
	isMsgEventLog

	ID string
}

// EventSendPacket is an implementation of `MsgEventLog` that represents the information of a `sendPacket` operation.
type EventSendPacket struct {
	isMsgEventLog

	Sequence         uint64
	SrcPort          string
	SrcChannel       string
	TimeoutHeight    clienttypes.Height
	TimeoutTimestamp time.Time
	Data             []byte
}

// EventRecvPacket is an implementation of `MsgEventLog` that represents the information of a `recvPacket` operation.
type EventRecvPacket struct {
	isMsgEventLog

	Sequence         uint64
	DstPort          string
	DstChannel       string
	TimeoutHeight    clienttypes.Height
	TimeoutTimestamp time.Time
	Data             []byte
}

// EventWriteAcknowledgement is an implementation of `MsgEventLog` that represents the information of a `writeAcknowledgement` operation.
type EventWriteAcknowledgement struct {
	isMsgEventLog

	Sequence        uint64
	DstPort         string
	DstChannel      string
	Acknowledgement []byte
}

// EventAcknowledgePacket is an implementation of `MsgEventLog` that represents the information of a `acknowledgePacket` operation.
type EventAcknowledgePacket struct {
	isMsgEventLog

	Sequence         uint64
	SrcPort          string
	SrcChannel       string
	TimeoutHeight    clienttypes.Height
	TimeoutTimestamp time.Time
}

// EventUnknown is an implementation of `MsgEventLog` that represents another event.
type EventUnknown struct {
	isMsgEventLog

	Value any
}

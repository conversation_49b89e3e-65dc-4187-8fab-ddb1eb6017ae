//go:generate mockgen -source=$GOFILE -package=mock_core -destination=./mock_core/mock_$GOFILE -self_package=github.com/decurret-lab/dcbg-dcjpy-relayer/core
//go:generate gotests -w -all $GOFILE
package core

import (
	"testing"
	"time"

	sdk "github.com/cosmos/cosmos-sdk/types"
	"github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	chantypes "github.com/cosmos/ibc-go/v7/modules/core/04-channel/types"
	host "github.com/cosmos/ibc-go/v7/modules/core/24-host"
	"go.uber.org/mock/gomock"
)

// TODO 一部、ethereum/tx.go の SendMsgs が参照できないため除外。
func TestSendTransferMsg(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockDstChain := NewMockChain(ctrl)
	mockSrcProver := NewMockProver(ctrl)
	mockDstProver := NewMockProver(ctrl)
	mockSyncHeaders := NewMockSyncHeaders(ctrl)
	mockSrcQueryContext := NewMockQueryContext(ctrl)
	mockDstQueryContext := NewMockQueryContext(ctrl)

	mockCodec := MakeCodec()

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	srcProvableChain := &ProvableChain{
		Chain:  mockSrcChain,
		Prover: mockSrcProver,
	}

	dstProvableChain := &ProvableChain{
		Chain:  mockDstChain,
		Prover: mockDstProver,
	}

	wantHeight := clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}

	packetInfoList := PacketInfoList{
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{1},
			EventHeight:     clienttypes.Height{},
		},
	}

	packetCommitmentPath := host.PacketCommitmentPath(
		packetInfoList[0].SourcePort,
		packetInfoList[0].SourceChannel,
		packetInfoList[0].Sequence,
	)

	packetAcknowledgementPath := host.PacketAcknowledgementPath(
		packetInfoList[0].DestinationPort,
		packetInfoList[0].DestinationChannel,
		packetInfoList[0].Sequence,
	)

	srcCommitment := chantypes.CommitPacket(
		mockCodec,
		packetInfoList[0],
	)
	dstCommitment := chantypes.CommitAcknowledgement(
		packetInfoList[0].Acknowledgement,
	)

	proof := []byte{1}

	type args struct {
		src            *ProvableChain
		dst            *ProvableChain
		amount         sdk.Coin
		dstAddr        string
		toHeightOffset uint64
		toTimeOffset   time.Duration
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		wantErr     bool
	}{
		{
			name: "toHeightOffset > 0 && toTimeOffset",
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SendMsgs(gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SendMsgs(gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, srcCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockSrcProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(false, nil).AnyTimes()

				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockDstProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(true, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(srcProvableChain, dstProvableChain).Return(nil, nil).AnyTimes()

			},
			args: args{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				amount: sdk.Coin{
					Denom:  "",
					Amount: sdk.Int{},
				},
				dstAddr:        "",
				toHeightOffset: 10,
				toTimeOffset:   10,
			},
			wantErr: true,
		},
		//{
		//	name: "toHeightOffset > 0",
		//	prepareMock: func() {
		//		mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
		//		mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
		//		mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
		//		mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
		//		mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
		//		mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
		//		mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockSrcChain.EXPECT().SendMsgs(gomock.Any()).Return(nil, nil).AnyTimes()
		//
		//		mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
		//		mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
		//		mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
		//		mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
		//		mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
		//		mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockDstChain.EXPECT().SendMsgs(gomock.Any()).Return(nil, nil).AnyTimes()
		//
		//		mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, srcCommitment).Return(proof, wantHeight, nil).AnyTimes()
		//		mockSrcProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(false, nil).AnyTimes()
		//
		//		mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()
		//		mockDstProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(true, nil).AnyTimes()
		//
		//		mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
		//		mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
		//		mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
		//		mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
		//
		//		mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
		//		mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
		//		mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
		//		mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
		//		mockSyncHeaders.EXPECT().SetupHeadersForUpdate(srcProvableChain, dstProvableChain).Return(nil, nil).AnyTimes()
		//
		//	},
		//	args: args{
		//		src: &ProvableChain{
		//			Chain:  mockSrcChain,
		//			Prover: mockSrcProver,
		//		},
		//		dst: &ProvableChain{
		//			Chain:  mockDstChain,
		//			Prover: mockDstProver,
		//		},
		//		amount: sdk.Coin{
		//			Denom:  "",
		//			Amount: sdk.Int{},
		//		},
		//		dstAddr:        "",
		//		toHeightOffset: 10,
		//		toTimeOffset:   0,
		//	},
		//	wantErr: false,
		//},
		//{
		//	name: "toTimeOffset > 0",
		//	prepareMock: func() {
		//		mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
		//		mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
		//		mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
		//		mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
		//		mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
		//		mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
		//		mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockSrcChain.EXPECT().SendMsgs(gomock.Any()).Return(nil, nil).AnyTimes()
		//
		//		mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
		//		mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
		//		mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
		//		mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
		//		mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
		//		mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockDstChain.EXPECT().SendMsgs(gomock.Any()).Return(nil, nil).AnyTimes()
		//
		//		mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, srcCommitment).Return(proof, wantHeight, nil).AnyTimes()
		//		mockSrcProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(false, nil).AnyTimes()
		//
		//		mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()
		//		mockDstProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(true, nil).AnyTimes()
		//
		//		mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
		//		mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
		//		mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
		//		mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
		//
		//		mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
		//		mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
		//		mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
		//		mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
		//		mockSyncHeaders.EXPECT().SetupHeadersForUpdate(srcProvableChain, dstProvableChain).Return(nil, nil).AnyTimes()
		//
		//	},
		//	args: args{
		//		src: &ProvableChain{
		//			Chain:  mockSrcChain,
		//			Prover: mockSrcProver,
		//		},
		//		dst: &ProvableChain{
		//			Chain:  mockDstChain,
		//			Prover: mockDstProver,
		//		},
		//		amount: sdk.Coin{
		//			Denom:  "",
		//			Amount: sdk.Int{},
		//		},
		//		dstAddr:        "",
		//		toHeightOffset: 0,
		//		toTimeOffset:   10,
		//	},
		//	wantErr: false,
		//},
		//{
		//	name: "toTimeOffset > 0",
		//	prepareMock: func() {
		//		mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
		//		mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
		//		mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
		//		mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
		//		mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
		//		mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
		//		mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockSrcChain.EXPECT().SendMsgs(gomock.Any()).Return(nil, nil).AnyTimes()
		//
		//		mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
		//		mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
		//		mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
		//		mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
		//		mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
		//		mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockDstChain.EXPECT().SendMsgs(gomock.Any()).Return(nil, nil).AnyTimes()
		//
		//		mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, srcCommitment).Return(proof, wantHeight, nil).AnyTimes()
		//		mockSrcProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(false, nil).AnyTimes()
		//
		//		mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()
		//		mockDstProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(true, nil).AnyTimes()
		//
		//		mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
		//		mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
		//		mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
		//		mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
		//
		//		mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
		//		mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
		//		mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
		//		mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
		//		mockSyncHeaders.EXPECT().SetupHeadersForUpdate(srcProvableChain, dstProvableChain).Return(nil, nil).AnyTimes()
		//
		//	},
		//	args: args{
		//		src: &ProvableChain{
		//			Chain:  mockSrcChain,
		//			Prover: mockSrcProver,
		//		},
		//		dst: &ProvableChain{
		//			Chain:  mockDstChain,
		//			Prover: mockDstProver,
		//		},
		//		amount: sdk.Coin{
		//			Denom:  "",
		//			Amount: sdk.Int{},
		//		},
		//		dstAddr:        "",
		//		toHeightOffset: 0,
		//		toTimeOffset:   0,
		//	},
		//	wantErr: false,
		//},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			if err := SendTransferMsg(tt.args.src, tt.args.dst, tt.args.amount, tt.args.dstAddr, tt.args.toHeightOffset, tt.args.toTimeOffset); (err != nil) != tt.wantErr {
				t.Errorf("SendTransferMsg() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

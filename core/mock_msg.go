// Code generated by MockGen. DO NOT EDIT.
// Source: msg.go
//
// Generated by this command:
//
//	mockgen -source=msg.go -package=core -destination=./mock_msg.go
//

// Package core is a generated GoMock package.
package core

import (
	reflect "reflect"

	types "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	gomock "go.uber.org/mock/gomock"
)

// MockMsgID is a mock of MsgID interface.
type MockMsgID struct {
	ctrl     *gomock.Controller
	recorder *MockMsgIDMockRecorder
}

// MockMsgIDMockRecorder is the mock recorder for MockMsgID.
type MockMsgIDMockRecorder struct {
	mock *MockMsgID
}

// NewMockMsgID creates a new mock instance.
func NewMockMsgID(ctrl *gomock.Controller) *MockMsgID {
	mock := &MockMsgID{ctrl: ctrl}
	mock.recorder = &MockMsgIDMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMsgID) EXPECT() *MockMsgIDMockRecorder {
	return m.recorder
}

// Is_MsgID mocks base method.
func (m *MockMsgID) Is_MsgID() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Is_MsgID")
}

// Is_MsgID indicates an expected call of Is_MsgID.
func (mr *MockMsgIDMockRecorder) Is_MsgID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Is_MsgID", reflect.TypeOf((*MockMsgID)(nil).Is_MsgID))
}

// ProtoMessage mocks base method.
func (m *MockMsgID) ProtoMessage() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ProtoMessage")
}

// ProtoMessage indicates an expected call of ProtoMessage.
func (mr *MockMsgIDMockRecorder) ProtoMessage() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProtoMessage", reflect.TypeOf((*MockMsgID)(nil).ProtoMessage))
}

// Reset mocks base method.
func (m *MockMsgID) Reset() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Reset")
}

// Reset indicates an expected call of Reset.
func (mr *MockMsgIDMockRecorder) Reset() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reset", reflect.TypeOf((*MockMsgID)(nil).Reset))
}

// String mocks base method.
func (m *MockMsgID) String() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "String")
	ret0, _ := ret[0].(string)
	return ret0
}

// String indicates an expected call of String.
func (mr *MockMsgIDMockRecorder) String() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "String", reflect.TypeOf((*MockMsgID)(nil).String))
}

// MockMsgResult is a mock of MsgResult interface.
type MockMsgResult struct {
	ctrl     *gomock.Controller
	recorder *MockMsgResultMockRecorder
}

// MockMsgResultMockRecorder is the mock recorder for MockMsgResult.
type MockMsgResultMockRecorder struct {
	mock *MockMsgResult
}

// NewMockMsgResult creates a new mock instance.
func NewMockMsgResult(ctrl *gomock.Controller) *MockMsgResult {
	mock := &MockMsgResult{ctrl: ctrl}
	mock.recorder = &MockMsgResultMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMsgResult) EXPECT() *MockMsgResultMockRecorder {
	return m.recorder
}

// BlockHeight mocks base method.
func (m *MockMsgResult) BlockHeight() types.Height {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BlockHeight")
	ret0, _ := ret[0].(types.Height)
	return ret0
}

// BlockHeight indicates an expected call of BlockHeight.
func (mr *MockMsgResultMockRecorder) BlockHeight() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BlockHeight", reflect.TypeOf((*MockMsgResult)(nil).BlockHeight))
}

// Events mocks base method.
func (m *MockMsgResult) Events() []MsgEventLog {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Events")
	ret0, _ := ret[0].([]MsgEventLog)
	return ret0
}

// Events indicates an expected call of Events.
func (mr *MockMsgResultMockRecorder) Events() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Events", reflect.TypeOf((*MockMsgResult)(nil).Events))
}

// Status mocks base method.
func (m *MockMsgResult) Status() (bool, string) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Status")
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(string)
	return ret0, ret1
}

// Status indicates an expected call of Status.
func (mr *MockMsgResultMockRecorder) Status() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Status", reflect.TypeOf((*MockMsgResult)(nil).Status))
}

// MockMsgEventLog is a mock of MsgEventLog interface.
type MockMsgEventLog struct {
	ctrl     *gomock.Controller
	recorder *MockMsgEventLogMockRecorder
}

// MockMsgEventLogMockRecorder is the mock recorder for MockMsgEventLog.
type MockMsgEventLogMockRecorder struct {
	mock *MockMsgEventLog
}

// NewMockMsgEventLog creates a new mock instance.
func NewMockMsgEventLog(ctrl *gomock.Controller) *MockMsgEventLog {
	mock := &MockMsgEventLog{ctrl: ctrl}
	mock.recorder = &MockMsgEventLogMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMsgEventLog) EXPECT() *MockMsgEventLogMockRecorder {
	return m.recorder
}

// is_MsgEventLog mocks base method.
func (m *MockMsgEventLog) is_MsgEventLog() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "is_MsgEventLog")
}

// is_MsgEventLog indicates an expected call of is_MsgEventLog.
func (mr *MockMsgEventLogMockRecorder) is_MsgEventLog() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "is_MsgEventLog", reflect.TypeOf((*MockMsgEventLog)(nil).is_MsgEventLog))
}

//go:generate mockgen -source=$GOFILE -package=core -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package core

import (
	"reflect"
	"testing"

	sdk "github.com/cosmos/cosmos-sdk/types"
	transfertypes "github.com/cosmos/ibc-go/v7/modules/apps/transfer/types"
	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	conntypes "github.com/cosmos/ibc-go/v7/modules/core/03-connection/types"
	chantypes "github.com/cosmos/ibc-go/v7/modules/core/04-channel/types"
	commitmenttypes "github.com/cosmos/ibc-go/v7/modules/core/23-commitment/types"
	"go.uber.org/mock/gomock"
)

func TestOrderFromString(t *testing.T) {

	type args struct {
		order string
	}
	tests := []struct {
		name string
		args args
		want chantypes.Order
	}{
		{
			name: "should return chantypes.UNORDERED",
			args: args{
				order: "UNORDERED",
			},
			want: chantypes.UNORDERED,
		},
		{
			name: "should return chantypes.ORDERED",
			args: args{
				order: "ORDERED",
			},
			want: chantypes.ORDERED,
		},
		{
			name: "should return chantypes.NONE",
			args: args{
				order: "",
			},
			want: chantypes.NONE,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := OrderFromString(tt.args.order); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OrderFromString() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPathEnd_GetOrder(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	//order := "unordered"
	version := "account-sync-0"

	peUnordered := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        "unordered",
		Version:      version,
	}

	peOrdered := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        "ordered",
		Version:      version,
	}

	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	tests := []struct {
		name   string
		fields fields
		want   chantypes.Order
	}{
		{
			name: "should return unordered string.",
			fields: fields{
				ChainID:      srcChainID,
				ClientID:     clientID,
				ConnectionID: connectionID,
				ChannelID:    channelID,
				PortID:       portID,
				Order:        "unordered",
				Version:      version,
			},
			want: peUnordered.GetOrder(),
		},
		{
			name: "should return ordered string.",
			fields: fields{
				ChainID:      dstChainID,
				ClientID:     clientID,
				ConnectionID: connectionID,
				ChannelID:    channelID,
				PortID:       portID,
				Order:        "ordered",
				Version:      version,
			},
			want: peOrdered.GetOrder(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if got := pe.GetOrder(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PathEnd.GetOrder() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPathEnd_UpdateClient(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockHeader := NewMockHeader(ctrl)

	// set expectations
	srcChainID := "********"
	srcClientID := "hb-ibft2-0"
	mockHeader.EXPECT().ValidateBasic().Return(nil).AnyTimes()

	wantMsg, _ := clienttypes.NewMsgUpdateClient(
		srcClientID,
		mockHeader,
		"",
	)

	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	type args struct {
		dstHeader Header
		signer    sdk.AccAddress
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   sdk.Msg
	}{
		{
			name: "should creates an sdk.Msg to update the client on src with data pulled from dst.",
			fields: fields{
				ChainID:      srcChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-0",
			},
			args: args{
				dstHeader: mockHeader,
				signer:    []byte{},
			},
			want: wantMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if got := pe.UpdateClient(tt.args.dstHeader, tt.args.signer); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PathEnd.UpdateClient() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPathEnd_UpdateClients(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockHeader := NewMockHeader(ctrl)

	// set expectations
	srcChainID := "********"
	srcClientID := "hb-ibft2-0"
	mockHeader.EXPECT().ValidateBasic().Return(nil).AnyTimes()

	wantMsg, _ := clienttypes.NewMsgUpdateClient(
		srcClientID,
		mockHeader,
		"",
	)

	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	type args struct {
		dstHeaders []Header
		signer     sdk.AccAddress
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []sdk.Msg
	}{
		{
			name: "should creates an sdk.Msg to update the client on src with data pulled from dst.",
			fields: fields{
				ChainID:      srcChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-0",
			},
			args: args{
				dstHeaders: []Header{
					mockHeader,
				},
				signer: []byte{},
			},
			want: []sdk.Msg{
				wantMsg,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if got := pe.UpdateClients(tt.args.dstHeaders, tt.args.signer); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PathEnd.UpdateClients() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPathEnd_ConnInit(t *testing.T) {

	// set expectations
	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	type args struct {
		dst    *PathEnd
		signer sdk.AccAddress
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   sdk.Msg
	}{
		{
			name: "should creates a MsgConnectionOpenInit.",
			fields: fields{
				ChainID:      srcChainID,
				ClientID:     clientID,
				ConnectionID: connectionID,
				ChannelID:    channelID,
				PortID:       portID,
				Order:        order,
				Version:      version,
			},
			args: args{
				dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     clientID,
					ConnectionID: connectionID,
					ChannelID:    channelID,
					PortID:       portID,
					Order:        order,
					Version:      version,
				},
				signer: []byte{},
			},
			want: conntypes.NewMsgConnectionOpenInit(
				clientID,
				clientID,
				commitmenttypes.NewMerklePrefix([]byte("ibc")),
				nil,
				0,
				"",
			),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if got := pe.ConnInit(tt.args.dst, tt.args.signer); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PathEnd.ConnInit() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TODO UnpackClientState 時の ClientState が protobuf から 取れないためテスト実施不可
//func TestPathEnd_ConnTry(t *testing.T) {
//
//	// set expectations
//	srcChainID := "********"
//	dstChainID := "********"
//	clientID := "hb-ibft2-0"
//	connectionID := "connection-0"
//	channelID := "channel-0"
//	portID := "account-sync"
//	order := "unordered"
//	version := "account-sync-0"
//
//	typeUrl := "/relayer.chains.ethereum.config.ChainConfig"
//
//	type fields struct {
//		ChainID      string
//		ClientID     string
//		ConnectionID string
//		ChannelID    string
//		PortID       string
//		Order        string
//		Version      string
//	}
//	type args struct {
//		dst                     *PathEnd
//		dstClientState          *clienttypes.QueryClientStateResponse
//		dstConnState            *conntypes.QueryConnectionResponse
//		dstConsState            *clienttypes.QueryConsensusStateResponse
//		hostConsensusStateProof []byte
//		signer                  sdk.AccAddress
//	}
//	tests := []struct {
//		name   string
//		fields fields
//		args   args
//		want   sdk.Msg
//	}{
//		// TODO: Add test cases.
//		{
//			name: "",
//			fields: fields{
//				ChainID:      srcChainID,
//				ClientID:     clientID,
//				ConnectionID: connectionID,
//				ChannelID:    channelID,
//				PortID:       portID,
//				Order:        order,
//				Version:      version,
//			},
//			args: args{
//				dst: &PathEnd{
//					ChainID:      dstChainID,
//					ClientID:     clientID,
//					ConnectionID: connectionID,
//					ChannelID:    channelID,
//					PortID:       portID,
//					Order:        order,
//					Version:      version,
//				},
//				dstClientState: &clienttypes.QueryClientStateResponse{
//					ClientState: &codectypes.Any{
//						TypeUrl:              typeUrl,
//						Value:                []byte{1},
//						XXX_NoUnkeyedLiteral: struct{}{},
//						XXX_unrecognized:     []byte{1},
//						XXX_sizecache:        0,
//					},
//					Proof: []byte{1},
//					ProofHeight: clienttypes.Height{
//						RevisionNumber: 0,
//						RevisionHeight: 0,
//					},
//				},
//				dstConnState: &conntypes.QueryConnectionResponse{
//					Connection: &conntypes.ConnectionEnd{
//						ClientId: clientID,
//						Versions: nil,
//						State:    0,
//						Counterparty: conntypes.Counterparty{
//							ClientId:     clientID,
//							ConnectionId: connectionID,
//							Prefix: commitmenttypes.MerklePrefix{
//								KeyPrefix: []byte("ibc"),
//							},
//						},
//						DelayPeriod: 0,
//					},
//					Proof: []byte{1},
//					ProofHeight: clienttypes.Height{
//						RevisionNumber: 0,
//						RevisionHeight: 0,
//					},
//				},
//				dstConsState: &clienttypes.QueryConsensusStateResponse{
//					ConsensusState: &codectypes.Any{
//						TypeUrl:              "typeUrl",
//						Value:                []byte{1},
//						XXX_NoUnkeyedLiteral: struct{}{},
//						XXX_unrecognized:     []byte{1},
//						XXX_sizecache:        0,
//					},
//					Proof: []byte{1},
//					ProofHeight: clienttypes.Height{
//						RevisionNumber: 0,
//						RevisionHeight: 0,
//					},
//				},
//				hostConsensusStateProof: nil,
//				signer:                  nil,
//			},
//			want: nil,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			pe := &PathEnd{
//				ChainID:      tt.fields.ChainID,
//				ClientID:     tt.fields.ClientID,
//				ConnectionID: tt.fields.ConnectionID,
//				ChannelID:    tt.fields.ChannelID,
//				PortID:       tt.fields.PortID,
//				Order:        tt.fields.Order,
//				Version:      tt.fields.Version,
//			}
//			if got := pe.ConnTry(tt.args.dst, tt.args.dstClientState, tt.args.dstConnState, tt.args.dstConsState, tt.args.hostConsensusStateProof, tt.args.signer); !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("PathEnd.ConnTry() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
//// ClientState が protobuf から 取れないためテスト実施不可
//func TestPathEnd_ConnAck(t *testing.T) {
//
//	srcChainID := "********"
//	dstChainID := "********"
//	clientID := "hb-ibft2-0"
//	connectionID := "connection-0"
//	typeUrl := "/relayer.chains.ethereum.config.ChainConfig"
//
//	type fields struct {
//		ChainID      string
//		ClientID     string
//		ConnectionID string
//		ChannelID    string
//		PortID       string
//		Order        string
//		Version      string
//	}
//	type args struct {
//		dst                     *PathEnd
//		dstClientState          *clienttypes.QueryClientStateResponse
//		dstConnState            *conntypes.QueryConnectionResponse
//		dstConsState            *clienttypes.QueryConsensusStateResponse
//		hostConsensusStateProof []byte
//		signer                  sdk.AccAddress
//	}
//	tests := []struct {
//		name   string
//		fields fields
//		args   args
//		want   sdk.Msg
//	}{
//		// TODO: Add test cases.
//		{
//			name: "",
//			fields: fields{
//				ChainID:      srcChainID,
//				ClientID:     clientID,
//				ConnectionID: connectionID,
//				ChannelID:    "channel-0",
//				PortID:       "account-sync",
//				Order:        "unordered",
//				Version:      "account-sync-0",
//			},
//			args: args{
//				dst: &PathEnd{
//					ChainID:      dstChainID,
//					ClientID:     clientID,
//					ConnectionID: connectionID,
//					ChannelID:    "channel-0",
//					PortID:       "account-sync",
//					Order:        "unordered",
//					Version:      "account-sync-0",
//				},
//				dstClientState: &clienttypes.QueryClientStateResponse{
//					ClientState: &codectypes.Any{
//						TypeUrl:              typeUrl,
//						Value:                []byte{1},
//						XXX_NoUnkeyedLiteral: struct{}{},
//						XXX_unrecognized:     []byte{1},
//						XXX_sizecache:        100,
//					},
//					Proof: []byte{1},
//					ProofHeight: clienttypes.Height{
//						RevisionNumber: 100,
//						RevisionHeight: 100,
//					},
//				},
//				dstConnState: &conntypes.QueryConnectionResponse{
//					Connection: &conntypes.ConnectionEnd{
//						ClientId: clientID,
//						Versions: nil,
//						State:    0,
//						Counterparty: conntypes.Counterparty{
//							ClientId:     clientID,
//							ConnectionId: connectionID,
//							Prefix: commitmenttypes.MerklePrefix{
//								KeyPrefix: []byte("ibc"),
//							},
//						},
//						DelayPeriod: 0,
//					},
//					Proof: []byte{1},
//					ProofHeight: clienttypes.Height{
//						RevisionNumber: 100,
//						RevisionHeight: 100,
//					},
//				},
//				dstConsState: &clienttypes.QueryConsensusStateResponse{
//					ConsensusState: &codectypes.Any{
//						TypeUrl:              typeUrl,
//						Value:                nil,
//						XXX_NoUnkeyedLiteral: struct{}{},
//						XXX_unrecognized:     []byte{1},
//						XXX_sizecache:        0,
//					},
//					Proof: []byte{1},
//					ProofHeight: clienttypes.Height{
//						RevisionNumber: 100,
//						RevisionHeight: 100,
//					},
//				},
//				hostConsensusStateProof: []byte{1},
//				signer:                  []byte{1},
//			},
//			want: nil,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			pe := &PathEnd{
//				ChainID:      tt.fields.ChainID,
//				ClientID:     tt.fields.ClientID,
//				ConnectionID: tt.fields.ConnectionID,
//				ChannelID:    tt.fields.ChannelID,
//				PortID:       tt.fields.PortID,
//				Order:        tt.fields.Order,
//				Version:      tt.fields.Version,
//			}
//			if got := pe.ConnAck(tt.args.dst, tt.args.dstClientState, tt.args.dstConnState, tt.args.dstConsState, tt.args.hostConsensusStateProof, tt.args.signer); !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("PathEnd.ConnAck() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func TestPathEnd_ConnConfirm(t *testing.T) {

	// set expectations
	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	wantMsg := conntypes.NewMsgConnectionOpenConfirm(
		connectionID,
		[]byte{1},
		clienttypes.Height{
			RevisionNumber: 100,
			RevisionHeight: 100,
		},
		string("cosmos1qyfkm2y3"),
	)

	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	type args struct {
		dstConnState *conntypes.QueryConnectionResponse
		signer       sdk.AccAddress
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   sdk.Msg
	}{
		{
			name: "should creates a MsgConnectionOpenAck.",
			fields: fields{
				ChainID:      srcChainID,
				ClientID:     clientID,
				ConnectionID: connectionID,
				ChannelID:    channelID,
				PortID:       portID,
				Order:        order,
				Version:      version,
			},
			args: args{
				dstConnState: &conntypes.QueryConnectionResponse{
					Connection: &conntypes.ConnectionEnd{
						ClientId: dstChainID,
						Versions: nil,
						State:    0,
						Counterparty: conntypes.Counterparty{
							ClientId:     clientID,
							ConnectionId: connectionID,
							Prefix: commitmenttypes.MerklePrefix{
								KeyPrefix: []byte("ibc"),
							},
						},
						DelayPeriod: 0,
					},
					Proof: []byte{1},
					ProofHeight: clienttypes.Height{
						RevisionNumber: 100,
						RevisionHeight: 100,
					},
				},
				signer: []byte{1},
			},
			want: wantMsg,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if got := pe.ConnConfirm(tt.args.dstConnState, tt.args.signer); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PathEnd.ConnConfirm() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPathEnd_ChanInit(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	pe := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	wantChanInit := chantypes.NewMsgChannelOpenInit(
		pe.PortID,
		pe.Version,
		pe.GetOrder(),
		[]string{pe.ConnectionID},
		portID,
		string("cosmos1qyfkm2y3"),
	)

	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	type args struct {
		dst    *PathEnd
		signer sdk.AccAddress
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   sdk.Msg
	}{
		{
			name: "",
			fields: fields{
				ChainID:      srcChainID,
				ClientID:     clientID,
				ConnectionID: connectionID,
				ChannelID:    "channel-0",
				PortID:       portID,
				Order:        order,
				Version:      version,
			},
			args: args{
				dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     clientID,
					ConnectionID: connectionID,
					ChannelID:    "channel-0",
					PortID:       portID,
					Order:        order,
					Version:      version,
				},
				signer: []byte{1},
			},
			want: wantChanInit,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if got := pe.ChanInit(tt.args.dst, tt.args.signer); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PathEnd.ChanInit() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPathEnd_ChanTry(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"
	signer := []byte{1}

	wantPathEnd := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	wantChanState := &chantypes.QueryChannelResponse{
		Channel: &chantypes.Channel{
			State:    0,
			Ordering: 0,
			Counterparty: chantypes.Counterparty{
				PortId:    portID,
				ChannelId: channelID,
			},
			ConnectionHops: nil,
			Version:        version,
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 0,
		},
	}

	wantChanTry := chantypes.NewMsgChannelOpenTry(
		wantPathEnd.PortID,
		wantPathEnd.Version,
		wantChanState.Channel.Ordering,
		[]string{wantPathEnd.ConnectionID},
		wantPathEnd.PortID,
		wantPathEnd.ChannelID,
		wantChanState.Channel.Version,
		wantChanState.Proof,
		wantChanState.ProofHeight,
		string("cosmos1qyfkm2y3"),
	)

	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	type args struct {
		dst          *PathEnd
		dstChanState *chantypes.QueryChannelResponse
		signer       sdk.AccAddress
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   sdk.Msg
	}{
		{
			name: "should creates a MsgChannelOpenTry.",
			fields: fields{
				ChainID:      srcChainID,
				ClientID:     clientID,
				ConnectionID: connectionID,
				ChannelID:    connectionID,
				PortID:       portID,
				Order:        order,
				Version:      version,
			},
			args: args{
				dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     clientID,
					ConnectionID: connectionID,
					ChannelID:    channelID,
					PortID:       portID,
					Order:        order,
					Version:      version,
				},
				dstChanState: &chantypes.QueryChannelResponse{
					Channel: &chantypes.Channel{
						State:    0,
						Ordering: 0,
						Counterparty: chantypes.Counterparty{
							PortId:    portID,
							ChannelId: channelID,
						},
						ConnectionHops: nil,
						Version:        version,
					},
					Proof: []byte{1},
					ProofHeight: clienttypes.Height{
						RevisionNumber: 0,
						RevisionHeight: 0,
					},
				},
				signer: signer,
			},
			want: wantChanTry,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if got := pe.ChanTry(tt.args.dst, tt.args.dstChanState, tt.args.signer); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PathEnd.ChanTry() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPathEnd_ChanAck(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	wantPathEnd := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	dstPathEnd := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	wantChanState := &chantypes.QueryChannelResponse{
		Channel: &chantypes.Channel{
			State:    0,
			Ordering: 0,
			Counterparty: chantypes.Counterparty{
				PortId:    portID,
				ChannelId: channelID,
			},
			ConnectionHops: nil,
			Version:        version,
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 0,
		},
	}

	wantChanAck := chantypes.NewMsgChannelOpenAck(
		wantPathEnd.PortID,
		wantPathEnd.ChannelID,
		dstPathEnd.ChannelID,
		wantChanState.Channel.Version,
		wantChanState.Proof,
		wantChanState.ProofHeight,
		string("cosmos1qyfkm2y3"),
	)

	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	type args struct {
		dst          *PathEnd
		dstChanState *chantypes.QueryChannelResponse
		signer       sdk.AccAddress
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   sdk.Msg
	}{
		{
			name: "should creates a MsgConnectionOpenAck.",
			fields: fields{
				ChainID:      srcChainID,
				ClientID:     clientID,
				ConnectionID: connectionID,
				ChannelID:    channelID,
				PortID:       portID,
				Order:        order,
				Version:      version,
			},
			args: args{
				dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     clientID,
					ConnectionID: connectionID,
					ChannelID:    channelID,
					PortID:       portID,
					Order:        order,
					Version:      version,
				},
				dstChanState: &chantypes.QueryChannelResponse{
					Channel: &chantypes.Channel{
						State:    0,
						Ordering: 0,
						Counterparty: chantypes.Counterparty{
							PortId:    portID,
							ChannelId: channelID,
						},
						ConnectionHops: nil,
						Version:        version,
					},
					Proof: []byte{1},
					ProofHeight: clienttypes.Height{
						RevisionNumber: 0,
						RevisionHeight: 0,
					},
				},
				signer: []byte{1},
			},
			want: wantChanAck,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if got := pe.ChanAck(tt.args.dst, tt.args.dstChanState, tt.args.signer); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PathEnd.ChanAck() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPathEnd_ChanConfirm(t *testing.T) {

	srcChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	wantPathEnd := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	wantChanState := &chantypes.QueryChannelResponse{
		Channel: &chantypes.Channel{
			State:    0,
			Ordering: 0,
			Counterparty: chantypes.Counterparty{
				PortId:    portID,
				ChannelId: channelID,
			},
			ConnectionHops: nil,
			Version:        version,
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 0,
		},
	}

	wantChanConfirm := chantypes.NewMsgChannelOpenConfirm(
		wantPathEnd.PortID,
		wantPathEnd.ChannelID,
		wantChanState.Proof,
		wantChanState.ProofHeight,
		string("cosmos1qyfkm2y3"),
	)

	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	type args struct {
		dstChanState *chantypes.QueryChannelResponse
		signer       sdk.AccAddress
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   sdk.Msg
	}{
		{
			name: "should creates a MsgChannelOpenConfirm.",
			fields: fields{
				ChainID:      srcChainID,
				ClientID:     clientID,
				ConnectionID: connectionID,
				ChannelID:    channelID,
				PortID:       portID,
				Order:        order,
				Version:      version,
			},
			args: args{
				dstChanState: &chantypes.QueryChannelResponse{
					Channel: &chantypes.Channel{
						State:    0,
						Ordering: 0,
						Counterparty: chantypes.Counterparty{
							PortId:    portID,
							ChannelId: channelID,
						},
						ConnectionHops: nil,
						Version:        version,
					},
					Proof: []byte{1},
					ProofHeight: clienttypes.Height{
						RevisionNumber: 0,
						RevisionHeight: 0,
					},
				},
				signer: []byte{1},
			},
			want: wantChanConfirm,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if got := pe.ChanConfirm(tt.args.dstChanState, tt.args.signer); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PathEnd.ChanConfirm() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPathEnd_ChanCloseInit(t *testing.T) {

	srcChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	wantPathEnd := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	wantChanAck := chantypes.NewMsgChannelCloseInit(
		wantPathEnd.PortID,
		wantPathEnd.ChannelID,
		string("cosmos1qyfkm2y3"),
	)

	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	type args struct {
		signer sdk.AccAddress
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   sdk.Msg
	}{
		{
			name: "should creates a MsgChannelCloseInit.",
			fields: fields{
				ChainID:      srcChainID,
				ClientID:     clientID,
				ConnectionID: connectionID,
				ChannelID:    channelID,
				PortID:       portID,
				Order:        order,
				Version:      version,
			},
			args: args{
				signer: []byte{1},
			},
			want: wantChanAck,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if got := pe.ChanCloseInit(tt.args.signer); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PathEnd.ChanCloseInit() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPathEnd_ChanCloseConfirm(t *testing.T) {

	srcChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	wantPathEnd := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	wantChanState := &chantypes.QueryChannelResponse{
		Channel: &chantypes.Channel{
			State:    0,
			Ordering: 0,
			Counterparty: chantypes.Counterparty{
				PortId:    portID,
				ChannelId: channelID,
			},
			ConnectionHops: nil,
			Version:        version,
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 0,
		},
	}

	wantChanCloseConfirm := chantypes.NewMsgChannelCloseConfirm(
		wantPathEnd.PortID,
		wantPathEnd.ChannelID,
		wantChanState.Proof,
		wantChanState.ProofHeight,
		string("cosmos1qyfkm2y3"),
	)

	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	type args struct {
		dstChanState *chantypes.QueryChannelResponse
		signer       sdk.AccAddress
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   sdk.Msg
	}{
		{
			name: "should creates a MsgChannelCloseConfirm.",
			fields: fields{
				ChainID:      srcChainID,
				ClientID:     clientID,
				ConnectionID: connectionID,
				ChannelID:    channelID,
				PortID:       portID,
				Order:        order,
				Version:      version,
			},
			args: args{
				dstChanState: &chantypes.QueryChannelResponse{
					Channel: &chantypes.Channel{
						State:    0,
						Ordering: 0,
						Counterparty: chantypes.Counterparty{
							PortId:    portID,
							ChannelId: channelID,
						},
						ConnectionHops: nil,
						Version:        version,
					},
					Proof: []byte{1},
					ProofHeight: clienttypes.Height{
						RevisionNumber: 0,
						RevisionHeight: 0,
					},
				},
				signer: []byte{1},
			},
			want: wantChanCloseConfirm,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if got := pe.ChanCloseConfirm(tt.args.dstChanState, tt.args.signer); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PathEnd.ChanCloseConfirm() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPathEnd_MsgTransfer(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"
	amount := sdk.Coin{
		Denom:  "",
		Amount: sdk.NewInt(100),
	}
	signer := &sdk.AccAddress{}
	dstAddr := ""
	wantPathEnd := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	type args struct {
		dst              *PathEnd
		amount           sdk.Coin
		dstAddr          string
		signer           sdk.AccAddress
		timeoutHeight    uint64
		timeoutTimestamp uint64
		memo             string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   sdk.Msg
	}{
		{
			name: "should creates a new transfer message.",
			fields: fields{
				ChainID:      srcChainID,
				ClientID:     clientID,
				ConnectionID: connectionID,
				ChannelID:    channelID,
				PortID:       portID,
				Order:        order,
				Version:      version,
			},
			args: args{
				dst: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     clientID,
					ConnectionID: connectionID,
					ChannelID:    channelID,
					PortID:       portID,
					Order:        order,
					Version:      version,
				},
				amount: sdk.Coin{
					Denom:  "",
					Amount: sdk.NewInt(100),
				},
				dstAddr:          "",
				signer:           nil,
				timeoutHeight:    100,
				timeoutTimestamp: 0,
				memo:             "memo",
			},
			want: transfertypes.NewMsgTransfer(
				wantPathEnd.PortID,
				wantPathEnd.ChannelID,
				amount,
				signer.String(),
				dstAddr,
				clienttypes.NewHeight(clienttypes.ParseChainID(dstChainID), 100),
				0,
				"memo",
			),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if got := pe.MsgTransfer(tt.args.dst, tt.args.amount, tt.args.dstAddr, tt.args.signer, tt.args.timeoutHeight, tt.args.timeoutTimestamp, tt.args.memo); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PathEnd.MsgTransfer() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPathEnd_NewPacket(t *testing.T) {

	chainID := "********"
	//dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"
	//signer := []byte{1}

	type fields struct {
		ChainID      string
		ClientID     string
		ConnectionID string
		ChannelID    string
		PortID       string
		Order        string
		Version      string
	}
	type args struct {
		dst           *PathEnd
		sequence      uint64
		packetData    []byte
		timeoutHeight uint64
		timeoutStamp  uint64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   chantypes.Packet
	}{
		{
			name: "should returns a new single packet containing one piece of data from src to dist.",
			fields: fields{
				ChainID:      chainID,
				ClientID:     clientID,
				ConnectionID: connectionID,
				ChannelID:    channelID,
				PortID:       portID,
				Order:        order,
				Version:      version,
			},
			args: args{
				dst: &PathEnd{
					ChainID:      chainID,
					ClientID:     clientID,
					ConnectionID: connectionID,
					ChannelID:    channelID,
					PortID:       portID,
					Order:        order,
					Version:      version,
				},
				sequence:      1,
				packetData:    []byte{1},
				timeoutHeight: 100,
				timeoutStamp:  0,
			},
			want: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: clienttypes.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
		},
		{
			name: "should returns a new single packet containing multiple piece of data from src to dist.",
			fields: fields{
				ChainID:      chainID,
				ClientID:     clientID,
				ConnectionID: connectionID,
				ChannelID:    channelID,
				PortID:       portID,
				Order:        order,
				Version:      version,
			},
			args: args{
				dst: &PathEnd{
					ChainID:      chainID,
					ClientID:     clientID,
					ConnectionID: connectionID,
					ChannelID:    channelID,
					PortID:       portID,
					Order:        order,
					Version:      version,
				},
				sequence:      10,
				packetData:    []byte{10, 11, 12, 13, 14, 15, 16, 17, 18, 19},
				timeoutHeight: 100,
				timeoutStamp:  0,
			},
			want: chantypes.Packet{
				Sequence:           10,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{10, 11, 12, 13, 14, 15, 16, 17, 18, 19},
				TimeoutHeight: clienttypes.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pe := &PathEnd{
				ChainID:      tt.fields.ChainID,
				ClientID:     tt.fields.ClientID,
				ConnectionID: tt.fields.ConnectionID,
				ChannelID:    tt.fields.ChannelID,
				PortID:       tt.fields.PortID,
				Order:        tt.fields.Order,
				Version:      tt.fields.Version,
			}
			if got := pe.NewPacket(tt.args.dst, tt.args.sequence, tt.args.packetData, tt.args.timeoutHeight, tt.args.timeoutStamp); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PathEnd.NewPacket() = %v, want %v", got, tt.want)
			}
		})
	}
}

// Code generated by MockGen. DO NOT EDIT.
// Source: chain.go
//
// Generated by this command:
//
//	mockgen -source=chain.go -package=core -destination=./mock_chain.go
//

// Package core is a generated GoMock package.
package core

import (
	context "context"
	reflect "reflect"
	time "time"

	codec "github.com/cosmos/cosmos-sdk/codec"
	types "github.com/cosmos/cosmos-sdk/types"
	types0 "github.com/cosmos/ibc-go/v7/modules/apps/transfer/types"
	types1 "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	types2 "github.com/cosmos/ibc-go/v7/modules/core/03-connection/types"
	types3 "github.com/cosmos/ibc-go/v7/modules/core/04-channel/types"
	exported "github.com/cosmos/ibc-go/v7/modules/core/exported"
	gomock "go.uber.org/mock/gomock"
)

// MockChain is a mock of Chain interface.
type MockChain struct {
	ctrl     *gomock.Controller
	recorder *MockChainMockRecorder
}

// MockChainMockRecorder is the mock recorder for MockChain.
type MockChainMockRecorder struct {
	mock *MockChain
}

// NewMockChain creates a new mock instance.
func NewMockChain(ctrl *gomock.Controller) *MockChain {
	mock := &MockChain{ctrl: ctrl}
	mock.recorder = &MockChainMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChain) EXPECT() *MockChainMockRecorder {
	return m.recorder
}

// AverageBlockTime mocks base method.
func (m *MockChain) AverageBlockTime() time.Duration {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AverageBlockTime")
	ret0, _ := ret[0].(time.Duration)
	return ret0
}

// AverageBlockTime indicates an expected call of AverageBlockTime.
func (mr *MockChainMockRecorder) AverageBlockTime() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AverageBlockTime", reflect.TypeOf((*MockChain)(nil).AverageBlockTime))
}

// ChainID mocks base method.
func (m *MockChain) ChainID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChainID")
	ret0, _ := ret[0].(string)
	return ret0
}

// ChainID indicates an expected call of ChainID.
func (mr *MockChainMockRecorder) ChainID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChainID", reflect.TypeOf((*MockChain)(nil).ChainID))
}

// Codec mocks base method.
func (m *MockChain) Codec() codec.ProtoCodecMarshaler {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Codec")
	ret0, _ := ret[0].(codec.ProtoCodecMarshaler)
	return ret0
}

// Codec indicates an expected call of Codec.
func (mr *MockChainMockRecorder) Codec() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Codec", reflect.TypeOf((*MockChain)(nil).Codec))
}

// GetAddress mocks base method.
func (m *MockChain) GetAddress() (types.AccAddress, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAddress")
	ret0, _ := ret[0].(types.AccAddress)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAddress indicates an expected call of GetAddress.
func (mr *MockChainMockRecorder) GetAddress() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAddress", reflect.TypeOf((*MockChain)(nil).GetAddress))
}

// GetMsgResult mocks base method.
func (m *MockChain) GetMsgResult(id MsgID) (MsgResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMsgResult", id)
	ret0, _ := ret[0].(MsgResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMsgResult indicates an expected call of GetMsgResult.
func (mr *MockChainMockRecorder) GetMsgResult(id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMsgResult", reflect.TypeOf((*MockChain)(nil).GetMsgResult), id)
}

// Init mocks base method.
func (m *MockChain) Init(homePath string, timeout time.Duration, codec codec.ProtoCodecMarshaler, debug bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Init", homePath, timeout, codec, debug)
	ret0, _ := ret[0].(error)
	return ret0
}

// Init indicates an expected call of Init.
func (mr *MockChainMockRecorder) Init(homePath, timeout, codec, debug any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Init", reflect.TypeOf((*MockChain)(nil).Init), homePath, timeout, codec, debug)
}

// LatestHeight mocks base method.
func (m *MockChain) LatestHeight() (exported.Height, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LatestHeight")
	ret0, _ := ret[0].(exported.Height)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LatestHeight indicates an expected call of LatestHeight.
func (mr *MockChainMockRecorder) LatestHeight() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LatestHeight", reflect.TypeOf((*MockChain)(nil).LatestHeight))
}

// Path mocks base method.
func (m *MockChain) Path() *PathEnd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Path")
	ret0, _ := ret[0].(*PathEnd)
	return ret0
}

// Path indicates an expected call of Path.
func (mr *MockChainMockRecorder) Path() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Path", reflect.TypeOf((*MockChain)(nil).Path))
}

// QueryBalance mocks base method.
func (m *MockChain) QueryBalance(ctx QueryContext, address types.AccAddress) (types.Coins, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryBalance", ctx, address)
	ret0, _ := ret[0].(types.Coins)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryBalance indicates an expected call of QueryBalance.
func (mr *MockChainMockRecorder) QueryBalance(ctx, address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryBalance", reflect.TypeOf((*MockChain)(nil).QueryBalance), ctx, address)
}

// QueryChannel mocks base method.
func (m *MockChain) QueryChannel(ctx QueryContext) (*types3.QueryChannelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryChannel", ctx)
	ret0, _ := ret[0].(*types3.QueryChannelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryChannel indicates an expected call of QueryChannel.
func (mr *MockChainMockRecorder) QueryChannel(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryChannel", reflect.TypeOf((*MockChain)(nil).QueryChannel), ctx)
}

// QueryClientConsensusState mocks base method.
func (m *MockChain) QueryClientConsensusState(ctx QueryContext, dstClientConsHeight exported.Height) (*types1.QueryConsensusStateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryClientConsensusState", ctx, dstClientConsHeight)
	ret0, _ := ret[0].(*types1.QueryConsensusStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryClientConsensusState indicates an expected call of QueryClientConsensusState.
func (mr *MockChainMockRecorder) QueryClientConsensusState(ctx, dstClientConsHeight any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryClientConsensusState", reflect.TypeOf((*MockChain)(nil).QueryClientConsensusState), ctx, dstClientConsHeight)
}

// QueryClientState mocks base method.
func (m *MockChain) QueryClientState(ctx QueryContext) (*types1.QueryClientStateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryClientState", ctx)
	ret0, _ := ret[0].(*types1.QueryClientStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryClientState indicates an expected call of QueryClientState.
func (mr *MockChainMockRecorder) QueryClientState(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryClientState", reflect.TypeOf((*MockChain)(nil).QueryClientState), ctx)
}

// QueryConnection mocks base method.
func (m *MockChain) QueryConnection(ctx QueryContext) (*types2.QueryConnectionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryConnection", ctx)
	ret0, _ := ret[0].(*types2.QueryConnectionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryConnection indicates an expected call of QueryConnection.
func (mr *MockChainMockRecorder) QueryConnection(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryConnection", reflect.TypeOf((*MockChain)(nil).QueryConnection), ctx)
}

// QueryDenomTraces mocks base method.
func (m *MockChain) QueryDenomTraces(ctx QueryContext, offset, limit uint64) (*types0.QueryDenomTracesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryDenomTraces", ctx, offset, limit)
	ret0, _ := ret[0].(*types0.QueryDenomTracesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryDenomTraces indicates an expected call of QueryDenomTraces.
func (mr *MockChainMockRecorder) QueryDenomTraces(ctx, offset, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryDenomTraces", reflect.TypeOf((*MockChain)(nil).QueryDenomTraces), ctx, offset, limit)
}

// QueryUnfinalizedRelayAcknowledgements mocks base method.
func (m *MockChain) QueryUnfinalizedRelayAcknowledgements(ctx QueryContext, counterparty LightClientICS04Querier) (PacketInfoList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnfinalizedRelayAcknowledgements", ctx, counterparty)
	ret0, _ := ret[0].(PacketInfoList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnfinalizedRelayAcknowledgements indicates an expected call of QueryUnfinalizedRelayAcknowledgements.
func (mr *MockChainMockRecorder) QueryUnfinalizedRelayAcknowledgements(ctx, counterparty any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnfinalizedRelayAcknowledgements", reflect.TypeOf((*MockChain)(nil).QueryUnfinalizedRelayAcknowledgements), ctx, counterparty)
}

// QueryUnfinalizedRelayPackets mocks base method.
func (m *MockChain) QueryUnfinalizedRelayPackets(ctx QueryContext, counterparty LightClientICS04Querier) (PacketInfoList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnfinalizedRelayPackets", ctx, counterparty)
	ret0, _ := ret[0].(PacketInfoList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnfinalizedRelayPackets indicates an expected call of QueryUnfinalizedRelayPackets.
func (mr *MockChainMockRecorder) QueryUnfinalizedRelayPackets(ctx, counterparty any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnfinalizedRelayPackets", reflect.TypeOf((*MockChain)(nil).QueryUnfinalizedRelayPackets), ctx, counterparty)
}

// QueryUnreceivedAcknowledgements mocks base method.
func (m *MockChain) QueryUnreceivedAcknowledgements(ctx QueryContext, seqs []uint64) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnreceivedAcknowledgements", ctx, seqs)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnreceivedAcknowledgements indicates an expected call of QueryUnreceivedAcknowledgements.
func (mr *MockChainMockRecorder) QueryUnreceivedAcknowledgements(ctx, seqs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnreceivedAcknowledgements", reflect.TypeOf((*MockChain)(nil).QueryUnreceivedAcknowledgements), ctx, seqs)
}

// QueryUnreceivedPackets mocks base method.
func (m *MockChain) QueryUnreceivedPackets(ctx QueryContext, seqs []uint64) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnreceivedPackets", ctx, seqs)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnreceivedPackets indicates an expected call of QueryUnreceivedPackets.
func (mr *MockChainMockRecorder) QueryUnreceivedPackets(ctx, seqs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnreceivedPackets", reflect.TypeOf((*MockChain)(nil).QueryUnreceivedPackets), ctx, seqs)
}

// RegisterMsgEventListener mocks base method.
func (m *MockChain) RegisterMsgEventListener(arg0 MsgEventListener) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RegisterMsgEventListener", arg0)
}

// RegisterMsgEventListener indicates an expected call of RegisterMsgEventListener.
func (mr *MockChainMockRecorder) RegisterMsgEventListener(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterMsgEventListener", reflect.TypeOf((*MockChain)(nil).RegisterMsgEventListener), arg0)
}

// SendMsgs mocks base method.
func (m *MockChain) SendMsgs(msgs []types.Msg) ([]MsgID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMsgs", msgs)
	ret0, _ := ret[0].([]MsgID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendMsgs indicates an expected call of SendMsgs.
func (mr *MockChainMockRecorder) SendMsgs(msgs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsgs", reflect.TypeOf((*MockChain)(nil).SendMsgs), msgs)
}

// SetRelayInfo mocks base method.
func (m *MockChain) SetRelayInfo(path *PathEnd, counterparty *ProvableChain, counterpartyPath *PathEnd) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRelayInfo", path, counterparty, counterpartyPath)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetRelayInfo indicates an expected call of SetRelayInfo.
func (mr *MockChainMockRecorder) SetRelayInfo(path, counterparty, counterpartyPath any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRelayInfo", reflect.TypeOf((*MockChain)(nil).SetRelayInfo), path, counterparty, counterpartyPath)
}

// SetupForRelay mocks base method.
func (m *MockChain) SetupForRelay(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetupForRelay", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetupForRelay indicates an expected call of SetupForRelay.
func (mr *MockChainMockRecorder) SetupForRelay(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetupForRelay", reflect.TypeOf((*MockChain)(nil).SetupForRelay), ctx)
}

// Timestamp mocks base method.
func (m *MockChain) Timestamp(arg0 exported.Height) (time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Timestamp", arg0)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Timestamp indicates an expected call of Timestamp.
func (mr *MockChainMockRecorder) Timestamp(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Timestamp", reflect.TypeOf((*MockChain)(nil).Timestamp), arg0)
}

// MockChainInfo is a mock of ChainInfo interface.
type MockChainInfo struct {
	ctrl     *gomock.Controller
	recorder *MockChainInfoMockRecorder
}

// MockChainInfoMockRecorder is the mock recorder for MockChainInfo.
type MockChainInfoMockRecorder struct {
	mock *MockChainInfo
}

// NewMockChainInfo creates a new mock instance.
func NewMockChainInfo(ctrl *gomock.Controller) *MockChainInfo {
	mock := &MockChainInfo{ctrl: ctrl}
	mock.recorder = &MockChainInfoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChainInfo) EXPECT() *MockChainInfoMockRecorder {
	return m.recorder
}

// AverageBlockTime mocks base method.
func (m *MockChainInfo) AverageBlockTime() time.Duration {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AverageBlockTime")
	ret0, _ := ret[0].(time.Duration)
	return ret0
}

// AverageBlockTime indicates an expected call of AverageBlockTime.
func (mr *MockChainInfoMockRecorder) AverageBlockTime() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AverageBlockTime", reflect.TypeOf((*MockChainInfo)(nil).AverageBlockTime))
}

// ChainID mocks base method.
func (m *MockChainInfo) ChainID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChainID")
	ret0, _ := ret[0].(string)
	return ret0
}

// ChainID indicates an expected call of ChainID.
func (mr *MockChainInfoMockRecorder) ChainID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChainID", reflect.TypeOf((*MockChainInfo)(nil).ChainID))
}

// LatestHeight mocks base method.
func (m *MockChainInfo) LatestHeight() (exported.Height, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LatestHeight")
	ret0, _ := ret[0].(exported.Height)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LatestHeight indicates an expected call of LatestHeight.
func (mr *MockChainInfoMockRecorder) LatestHeight() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LatestHeight", reflect.TypeOf((*MockChainInfo)(nil).LatestHeight))
}

// Timestamp mocks base method.
func (m *MockChainInfo) Timestamp(arg0 exported.Height) (time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Timestamp", arg0)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Timestamp indicates an expected call of Timestamp.
func (mr *MockChainInfoMockRecorder) Timestamp(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Timestamp", reflect.TypeOf((*MockChainInfo)(nil).Timestamp), arg0)
}

// MockMsgEventListener is a mock of MsgEventListener interface.
type MockMsgEventListener struct {
	ctrl     *gomock.Controller
	recorder *MockMsgEventListenerMockRecorder
}

// MockMsgEventListenerMockRecorder is the mock recorder for MockMsgEventListener.
type MockMsgEventListenerMockRecorder struct {
	mock *MockMsgEventListener
}

// NewMockMsgEventListener creates a new mock instance.
func NewMockMsgEventListener(ctrl *gomock.Controller) *MockMsgEventListener {
	mock := &MockMsgEventListener{ctrl: ctrl}
	mock.recorder = &MockMsgEventListenerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMsgEventListener) EXPECT() *MockMsgEventListenerMockRecorder {
	return m.recorder
}

// OnSentMsg mocks base method.
func (m *MockMsgEventListener) OnSentMsg(msgs []types.Msg) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnSentMsg", msgs)
	ret0, _ := ret[0].(error)
	return ret0
}

// OnSentMsg indicates an expected call of OnSentMsg.
func (mr *MockMsgEventListenerMockRecorder) OnSentMsg(msgs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnSentMsg", reflect.TypeOf((*MockMsgEventListener)(nil).OnSentMsg), msgs)
}

// MockIBCQuerier is a mock of IBCQuerier interface.
type MockIBCQuerier struct {
	ctrl     *gomock.Controller
	recorder *MockIBCQuerierMockRecorder
}

// MockIBCQuerierMockRecorder is the mock recorder for MockIBCQuerier.
type MockIBCQuerierMockRecorder struct {
	mock *MockIBCQuerier
}

// NewMockIBCQuerier creates a new mock instance.
func NewMockIBCQuerier(ctrl *gomock.Controller) *MockIBCQuerier {
	mock := &MockIBCQuerier{ctrl: ctrl}
	mock.recorder = &MockIBCQuerierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBCQuerier) EXPECT() *MockIBCQuerierMockRecorder {
	return m.recorder
}

// QueryChannel mocks base method.
func (m *MockIBCQuerier) QueryChannel(ctx QueryContext) (*types3.QueryChannelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryChannel", ctx)
	ret0, _ := ret[0].(*types3.QueryChannelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryChannel indicates an expected call of QueryChannel.
func (mr *MockIBCQuerierMockRecorder) QueryChannel(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryChannel", reflect.TypeOf((*MockIBCQuerier)(nil).QueryChannel), ctx)
}

// QueryClientConsensusState mocks base method.
func (m *MockIBCQuerier) QueryClientConsensusState(ctx QueryContext, dstClientConsHeight exported.Height) (*types1.QueryConsensusStateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryClientConsensusState", ctx, dstClientConsHeight)
	ret0, _ := ret[0].(*types1.QueryConsensusStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryClientConsensusState indicates an expected call of QueryClientConsensusState.
func (mr *MockIBCQuerierMockRecorder) QueryClientConsensusState(ctx, dstClientConsHeight any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryClientConsensusState", reflect.TypeOf((*MockIBCQuerier)(nil).QueryClientConsensusState), ctx, dstClientConsHeight)
}

// QueryClientState mocks base method.
func (m *MockIBCQuerier) QueryClientState(ctx QueryContext) (*types1.QueryClientStateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryClientState", ctx)
	ret0, _ := ret[0].(*types1.QueryClientStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryClientState indicates an expected call of QueryClientState.
func (mr *MockIBCQuerierMockRecorder) QueryClientState(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryClientState", reflect.TypeOf((*MockIBCQuerier)(nil).QueryClientState), ctx)
}

// QueryConnection mocks base method.
func (m *MockIBCQuerier) QueryConnection(ctx QueryContext) (*types2.QueryConnectionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryConnection", ctx)
	ret0, _ := ret[0].(*types2.QueryConnectionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryConnection indicates an expected call of QueryConnection.
func (mr *MockIBCQuerierMockRecorder) QueryConnection(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryConnection", reflect.TypeOf((*MockIBCQuerier)(nil).QueryConnection), ctx)
}

// QueryUnfinalizedRelayAcknowledgements mocks base method.
func (m *MockIBCQuerier) QueryUnfinalizedRelayAcknowledgements(ctx QueryContext, counterparty LightClientICS04Querier) (PacketInfoList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnfinalizedRelayAcknowledgements", ctx, counterparty)
	ret0, _ := ret[0].(PacketInfoList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnfinalizedRelayAcknowledgements indicates an expected call of QueryUnfinalizedRelayAcknowledgements.
func (mr *MockIBCQuerierMockRecorder) QueryUnfinalizedRelayAcknowledgements(ctx, counterparty any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnfinalizedRelayAcknowledgements", reflect.TypeOf((*MockIBCQuerier)(nil).QueryUnfinalizedRelayAcknowledgements), ctx, counterparty)
}

// QueryUnfinalizedRelayPackets mocks base method.
func (m *MockIBCQuerier) QueryUnfinalizedRelayPackets(ctx QueryContext, counterparty LightClientICS04Querier) (PacketInfoList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnfinalizedRelayPackets", ctx, counterparty)
	ret0, _ := ret[0].(PacketInfoList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnfinalizedRelayPackets indicates an expected call of QueryUnfinalizedRelayPackets.
func (mr *MockIBCQuerierMockRecorder) QueryUnfinalizedRelayPackets(ctx, counterparty any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnfinalizedRelayPackets", reflect.TypeOf((*MockIBCQuerier)(nil).QueryUnfinalizedRelayPackets), ctx, counterparty)
}

// QueryUnreceivedAcknowledgements mocks base method.
func (m *MockIBCQuerier) QueryUnreceivedAcknowledgements(ctx QueryContext, seqs []uint64) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnreceivedAcknowledgements", ctx, seqs)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnreceivedAcknowledgements indicates an expected call of QueryUnreceivedAcknowledgements.
func (mr *MockIBCQuerierMockRecorder) QueryUnreceivedAcknowledgements(ctx, seqs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnreceivedAcknowledgements", reflect.TypeOf((*MockIBCQuerier)(nil).QueryUnreceivedAcknowledgements), ctx, seqs)
}

// QueryUnreceivedPackets mocks base method.
func (m *MockIBCQuerier) QueryUnreceivedPackets(ctx QueryContext, seqs []uint64) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnreceivedPackets", ctx, seqs)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnreceivedPackets indicates an expected call of QueryUnreceivedPackets.
func (mr *MockIBCQuerierMockRecorder) QueryUnreceivedPackets(ctx, seqs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnreceivedPackets", reflect.TypeOf((*MockIBCQuerier)(nil).QueryUnreceivedPackets), ctx, seqs)
}

// MockICS02Querier is a mock of ICS02Querier interface.
type MockICS02Querier struct {
	ctrl     *gomock.Controller
	recorder *MockICS02QuerierMockRecorder
}

// MockICS02QuerierMockRecorder is the mock recorder for MockICS02Querier.
type MockICS02QuerierMockRecorder struct {
	mock *MockICS02Querier
}

// NewMockICS02Querier creates a new mock instance.
func NewMockICS02Querier(ctrl *gomock.Controller) *MockICS02Querier {
	mock := &MockICS02Querier{ctrl: ctrl}
	mock.recorder = &MockICS02QuerierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICS02Querier) EXPECT() *MockICS02QuerierMockRecorder {
	return m.recorder
}

// QueryClientConsensusState mocks base method.
func (m *MockICS02Querier) QueryClientConsensusState(ctx QueryContext, dstClientConsHeight exported.Height) (*types1.QueryConsensusStateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryClientConsensusState", ctx, dstClientConsHeight)
	ret0, _ := ret[0].(*types1.QueryConsensusStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryClientConsensusState indicates an expected call of QueryClientConsensusState.
func (mr *MockICS02QuerierMockRecorder) QueryClientConsensusState(ctx, dstClientConsHeight any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryClientConsensusState", reflect.TypeOf((*MockICS02Querier)(nil).QueryClientConsensusState), ctx, dstClientConsHeight)
}

// QueryClientState mocks base method.
func (m *MockICS02Querier) QueryClientState(ctx QueryContext) (*types1.QueryClientStateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryClientState", ctx)
	ret0, _ := ret[0].(*types1.QueryClientStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryClientState indicates an expected call of QueryClientState.
func (mr *MockICS02QuerierMockRecorder) QueryClientState(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryClientState", reflect.TypeOf((*MockICS02Querier)(nil).QueryClientState), ctx)
}

// MockICS03Querier is a mock of ICS03Querier interface.
type MockICS03Querier struct {
	ctrl     *gomock.Controller
	recorder *MockICS03QuerierMockRecorder
}

// MockICS03QuerierMockRecorder is the mock recorder for MockICS03Querier.
type MockICS03QuerierMockRecorder struct {
	mock *MockICS03Querier
}

// NewMockICS03Querier creates a new mock instance.
func NewMockICS03Querier(ctrl *gomock.Controller) *MockICS03Querier {
	mock := &MockICS03Querier{ctrl: ctrl}
	mock.recorder = &MockICS03QuerierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICS03Querier) EXPECT() *MockICS03QuerierMockRecorder {
	return m.recorder
}

// QueryConnection mocks base method.
func (m *MockICS03Querier) QueryConnection(ctx QueryContext) (*types2.QueryConnectionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryConnection", ctx)
	ret0, _ := ret[0].(*types2.QueryConnectionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryConnection indicates an expected call of QueryConnection.
func (mr *MockICS03QuerierMockRecorder) QueryConnection(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryConnection", reflect.TypeOf((*MockICS03Querier)(nil).QueryConnection), ctx)
}

// MockICS04Querier is a mock of ICS04Querier interface.
type MockICS04Querier struct {
	ctrl     *gomock.Controller
	recorder *MockICS04QuerierMockRecorder
}

// MockICS04QuerierMockRecorder is the mock recorder for MockICS04Querier.
type MockICS04QuerierMockRecorder struct {
	mock *MockICS04Querier
}

// NewMockICS04Querier creates a new mock instance.
func NewMockICS04Querier(ctrl *gomock.Controller) *MockICS04Querier {
	mock := &MockICS04Querier{ctrl: ctrl}
	mock.recorder = &MockICS04QuerierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICS04Querier) EXPECT() *MockICS04QuerierMockRecorder {
	return m.recorder
}

// QueryChannel mocks base method.
func (m *MockICS04Querier) QueryChannel(ctx QueryContext) (*types3.QueryChannelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryChannel", ctx)
	ret0, _ := ret[0].(*types3.QueryChannelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryChannel indicates an expected call of QueryChannel.
func (mr *MockICS04QuerierMockRecorder) QueryChannel(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryChannel", reflect.TypeOf((*MockICS04Querier)(nil).QueryChannel), ctx)
}

// QueryUnfinalizedRelayAcknowledgements mocks base method.
func (m *MockICS04Querier) QueryUnfinalizedRelayAcknowledgements(ctx QueryContext, counterparty LightClientICS04Querier) (PacketInfoList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnfinalizedRelayAcknowledgements", ctx, counterparty)
	ret0, _ := ret[0].(PacketInfoList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnfinalizedRelayAcknowledgements indicates an expected call of QueryUnfinalizedRelayAcknowledgements.
func (mr *MockICS04QuerierMockRecorder) QueryUnfinalizedRelayAcknowledgements(ctx, counterparty any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnfinalizedRelayAcknowledgements", reflect.TypeOf((*MockICS04Querier)(nil).QueryUnfinalizedRelayAcknowledgements), ctx, counterparty)
}

// QueryUnfinalizedRelayPackets mocks base method.
func (m *MockICS04Querier) QueryUnfinalizedRelayPackets(ctx QueryContext, counterparty LightClientICS04Querier) (PacketInfoList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnfinalizedRelayPackets", ctx, counterparty)
	ret0, _ := ret[0].(PacketInfoList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnfinalizedRelayPackets indicates an expected call of QueryUnfinalizedRelayPackets.
func (mr *MockICS04QuerierMockRecorder) QueryUnfinalizedRelayPackets(ctx, counterparty any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnfinalizedRelayPackets", reflect.TypeOf((*MockICS04Querier)(nil).QueryUnfinalizedRelayPackets), ctx, counterparty)
}

// QueryUnreceivedAcknowledgements mocks base method.
func (m *MockICS04Querier) QueryUnreceivedAcknowledgements(ctx QueryContext, seqs []uint64) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnreceivedAcknowledgements", ctx, seqs)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnreceivedAcknowledgements indicates an expected call of QueryUnreceivedAcknowledgements.
func (mr *MockICS04QuerierMockRecorder) QueryUnreceivedAcknowledgements(ctx, seqs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnreceivedAcknowledgements", reflect.TypeOf((*MockICS04Querier)(nil).QueryUnreceivedAcknowledgements), ctx, seqs)
}

// QueryUnreceivedPackets mocks base method.
func (m *MockICS04Querier) QueryUnreceivedPackets(ctx QueryContext, seqs []uint64) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnreceivedPackets", ctx, seqs)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnreceivedPackets indicates an expected call of QueryUnreceivedPackets.
func (mr *MockICS04QuerierMockRecorder) QueryUnreceivedPackets(ctx, seqs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnreceivedPackets", reflect.TypeOf((*MockICS04Querier)(nil).QueryUnreceivedPackets), ctx, seqs)
}

// MockICS20Querier is a mock of ICS20Querier interface.
type MockICS20Querier struct {
	ctrl     *gomock.Controller
	recorder *MockICS20QuerierMockRecorder
}

// MockICS20QuerierMockRecorder is the mock recorder for MockICS20Querier.
type MockICS20QuerierMockRecorder struct {
	mock *MockICS20Querier
}

// NewMockICS20Querier creates a new mock instance.
func NewMockICS20Querier(ctrl *gomock.Controller) *MockICS20Querier {
	mock := &MockICS20Querier{ctrl: ctrl}
	mock.recorder = &MockICS20QuerierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICS20Querier) EXPECT() *MockICS20QuerierMockRecorder {
	return m.recorder
}

// QueryBalance mocks base method.
func (m *MockICS20Querier) QueryBalance(ctx QueryContext, address types.AccAddress) (types.Coins, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryBalance", ctx, address)
	ret0, _ := ret[0].(types.Coins)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryBalance indicates an expected call of QueryBalance.
func (mr *MockICS20QuerierMockRecorder) QueryBalance(ctx, address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryBalance", reflect.TypeOf((*MockICS20Querier)(nil).QueryBalance), ctx, address)
}

// QueryDenomTraces mocks base method.
func (m *MockICS20Querier) QueryDenomTraces(ctx QueryContext, offset, limit uint64) (*types0.QueryDenomTracesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryDenomTraces", ctx, offset, limit)
	ret0, _ := ret[0].(*types0.QueryDenomTracesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryDenomTraces indicates an expected call of QueryDenomTraces.
func (mr *MockICS20QuerierMockRecorder) QueryDenomTraces(ctx, offset, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryDenomTraces", reflect.TypeOf((*MockICS20Querier)(nil).QueryDenomTraces), ctx, offset, limit)
}

// MockLightClientICS04Querier is a mock of LightClientICS04Querier interface.
type MockLightClientICS04Querier struct {
	ctrl     *gomock.Controller
	recorder *MockLightClientICS04QuerierMockRecorder
}

// MockLightClientICS04QuerierMockRecorder is the mock recorder for MockLightClientICS04Querier.
type MockLightClientICS04QuerierMockRecorder struct {
	mock *MockLightClientICS04Querier
}

// NewMockLightClientICS04Querier creates a new mock instance.
func NewMockLightClientICS04Querier(ctrl *gomock.Controller) *MockLightClientICS04Querier {
	mock := &MockLightClientICS04Querier{ctrl: ctrl}
	mock.recorder = &MockLightClientICS04QuerierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLightClientICS04Querier) EXPECT() *MockLightClientICS04QuerierMockRecorder {
	return m.recorder
}

// CheckRefreshRequired mocks base method.
func (m *MockLightClientICS04Querier) CheckRefreshRequired(counterparty ChainInfoICS02Querier) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckRefreshRequired", counterparty)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckRefreshRequired indicates an expected call of CheckRefreshRequired.
func (mr *MockLightClientICS04QuerierMockRecorder) CheckRefreshRequired(counterparty any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckRefreshRequired", reflect.TypeOf((*MockLightClientICS04Querier)(nil).CheckRefreshRequired), counterparty)
}

// CreateInitialLightClientState mocks base method.
func (m *MockLightClientICS04Querier) CreateInitialLightClientState(height exported.Height) (exported.ClientState, exported.ConsensusState, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInitialLightClientState", height)
	ret0, _ := ret[0].(exported.ClientState)
	ret1, _ := ret[1].(exported.ConsensusState)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CreateInitialLightClientState indicates an expected call of CreateInitialLightClientState.
func (mr *MockLightClientICS04QuerierMockRecorder) CreateInitialLightClientState(height any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInitialLightClientState", reflect.TypeOf((*MockLightClientICS04Querier)(nil).CreateInitialLightClientState), height)
}

// GetLatestFinalizedHeader mocks base method.
func (m *MockLightClientICS04Querier) GetLatestFinalizedHeader() (Header, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestFinalizedHeader")
	ret0, _ := ret[0].(Header)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestFinalizedHeader indicates an expected call of GetLatestFinalizedHeader.
func (mr *MockLightClientICS04QuerierMockRecorder) GetLatestFinalizedHeader() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestFinalizedHeader", reflect.TypeOf((*MockLightClientICS04Querier)(nil).GetLatestFinalizedHeader))
}

// QueryChannel mocks base method.
func (m *MockLightClientICS04Querier) QueryChannel(ctx QueryContext) (*types3.QueryChannelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryChannel", ctx)
	ret0, _ := ret[0].(*types3.QueryChannelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryChannel indicates an expected call of QueryChannel.
func (mr *MockLightClientICS04QuerierMockRecorder) QueryChannel(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryChannel", reflect.TypeOf((*MockLightClientICS04Querier)(nil).QueryChannel), ctx)
}

// QueryUnfinalizedRelayAcknowledgements mocks base method.
func (m *MockLightClientICS04Querier) QueryUnfinalizedRelayAcknowledgements(ctx QueryContext, counterparty LightClientICS04Querier) (PacketInfoList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnfinalizedRelayAcknowledgements", ctx, counterparty)
	ret0, _ := ret[0].(PacketInfoList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnfinalizedRelayAcknowledgements indicates an expected call of QueryUnfinalizedRelayAcknowledgements.
func (mr *MockLightClientICS04QuerierMockRecorder) QueryUnfinalizedRelayAcknowledgements(ctx, counterparty any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnfinalizedRelayAcknowledgements", reflect.TypeOf((*MockLightClientICS04Querier)(nil).QueryUnfinalizedRelayAcknowledgements), ctx, counterparty)
}

// QueryUnfinalizedRelayPackets mocks base method.
func (m *MockLightClientICS04Querier) QueryUnfinalizedRelayPackets(ctx QueryContext, counterparty LightClientICS04Querier) (PacketInfoList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnfinalizedRelayPackets", ctx, counterparty)
	ret0, _ := ret[0].(PacketInfoList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnfinalizedRelayPackets indicates an expected call of QueryUnfinalizedRelayPackets.
func (mr *MockLightClientICS04QuerierMockRecorder) QueryUnfinalizedRelayPackets(ctx, counterparty any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnfinalizedRelayPackets", reflect.TypeOf((*MockLightClientICS04Querier)(nil).QueryUnfinalizedRelayPackets), ctx, counterparty)
}

// QueryUnreceivedAcknowledgements mocks base method.
func (m *MockLightClientICS04Querier) QueryUnreceivedAcknowledgements(ctx QueryContext, seqs []uint64) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnreceivedAcknowledgements", ctx, seqs)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnreceivedAcknowledgements indicates an expected call of QueryUnreceivedAcknowledgements.
func (mr *MockLightClientICS04QuerierMockRecorder) QueryUnreceivedAcknowledgements(ctx, seqs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnreceivedAcknowledgements", reflect.TypeOf((*MockLightClientICS04Querier)(nil).QueryUnreceivedAcknowledgements), ctx, seqs)
}

// QueryUnreceivedPackets mocks base method.
func (m *MockLightClientICS04Querier) QueryUnreceivedPackets(ctx QueryContext, seqs []uint64) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnreceivedPackets", ctx, seqs)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnreceivedPackets indicates an expected call of QueryUnreceivedPackets.
func (mr *MockLightClientICS04QuerierMockRecorder) QueryUnreceivedPackets(ctx, seqs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnreceivedPackets", reflect.TypeOf((*MockLightClientICS04Querier)(nil).QueryUnreceivedPackets), ctx, seqs)
}

// SetupHeadersForUpdate mocks base method.
func (m *MockLightClientICS04Querier) SetupHeadersForUpdate(counterparty FinalityAwareChain, latestFinalizedHeader Header) ([]Header, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetupHeadersForUpdate", counterparty, latestFinalizedHeader)
	ret0, _ := ret[0].([]Header)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetupHeadersForUpdate indicates an expected call of SetupHeadersForUpdate.
func (mr *MockLightClientICS04QuerierMockRecorder) SetupHeadersForUpdate(counterparty, latestFinalizedHeader any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetupHeadersForUpdate", reflect.TypeOf((*MockLightClientICS04Querier)(nil).SetupHeadersForUpdate), counterparty, latestFinalizedHeader)
}

// MockQueryContext is a mock of QueryContext interface.
type MockQueryContext struct {
	ctrl     *gomock.Controller
	recorder *MockQueryContextMockRecorder
}

// MockQueryContextMockRecorder is the mock recorder for MockQueryContext.
type MockQueryContextMockRecorder struct {
	mock *MockQueryContext
}

// NewMockQueryContext creates a new mock instance.
func NewMockQueryContext(ctrl *gomock.Controller) *MockQueryContext {
	mock := &MockQueryContext{ctrl: ctrl}
	mock.recorder = &MockQueryContextMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQueryContext) EXPECT() *MockQueryContextMockRecorder {
	return m.recorder
}

// Context mocks base method.
func (m *MockQueryContext) Context() context.Context {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Context")
	ret0, _ := ret[0].(context.Context)
	return ret0
}

// Context indicates an expected call of Context.
func (mr *MockQueryContextMockRecorder) Context() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Context", reflect.TypeOf((*MockQueryContext)(nil).Context))
}

// Height mocks base method.
func (m *MockQueryContext) Height() exported.Height {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Height")
	ret0, _ := ret[0].(exported.Height)
	return ret0
}

// Height indicates an expected call of Height.
func (mr *MockQueryContextMockRecorder) Height() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Height", reflect.TypeOf((*MockQueryContext)(nil).Height))
}

// Code generated by MockGen. DO NOT EDIT.
// Source: provers.go
//
// Generated by this command:
//
//	mockgen -source=provers.go -package=core -destination=./mock_provers.go
//

// Package core is a generated GoMock package.
package core

import (
	context "context"
	reflect "reflect"
	time "time"

	codec "github.com/cosmos/cosmos-sdk/codec"
	types "github.com/cosmos/cosmos-sdk/types"
	types0 "github.com/cosmos/ibc-go/v7/modules/apps/transfer/types"
	types1 "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	types2 "github.com/cosmos/ibc-go/v7/modules/core/03-connection/types"
	types3 "github.com/cosmos/ibc-go/v7/modules/core/04-channel/types"
	exported "github.com/cosmos/ibc-go/v7/modules/core/exported"
	gomock "go.uber.org/mock/gomock"
)

// MockProver is a mock of Prover interface.
type MockProver struct {
	ctrl     *gomock.Controller
	recorder *MockProverMockRecorder
}

// MockProverMockRecorder is the mock recorder for MockProver.
type MockProverMockRecorder struct {
	mock *MockProver
}

// NewMockProver creates a new mock instance.
func NewMockProver(ctrl *gomock.Controller) *MockProver {
	mock := &MockProver{ctrl: ctrl}
	mock.recorder = &MockProverMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProver) EXPECT() *MockProverMockRecorder {
	return m.recorder
}

// CheckRefreshRequired mocks base method.
func (m *MockProver) CheckRefreshRequired(counterparty ChainInfoICS02Querier) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckRefreshRequired", counterparty)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckRefreshRequired indicates an expected call of CheckRefreshRequired.
func (mr *MockProverMockRecorder) CheckRefreshRequired(counterparty any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckRefreshRequired", reflect.TypeOf((*MockProver)(nil).CheckRefreshRequired), counterparty)
}

// CreateInitialLightClientState mocks base method.
func (m *MockProver) CreateInitialLightClientState(height exported.Height) (exported.ClientState, exported.ConsensusState, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInitialLightClientState", height)
	ret0, _ := ret[0].(exported.ClientState)
	ret1, _ := ret[1].(exported.ConsensusState)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CreateInitialLightClientState indicates an expected call of CreateInitialLightClientState.
func (mr *MockProverMockRecorder) CreateInitialLightClientState(height any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInitialLightClientState", reflect.TypeOf((*MockProver)(nil).CreateInitialLightClientState), height)
}

// GetLatestFinalizedHeader mocks base method.
func (m *MockProver) GetLatestFinalizedHeader() (Header, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestFinalizedHeader")
	ret0, _ := ret[0].(Header)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestFinalizedHeader indicates an expected call of GetLatestFinalizedHeader.
func (mr *MockProverMockRecorder) GetLatestFinalizedHeader() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestFinalizedHeader", reflect.TypeOf((*MockProver)(nil).GetLatestFinalizedHeader))
}

// Init mocks base method.
func (m *MockProver) Init(homePath string, timeout time.Duration, codec codec.ProtoCodecMarshaler, debug bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Init", homePath, timeout, codec, debug)
	ret0, _ := ret[0].(error)
	return ret0
}

// Init indicates an expected call of Init.
func (mr *MockProverMockRecorder) Init(homePath, timeout, codec, debug any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Init", reflect.TypeOf((*MockProver)(nil).Init), homePath, timeout, codec, debug)
}

// ProveHostConsensusState mocks base method.
func (m *MockProver) ProveHostConsensusState(ctx QueryContext, height exported.Height, consensusState exported.ConsensusState) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProveHostConsensusState", ctx, height, consensusState)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProveHostConsensusState indicates an expected call of ProveHostConsensusState.
func (mr *MockProverMockRecorder) ProveHostConsensusState(ctx, height, consensusState any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProveHostConsensusState", reflect.TypeOf((*MockProver)(nil).ProveHostConsensusState), ctx, height, consensusState)
}

// ProveState mocks base method.
func (m *MockProver) ProveState(ctx QueryContext, path string, value []byte) ([]byte, types1.Height, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProveState", ctx, path, value)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(types1.Height)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ProveState indicates an expected call of ProveState.
func (mr *MockProverMockRecorder) ProveState(ctx, path, value any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProveState", reflect.TypeOf((*MockProver)(nil).ProveState), ctx, path, value)
}

// SetRelayInfo mocks base method.
func (m *MockProver) SetRelayInfo(path *PathEnd, counterparty *ProvableChain, counterpartyPath *PathEnd) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRelayInfo", path, counterparty, counterpartyPath)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetRelayInfo indicates an expected call of SetRelayInfo.
func (mr *MockProverMockRecorder) SetRelayInfo(path, counterparty, counterpartyPath any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRelayInfo", reflect.TypeOf((*MockProver)(nil).SetRelayInfo), path, counterparty, counterpartyPath)
}

// SetupForRelay mocks base method.
func (m *MockProver) SetupForRelay(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetupForRelay", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetupForRelay indicates an expected call of SetupForRelay.
func (mr *MockProverMockRecorder) SetupForRelay(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetupForRelay", reflect.TypeOf((*MockProver)(nil).SetupForRelay), ctx)
}

// SetupHeadersForUpdate mocks base method.
func (m *MockProver) SetupHeadersForUpdate(counterparty FinalityAwareChain, latestFinalizedHeader Header) ([]Header, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetupHeadersForUpdate", counterparty, latestFinalizedHeader)
	ret0, _ := ret[0].([]Header)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetupHeadersForUpdate indicates an expected call of SetupHeadersForUpdate.
func (mr *MockProverMockRecorder) SetupHeadersForUpdate(counterparty, latestFinalizedHeader any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetupHeadersForUpdate", reflect.TypeOf((*MockProver)(nil).SetupHeadersForUpdate), counterparty, latestFinalizedHeader)
}

// MockStateProver is a mock of StateProver interface.
type MockStateProver struct {
	ctrl     *gomock.Controller
	recorder *MockStateProverMockRecorder
}

// MockStateProverMockRecorder is the mock recorder for MockStateProver.
type MockStateProverMockRecorder struct {
	mock *MockStateProver
}

// NewMockStateProver creates a new mock instance.
func NewMockStateProver(ctrl *gomock.Controller) *MockStateProver {
	mock := &MockStateProver{ctrl: ctrl}
	mock.recorder = &MockStateProverMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStateProver) EXPECT() *MockStateProverMockRecorder {
	return m.recorder
}

// ProveHostConsensusState mocks base method.
func (m *MockStateProver) ProveHostConsensusState(ctx QueryContext, height exported.Height, consensusState exported.ConsensusState) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProveHostConsensusState", ctx, height, consensusState)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProveHostConsensusState indicates an expected call of ProveHostConsensusState.
func (mr *MockStateProverMockRecorder) ProveHostConsensusState(ctx, height, consensusState any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProveHostConsensusState", reflect.TypeOf((*MockStateProver)(nil).ProveHostConsensusState), ctx, height, consensusState)
}

// ProveState mocks base method.
func (m *MockStateProver) ProveState(ctx QueryContext, path string, value []byte) ([]byte, types1.Height, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProveState", ctx, path, value)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(types1.Height)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ProveState indicates an expected call of ProveState.
func (mr *MockStateProverMockRecorder) ProveState(ctx, path, value any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProveState", reflect.TypeOf((*MockStateProver)(nil).ProveState), ctx, path, value)
}

// MockLightClient is a mock of LightClient interface.
type MockLightClient struct {
	ctrl     *gomock.Controller
	recorder *MockLightClientMockRecorder
}

// MockLightClientMockRecorder is the mock recorder for MockLightClient.
type MockLightClientMockRecorder struct {
	mock *MockLightClient
}

// NewMockLightClient creates a new mock instance.
func NewMockLightClient(ctrl *gomock.Controller) *MockLightClient {
	mock := &MockLightClient{ctrl: ctrl}
	mock.recorder = &MockLightClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLightClient) EXPECT() *MockLightClientMockRecorder {
	return m.recorder
}

// CheckRefreshRequired mocks base method.
func (m *MockLightClient) CheckRefreshRequired(counterparty ChainInfoICS02Querier) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckRefreshRequired", counterparty)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckRefreshRequired indicates an expected call of CheckRefreshRequired.
func (mr *MockLightClientMockRecorder) CheckRefreshRequired(counterparty any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckRefreshRequired", reflect.TypeOf((*MockLightClient)(nil).CheckRefreshRequired), counterparty)
}

// CreateInitialLightClientState mocks base method.
func (m *MockLightClient) CreateInitialLightClientState(height exported.Height) (exported.ClientState, exported.ConsensusState, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInitialLightClientState", height)
	ret0, _ := ret[0].(exported.ClientState)
	ret1, _ := ret[1].(exported.ConsensusState)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CreateInitialLightClientState indicates an expected call of CreateInitialLightClientState.
func (mr *MockLightClientMockRecorder) CreateInitialLightClientState(height any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInitialLightClientState", reflect.TypeOf((*MockLightClient)(nil).CreateInitialLightClientState), height)
}

// GetLatestFinalizedHeader mocks base method.
func (m *MockLightClient) GetLatestFinalizedHeader() (Header, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestFinalizedHeader")
	ret0, _ := ret[0].(Header)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestFinalizedHeader indicates an expected call of GetLatestFinalizedHeader.
func (mr *MockLightClientMockRecorder) GetLatestFinalizedHeader() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestFinalizedHeader", reflect.TypeOf((*MockLightClient)(nil).GetLatestFinalizedHeader))
}

// SetupHeadersForUpdate mocks base method.
func (m *MockLightClient) SetupHeadersForUpdate(counterparty FinalityAwareChain, latestFinalizedHeader Header) ([]Header, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetupHeadersForUpdate", counterparty, latestFinalizedHeader)
	ret0, _ := ret[0].([]Header)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetupHeadersForUpdate indicates an expected call of SetupHeadersForUpdate.
func (mr *MockLightClientMockRecorder) SetupHeadersForUpdate(counterparty, latestFinalizedHeader any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetupHeadersForUpdate", reflect.TypeOf((*MockLightClient)(nil).SetupHeadersForUpdate), counterparty, latestFinalizedHeader)
}

// MockFinalityAware is a mock of FinalityAware interface.
type MockFinalityAware struct {
	ctrl     *gomock.Controller
	recorder *MockFinalityAwareMockRecorder
}

// MockFinalityAwareMockRecorder is the mock recorder for MockFinalityAware.
type MockFinalityAwareMockRecorder struct {
	mock *MockFinalityAware
}

// NewMockFinalityAware creates a new mock instance.
func NewMockFinalityAware(ctrl *gomock.Controller) *MockFinalityAware {
	mock := &MockFinalityAware{ctrl: ctrl}
	mock.recorder = &MockFinalityAwareMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFinalityAware) EXPECT() *MockFinalityAwareMockRecorder {
	return m.recorder
}

// GetLatestFinalizedHeader mocks base method.
func (m *MockFinalityAware) GetLatestFinalizedHeader() (Header, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestFinalizedHeader")
	ret0, _ := ret[0].(Header)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestFinalizedHeader indicates an expected call of GetLatestFinalizedHeader.
func (mr *MockFinalityAwareMockRecorder) GetLatestFinalizedHeader() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestFinalizedHeader", reflect.TypeOf((*MockFinalityAware)(nil).GetLatestFinalizedHeader))
}

// MockFinalityAwareChain is a mock of FinalityAwareChain interface.
type MockFinalityAwareChain struct {
	ctrl     *gomock.Controller
	recorder *MockFinalityAwareChainMockRecorder
}

// MockFinalityAwareChainMockRecorder is the mock recorder for MockFinalityAwareChain.
type MockFinalityAwareChainMockRecorder struct {
	mock *MockFinalityAwareChain
}

// NewMockFinalityAwareChain creates a new mock instance.
func NewMockFinalityAwareChain(ctrl *gomock.Controller) *MockFinalityAwareChain {
	mock := &MockFinalityAwareChain{ctrl: ctrl}
	mock.recorder = &MockFinalityAwareChainMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFinalityAwareChain) EXPECT() *MockFinalityAwareChainMockRecorder {
	return m.recorder
}

// AverageBlockTime mocks base method.
func (m *MockFinalityAwareChain) AverageBlockTime() time.Duration {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AverageBlockTime")
	ret0, _ := ret[0].(time.Duration)
	return ret0
}

// AverageBlockTime indicates an expected call of AverageBlockTime.
func (mr *MockFinalityAwareChainMockRecorder) AverageBlockTime() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AverageBlockTime", reflect.TypeOf((*MockFinalityAwareChain)(nil).AverageBlockTime))
}

// ChainID mocks base method.
func (m *MockFinalityAwareChain) ChainID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChainID")
	ret0, _ := ret[0].(string)
	return ret0
}

// ChainID indicates an expected call of ChainID.
func (mr *MockFinalityAwareChainMockRecorder) ChainID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChainID", reflect.TypeOf((*MockFinalityAwareChain)(nil).ChainID))
}

// Codec mocks base method.
func (m *MockFinalityAwareChain) Codec() codec.ProtoCodecMarshaler {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Codec")
	ret0, _ := ret[0].(codec.ProtoCodecMarshaler)
	return ret0
}

// Codec indicates an expected call of Codec.
func (mr *MockFinalityAwareChainMockRecorder) Codec() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Codec", reflect.TypeOf((*MockFinalityAwareChain)(nil).Codec))
}

// GetAddress mocks base method.
func (m *MockFinalityAwareChain) GetAddress() (types.AccAddress, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAddress")
	ret0, _ := ret[0].(types.AccAddress)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAddress indicates an expected call of GetAddress.
func (mr *MockFinalityAwareChainMockRecorder) GetAddress() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAddress", reflect.TypeOf((*MockFinalityAwareChain)(nil).GetAddress))
}

// GetLatestFinalizedHeader mocks base method.
func (m *MockFinalityAwareChain) GetLatestFinalizedHeader() (Header, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestFinalizedHeader")
	ret0, _ := ret[0].(Header)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestFinalizedHeader indicates an expected call of GetLatestFinalizedHeader.
func (mr *MockFinalityAwareChainMockRecorder) GetLatestFinalizedHeader() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestFinalizedHeader", reflect.TypeOf((*MockFinalityAwareChain)(nil).GetLatestFinalizedHeader))
}

// GetMsgResult mocks base method.
func (m *MockFinalityAwareChain) GetMsgResult(id MsgID) (MsgResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMsgResult", id)
	ret0, _ := ret[0].(MsgResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMsgResult indicates an expected call of GetMsgResult.
func (mr *MockFinalityAwareChainMockRecorder) GetMsgResult(id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMsgResult", reflect.TypeOf((*MockFinalityAwareChain)(nil).GetMsgResult), id)
}

// Init mocks base method.
func (m *MockFinalityAwareChain) Init(homePath string, timeout time.Duration, codec codec.ProtoCodecMarshaler, debug bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Init", homePath, timeout, codec, debug)
	ret0, _ := ret[0].(error)
	return ret0
}

// Init indicates an expected call of Init.
func (mr *MockFinalityAwareChainMockRecorder) Init(homePath, timeout, codec, debug any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Init", reflect.TypeOf((*MockFinalityAwareChain)(nil).Init), homePath, timeout, codec, debug)
}

// LatestHeight mocks base method.
func (m *MockFinalityAwareChain) LatestHeight() (exported.Height, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LatestHeight")
	ret0, _ := ret[0].(exported.Height)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LatestHeight indicates an expected call of LatestHeight.
func (mr *MockFinalityAwareChainMockRecorder) LatestHeight() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LatestHeight", reflect.TypeOf((*MockFinalityAwareChain)(nil).LatestHeight))
}

// Path mocks base method.
func (m *MockFinalityAwareChain) Path() *PathEnd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Path")
	ret0, _ := ret[0].(*PathEnd)
	return ret0
}

// Path indicates an expected call of Path.
func (mr *MockFinalityAwareChainMockRecorder) Path() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Path", reflect.TypeOf((*MockFinalityAwareChain)(nil).Path))
}

// QueryBalance mocks base method.
func (m *MockFinalityAwareChain) QueryBalance(ctx QueryContext, address types.AccAddress) (types.Coins, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryBalance", ctx, address)
	ret0, _ := ret[0].(types.Coins)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryBalance indicates an expected call of QueryBalance.
func (mr *MockFinalityAwareChainMockRecorder) QueryBalance(ctx, address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryBalance", reflect.TypeOf((*MockFinalityAwareChain)(nil).QueryBalance), ctx, address)
}

// QueryChannel mocks base method.
func (m *MockFinalityAwareChain) QueryChannel(ctx QueryContext) (*types3.QueryChannelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryChannel", ctx)
	ret0, _ := ret[0].(*types3.QueryChannelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryChannel indicates an expected call of QueryChannel.
func (mr *MockFinalityAwareChainMockRecorder) QueryChannel(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryChannel", reflect.TypeOf((*MockFinalityAwareChain)(nil).QueryChannel), ctx)
}

// QueryClientConsensusState mocks base method.
func (m *MockFinalityAwareChain) QueryClientConsensusState(ctx QueryContext, dstClientConsHeight exported.Height) (*types1.QueryConsensusStateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryClientConsensusState", ctx, dstClientConsHeight)
	ret0, _ := ret[0].(*types1.QueryConsensusStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryClientConsensusState indicates an expected call of QueryClientConsensusState.
func (mr *MockFinalityAwareChainMockRecorder) QueryClientConsensusState(ctx, dstClientConsHeight any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryClientConsensusState", reflect.TypeOf((*MockFinalityAwareChain)(nil).QueryClientConsensusState), ctx, dstClientConsHeight)
}

// QueryClientState mocks base method.
func (m *MockFinalityAwareChain) QueryClientState(ctx QueryContext) (*types1.QueryClientStateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryClientState", ctx)
	ret0, _ := ret[0].(*types1.QueryClientStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryClientState indicates an expected call of QueryClientState.
func (mr *MockFinalityAwareChainMockRecorder) QueryClientState(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryClientState", reflect.TypeOf((*MockFinalityAwareChain)(nil).QueryClientState), ctx)
}

// QueryConnection mocks base method.
func (m *MockFinalityAwareChain) QueryConnection(ctx QueryContext) (*types2.QueryConnectionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryConnection", ctx)
	ret0, _ := ret[0].(*types2.QueryConnectionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryConnection indicates an expected call of QueryConnection.
func (mr *MockFinalityAwareChainMockRecorder) QueryConnection(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryConnection", reflect.TypeOf((*MockFinalityAwareChain)(nil).QueryConnection), ctx)
}

// QueryDenomTraces mocks base method.
func (m *MockFinalityAwareChain) QueryDenomTraces(ctx QueryContext, offset, limit uint64) (*types0.QueryDenomTracesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryDenomTraces", ctx, offset, limit)
	ret0, _ := ret[0].(*types0.QueryDenomTracesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryDenomTraces indicates an expected call of QueryDenomTraces.
func (mr *MockFinalityAwareChainMockRecorder) QueryDenomTraces(ctx, offset, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryDenomTraces", reflect.TypeOf((*MockFinalityAwareChain)(nil).QueryDenomTraces), ctx, offset, limit)
}

// QueryUnfinalizedRelayAcknowledgements mocks base method.
func (m *MockFinalityAwareChain) QueryUnfinalizedRelayAcknowledgements(ctx QueryContext, counterparty LightClientICS04Querier) (PacketInfoList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnfinalizedRelayAcknowledgements", ctx, counterparty)
	ret0, _ := ret[0].(PacketInfoList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnfinalizedRelayAcknowledgements indicates an expected call of QueryUnfinalizedRelayAcknowledgements.
func (mr *MockFinalityAwareChainMockRecorder) QueryUnfinalizedRelayAcknowledgements(ctx, counterparty any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnfinalizedRelayAcknowledgements", reflect.TypeOf((*MockFinalityAwareChain)(nil).QueryUnfinalizedRelayAcknowledgements), ctx, counterparty)
}

// QueryUnfinalizedRelayPackets mocks base method.
func (m *MockFinalityAwareChain) QueryUnfinalizedRelayPackets(ctx QueryContext, counterparty LightClientICS04Querier) (PacketInfoList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnfinalizedRelayPackets", ctx, counterparty)
	ret0, _ := ret[0].(PacketInfoList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnfinalizedRelayPackets indicates an expected call of QueryUnfinalizedRelayPackets.
func (mr *MockFinalityAwareChainMockRecorder) QueryUnfinalizedRelayPackets(ctx, counterparty any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnfinalizedRelayPackets", reflect.TypeOf((*MockFinalityAwareChain)(nil).QueryUnfinalizedRelayPackets), ctx, counterparty)
}

// QueryUnreceivedAcknowledgements mocks base method.
func (m *MockFinalityAwareChain) QueryUnreceivedAcknowledgements(ctx QueryContext, seqs []uint64) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnreceivedAcknowledgements", ctx, seqs)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnreceivedAcknowledgements indicates an expected call of QueryUnreceivedAcknowledgements.
func (mr *MockFinalityAwareChainMockRecorder) QueryUnreceivedAcknowledgements(ctx, seqs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnreceivedAcknowledgements", reflect.TypeOf((*MockFinalityAwareChain)(nil).QueryUnreceivedAcknowledgements), ctx, seqs)
}

// QueryUnreceivedPackets mocks base method.
func (m *MockFinalityAwareChain) QueryUnreceivedPackets(ctx QueryContext, seqs []uint64) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnreceivedPackets", ctx, seqs)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnreceivedPackets indicates an expected call of QueryUnreceivedPackets.
func (mr *MockFinalityAwareChainMockRecorder) QueryUnreceivedPackets(ctx, seqs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnreceivedPackets", reflect.TypeOf((*MockFinalityAwareChain)(nil).QueryUnreceivedPackets), ctx, seqs)
}

// RegisterMsgEventListener mocks base method.
func (m *MockFinalityAwareChain) RegisterMsgEventListener(arg0 MsgEventListener) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RegisterMsgEventListener", arg0)
}

// RegisterMsgEventListener indicates an expected call of RegisterMsgEventListener.
func (mr *MockFinalityAwareChainMockRecorder) RegisterMsgEventListener(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterMsgEventListener", reflect.TypeOf((*MockFinalityAwareChain)(nil).RegisterMsgEventListener), arg0)
}

// SendMsgs mocks base method.
func (m *MockFinalityAwareChain) SendMsgs(msgs []types.Msg) ([]MsgID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMsgs", msgs)
	ret0, _ := ret[0].([]MsgID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendMsgs indicates an expected call of SendMsgs.
func (mr *MockFinalityAwareChainMockRecorder) SendMsgs(msgs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsgs", reflect.TypeOf((*MockFinalityAwareChain)(nil).SendMsgs), msgs)
}

// SetRelayInfo mocks base method.
func (m *MockFinalityAwareChain) SetRelayInfo(path *PathEnd, counterparty *ProvableChain, counterpartyPath *PathEnd) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRelayInfo", path, counterparty, counterpartyPath)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetRelayInfo indicates an expected call of SetRelayInfo.
func (mr *MockFinalityAwareChainMockRecorder) SetRelayInfo(path, counterparty, counterpartyPath any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRelayInfo", reflect.TypeOf((*MockFinalityAwareChain)(nil).SetRelayInfo), path, counterparty, counterpartyPath)
}

// SetupForRelay mocks base method.
func (m *MockFinalityAwareChain) SetupForRelay(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetupForRelay", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetupForRelay indicates an expected call of SetupForRelay.
func (mr *MockFinalityAwareChainMockRecorder) SetupForRelay(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetupForRelay", reflect.TypeOf((*MockFinalityAwareChain)(nil).SetupForRelay), ctx)
}

// Timestamp mocks base method.
func (m *MockFinalityAwareChain) Timestamp(arg0 exported.Height) (time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Timestamp", arg0)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Timestamp indicates an expected call of Timestamp.
func (mr *MockFinalityAwareChainMockRecorder) Timestamp(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Timestamp", reflect.TypeOf((*MockFinalityAwareChain)(nil).Timestamp), arg0)
}

// MockChainInfoICS02Querier is a mock of ChainInfoICS02Querier interface.
type MockChainInfoICS02Querier struct {
	ctrl     *gomock.Controller
	recorder *MockChainInfoICS02QuerierMockRecorder
}

// MockChainInfoICS02QuerierMockRecorder is the mock recorder for MockChainInfoICS02Querier.
type MockChainInfoICS02QuerierMockRecorder struct {
	mock *MockChainInfoICS02Querier
}

// NewMockChainInfoICS02Querier creates a new mock instance.
func NewMockChainInfoICS02Querier(ctrl *gomock.Controller) *MockChainInfoICS02Querier {
	mock := &MockChainInfoICS02Querier{ctrl: ctrl}
	mock.recorder = &MockChainInfoICS02QuerierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChainInfoICS02Querier) EXPECT() *MockChainInfoICS02QuerierMockRecorder {
	return m.recorder
}

// AverageBlockTime mocks base method.
func (m *MockChainInfoICS02Querier) AverageBlockTime() time.Duration {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AverageBlockTime")
	ret0, _ := ret[0].(time.Duration)
	return ret0
}

// AverageBlockTime indicates an expected call of AverageBlockTime.
func (mr *MockChainInfoICS02QuerierMockRecorder) AverageBlockTime() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AverageBlockTime", reflect.TypeOf((*MockChainInfoICS02Querier)(nil).AverageBlockTime))
}

// ChainID mocks base method.
func (m *MockChainInfoICS02Querier) ChainID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChainID")
	ret0, _ := ret[0].(string)
	return ret0
}

// ChainID indicates an expected call of ChainID.
func (mr *MockChainInfoICS02QuerierMockRecorder) ChainID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChainID", reflect.TypeOf((*MockChainInfoICS02Querier)(nil).ChainID))
}

// LatestHeight mocks base method.
func (m *MockChainInfoICS02Querier) LatestHeight() (exported.Height, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LatestHeight")
	ret0, _ := ret[0].(exported.Height)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LatestHeight indicates an expected call of LatestHeight.
func (mr *MockChainInfoICS02QuerierMockRecorder) LatestHeight() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LatestHeight", reflect.TypeOf((*MockChainInfoICS02Querier)(nil).LatestHeight))
}

// QueryClientConsensusState mocks base method.
func (m *MockChainInfoICS02Querier) QueryClientConsensusState(ctx QueryContext, dstClientConsHeight exported.Height) (*types1.QueryConsensusStateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryClientConsensusState", ctx, dstClientConsHeight)
	ret0, _ := ret[0].(*types1.QueryConsensusStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryClientConsensusState indicates an expected call of QueryClientConsensusState.
func (mr *MockChainInfoICS02QuerierMockRecorder) QueryClientConsensusState(ctx, dstClientConsHeight any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryClientConsensusState", reflect.TypeOf((*MockChainInfoICS02Querier)(nil).QueryClientConsensusState), ctx, dstClientConsHeight)
}

// QueryClientState mocks base method.
func (m *MockChainInfoICS02Querier) QueryClientState(ctx QueryContext) (*types1.QueryClientStateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryClientState", ctx)
	ret0, _ := ret[0].(*types1.QueryClientStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryClientState indicates an expected call of QueryClientState.
func (mr *MockChainInfoICS02QuerierMockRecorder) QueryClientState(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryClientState", reflect.TypeOf((*MockChainInfoICS02Querier)(nil).QueryClientState), ctx)
}

// Timestamp mocks base method.
func (m *MockChainInfoICS02Querier) Timestamp(arg0 exported.Height) (time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Timestamp", arg0)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Timestamp indicates an expected call of Timestamp.
func (mr *MockChainInfoICS02QuerierMockRecorder) Timestamp(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Timestamp", reflect.TypeOf((*MockChainInfoICS02Querier)(nil).Timestamp), arg0)
}

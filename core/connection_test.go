//go:generate mockgen -source=$GOFILE -package=core -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package core

import (
	"reflect"
	"testing"
	"time"

	sdk "github.com/cosmos/cosmos-sdk/types"
	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	types2 "github.com/cosmos/ibc-go/v7/modules/core/03-connection/types"
	commitmenttypes "github.com/cosmos/ibc-go/v7/modules/core/23-commitment/types"
	ibcexported "github.com/cosmos/ibc-go/v7/modules/core/exported"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/log"
	"github.com/google/go-cmp/cmp"
	"go.uber.org/mock/gomock"
)

// TODO State が変更されず無限ループとなるのでテスト不可
func TestCreateConnection(t *testing.T) {
	t.Skip("cannot not possible because the state remains unchanged, resulting in an infinite loop..")

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcMockChain := NewMockChain(ctrl)
	dstMockChain := NewMockChain(ctrl)
	srcMockProver := NewMockProver(ctrl)
	dstMockProver := NewMockProver(ctrl)
	srcMockQueryContext := NewMockQueryContext(ctrl)
	dstMockQueryContext := NewMockQueryContext(ctrl)
	srcMockHeader := NewMockHeader(ctrl)
	dstMockHeader := NewMockHeader(ctrl)
	srcMockHeaders := []Header{srcMockHeader}
	dstMockHeaders := []Header{dstMockHeader}
	mockSyncHeaders := NewMockSyncHeaders(ctrl)

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	var stubAccAddress = sdk.AccAddress{}
	var stubHeight = &clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}
	var stubQueryConnectionResponse = &types2.QueryConnectionResponse{
		Connection: &types2.ConnectionEnd{
			ClientId: "hb-ibft2-0",
			Versions: make([]*types2.Version, 0),
			State:    types2.UNINITIALIZED,
			Counterparty: types2.Counterparty{
				ClientId:     "hb-ibft2-0",
				ConnectionId: connectionID,
				Prefix:       DefaultChainPrefix,
			},
			DelayPeriod: DefaultDelayPeriod,
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}
	var srcStubMsgID = make([]MsgID, 1)
	var dstStubMsgID = make([]MsgID, 1)

	type args struct {
		pathName string
		src      *ProvableChain
		dst      *ProvableChain
		to       time.Duration
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		wantErr     bool
	}{
		{
			name: "should create connection successfully.",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				pathName: "",
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
				to: 2,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			if err := CreateConnection(tt.args.pathName, tt.args.src, tt.args.dst, tt.args.to); (err != nil) != tt.wantErr {
				t.Errorf("CreateConnection() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_createConnectionStep(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcMockChain := NewMockChain(ctrl)
	dstMockChain := NewMockChain(ctrl)
	srcMockProver := NewMockProver(ctrl)
	dstMockProver := NewMockProver(ctrl)
	srcMockQueryContext := NewMockQueryContext(ctrl)
	dstMockQueryContext := NewMockQueryContext(ctrl)
	srcMockHeader := NewMockHeader(ctrl)
	dstMockHeader := NewMockHeader(ctrl)
	srcMockHeaders := []Header{srcMockHeader}
	dstMockHeaders := []Header{dstMockHeader}
	mockSyncHeaders := NewMockSyncHeaders(ctrl)

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	invalidPath := &PathEnd{
		ChainID:      "",
		ClientID:     "",
		ConnectionID: "",
		ChannelID:    "",
		PortID:       "",
		Order:        "",
		Version:      "",
	}

	//var stubCodecs = MakeCodec()
	var stubAccAddress = sdk.AccAddress{}
	var stubHeight = clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}
	//var stubProofHeight = clienttypes.NewHeight(0, uint64(stubHeight.GetRevisionHeight()))
	//var stubQueryClientStateResponse = &clienttypes.QueryClientStateResponse{}
	var stubUninitializedQueryConnectionResponse = &types2.QueryConnectionResponse{
		Connection: &types2.ConnectionEnd{
			ClientId: "hb-ibft2-0",
			Versions: make([]*types2.Version, 0),
			State:    types2.UNINITIALIZED,
			Counterparty: types2.Counterparty{
				ClientId:     "hb-ibft2-0",
				ConnectionId: connectionID,
				Prefix:       DefaultChainPrefix,
			},
			DelayPeriod: DefaultDelayPeriod,
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}
	var stubInitQueryConnectionResponse = &types2.QueryConnectionResponse{
		Connection: &types2.ConnectionEnd{
			ClientId: "hb-ibft2-0",
			Versions: make([]*types2.Version, 0),
			State:    types2.INIT,
			Counterparty: types2.Counterparty{
				ClientId:     "hb-ibft2-0",
				ConnectionId: connectionID,
				Prefix:       DefaultChainPrefix,
			},
			DelayPeriod: DefaultDelayPeriod,
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}
	var stubInvalidQueryConnectionResponse = &types2.QueryConnectionResponse{
		Connection: &types2.ConnectionEnd{
			ClientId:     "",
			Versions:     nil,
			State:        0,
			Counterparty: types2.Counterparty{},
			DelayPeriod:  0,
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 0,
		},
	}
	var srcStubMsgID = make([]MsgID, 1)
	var dstStubMsgID = make([]MsgID, 1)

	type args struct {
		src *ProvableChain
		dst *ProvableChain
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		want        *RelayMsgs
		wantErr     bool
	}{
		{
			name: "should Src validatePaths error.",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				// Error
				srcMockChain.EXPECT().Path().Return(invalidPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubUninitializedQueryConnectionResponse, nil).AnyTimes()
				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubUninitializedQueryConnectionResponse, nil).AnyTimes()
				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should NewSyncHeaders error.",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubUninitializedQueryConnectionResponse, nil).AnyTimes()
				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				// Error
				dstMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubUninitializedQueryConnectionResponse, nil).AnyTimes()
				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should error updating headers.",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubUninitializedQueryConnectionResponse, nil).AnyTimes()
				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(invalidPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubUninitializedQueryConnectionResponse, nil).AnyTimes()
				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				// Error
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(nil, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should QueryConnectionPair error.",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				// Error
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(nil, nil).AnyTimes()
				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(invalidPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubUninitializedQueryConnectionResponse, nil).AnyTimes()
				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should checkConnectionFinality error.",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				// Error
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubInvalidQueryConnectionResponse, nil).AnyTimes()
				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(invalidPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubUninitializedQueryConnectionResponse, nil).AnyTimes()
				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should QueryClientStatePair error.",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				// Error
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubInitQueryConnectionResponse, nil).AnyTimes()
				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(invalidPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				// Error
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubInitQueryConnectionResponse, nil).AnyTimes()
				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return UNINITIALIZED RelayMsgs.",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubUninitializedQueryConnectionResponse, nil).AnyTimes()
				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubUninitializedQueryConnectionResponse, nil).AnyTimes()
				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
			},
			want: &RelayMsgs{
				Src: []sdk.Msg{
					srcPath.ConnInit(dstPath, stubAccAddress),
				},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			wantErr: false,
		},
		// 以下、Prove Mock 内で Proof が取得できずのため実施不可
		//{
		//	name: "should return Init RelayMsgs.",
		//	prepareMock: func() {
		//		srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
		//		srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
		//		dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
		//		dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
		//
		//		srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
		//		dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
		//
		//		mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
		//		mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
		//		mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
		//		mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
		//		mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
		//		mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//
		//		srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
		//		srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
		//		srcMockChain.EXPECT().Codec().Return(stubCodecs).AnyTimes()
		//		srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
		//		srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
		//		srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubUninitializedQueryConnectionResponse, nil).AnyTimes()
		//		srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()
		//		srcMockChain.EXPECT().QueryClientState(gomock.Any()).Return(stubQueryClientStateResponse, nil).AnyTimes()
		//
		//		dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
		//		dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
		//		dstMockChain.EXPECT().Codec().Return(stubCodecs).AnyTimes()
		//		dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
		//		dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
		//		dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubInitQueryConnectionResponse, nil).AnyTimes()
		//		dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()
		//		dstMockChain.EXPECT().QueryClientState(gomock.Any()).Return(stubQueryClientStateResponse, nil).AnyTimes()
		//
		//		srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
		//		srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		srcMockProver.EXPECT().ProveState(srcMockQueryContext.Context(), gomock.Any(), gomock.Any()).Return(nil, stubHeight, nil).AnyTimes()
		//
		//		dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		//		dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
		//		dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
		//		dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		//		dstMockProver.EXPECT().ProveState(dstMockQueryContext.Context(), gomock.Any(), gomock.Any()).Return(nil, stubHeight, nil).AnyTimes()
		//	},
		//	args: args{
		//		src: &ProvableChain{
		//			Chain:  srcMockChain,
		//			Prover: srcMockProver,
		//		},
		//		dst: &ProvableChain{
		//			Chain:  dstMockChain,
		//			Prover: dstMockProver,
		//		},
		//	},
		//	want: &RelayMsgs{
		//		Src: []sdk.Msg{
		//			srcPath.ConnInit(dstPath, stubAccAddress),
		//		},
		//		Dst:          []sdk.Msg{},
		//		MaxTxSize:    0,
		//		MaxMsgLength: 0,
		//		Last:         false,
		//		Succeeded:    false,
		//		SrcMsgIDs:    nil,
		//		DstMsgIDs:    nil,
		//	},
		//	wantErr: false,
		//},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			got, err := createConnectionStep(tt.args.src, tt.args.dst)
			if (err != nil) != tt.wantErr {
				t.Errorf("createConnectionStep() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("createConnectionStep() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("createConnectionStep() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

func Test_validatePaths(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcMockChain := NewMockChain(ctrl)
	dstMockChain := NewMockChain(ctrl)
	//srcMockProver := NewMockProver(ctrl)
	//dstMockProver := NewMockProver(ctrl)
	//srcMockQueryContext := NewMockQueryContext(ctrl)
	//dstMockQueryContext := NewMockQueryContext(ctrl)
	//srcMockHeader := NewMockHeader(ctrl)
	//dstMockHeader := NewMockHeader(ctrl)
	//srcMockHeaders := []Header{srcMockHeader}
	//dstMockHeaders := []Header{dstMockHeader}
	//mockSyncHeaders := NewMockSyncHeaders(ctrl)

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	invalidPath := &PathEnd{
		ChainID:      "",
		ClientID:     "",
		ConnectionID: "",
		ChannelID:    "",
		PortID:       "",
		Order:        "",
		Version:      "",
	}

	type args struct {
		src Chain
		dst Chain
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		wantErr     bool
	}{
		{
			name: "should Src chain validate error .",
			prepareMock: func() {
				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(invalidPath).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
			},
			args: args{
				src: srcMockChain,
				dst: dstMockChain,
			},
			wantErr: true,
		},
		{
			name: "should Dst chain validate error .",
			prepareMock: func() {
				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(invalidPath).AnyTimes()
			},
			args: args{
				src: srcMockChain,
				dst: dstMockChain,
			},
			wantErr: true,
		},
		{
			name: "should takes two chains and validates their paths.",
			prepareMock: func() {
				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
			},
			args: args{
				src: srcMockChain,
				dst: dstMockChain,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			if err := validatePaths(tt.args.src, tt.args.dst); (err != nil) != tt.wantErr {
				t.Errorf("validatePaths() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_logConnectionStates(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcMockChain := NewMockChain(ctrl)
	dstMockChain := NewMockChain(ctrl)

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	type args struct {
		src     Chain
		dst     Chain
		srcConn *types2.QueryConnectionResponse
		dstConn *types2.QueryConnectionResponse
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
	}{
		{
			name: "should logging ConnectionStates.",
			prepareMock: func() {
				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()

			},
			args: args{
				src: srcMockChain,
				dst: dstMockChain,
				srcConn: &types2.QueryConnectionResponse{
					Connection: &types2.ConnectionEnd{
						ClientId: "hb-ibft2-0",
						Versions: nil,
						State:    0,
						Counterparty: types2.Counterparty{
							ClientId:     "hb-ibft2-0",
							ConnectionId: "connection-0",
							Prefix: commitmenttypes.MerklePrefix{
								KeyPrefix: []byte("ibc"),
							},
						},
						DelayPeriod: 0,
					},
					Proof: nil,
					ProofHeight: clienttypes.Height{
						RevisionNumber: 0,
						RevisionHeight: 100,
					},
				},
				dstConn: &types2.QueryConnectionResponse{
					Connection: &types2.ConnectionEnd{
						ClientId: "hb-ibft2-0",
						Versions: nil,
						State:    0,
						Counterparty: types2.Counterparty{
							ClientId:     "hb-ibft2-0",
							ConnectionId: "connection-0",
							Prefix: commitmenttypes.MerklePrefix{
								KeyPrefix: []byte("ibc"),
							},
						},
						DelayPeriod: 0,
					},
					Proof: nil,
					ProofHeight: clienttypes.Height{
						RevisionNumber: 0,
						RevisionHeight: 0,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			logConnectionStates(tt.args.src, tt.args.dst, tt.args.srcConn, tt.args.dstConn)
		})
	}
}

func Test_mustGetHeight(t *testing.T) {
	type args struct {
		h ibcexported.Height
	}
	tests := []struct {
		name string
		args args
		want uint64
	}{
		{
			name: "should get height from chain",
			args: args{
				h: clienttypes.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
			},
			want: 100,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := mustGetHeight(tt.args.h); got != tt.want {
				t.Errorf("mustGetHeight() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_mustGetAddress(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcMockChain := NewMockChain(ctrl)

	srcChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	var stubAccAddress = sdk.AccAddress{}

	type args struct {
		chain interface {
			GetAddress() (sdk.AccAddress, error)
		}
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		want        sdk.AccAddress
	}{
		{
			name: "should get address from chain",
			prepareMock: func() {
				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
			},
			args: args{
				chain: srcMockChain,
			},
			want: sdk.AccAddress{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			if got := mustGetAddress(tt.args.chain); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("mustGetAddress() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_checkConnectionFinality(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcMockChain := NewMockChain(ctrl)
	dstMockChain := NewMockChain(ctrl)
	srcMockProver := NewMockProver(ctrl)
	dstMockProver := NewMockProver(ctrl)
	srcMockQueryContext := NewMockQueryContext(ctrl)
	dstMockQueryContext := NewMockQueryContext(ctrl)
	srcMockHeader := NewMockHeader(ctrl)
	dstMockHeader := NewMockHeader(ctrl)
	srcMockHeaders := []Header{srcMockHeader}
	dstMockHeaders := []Header{dstMockHeader}
	mockSyncHeaders := NewMockSyncHeaders(ctrl)

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	var stubAccAddress = sdk.AccAddress{}
	var stubHeight = &clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}
	var stubQueryConnectionResponse = &types2.QueryConnectionResponse{
		Connection: &types2.ConnectionEnd{
			ClientId: "hb-ibft2-0",
			Versions: make([]*types2.Version, 0),
			State:    types2.UNINITIALIZED,
			Counterparty: types2.Counterparty{
				ClientId:     "hb-ibft2-0",
				ConnectionId: connectionID,
				Prefix:       DefaultChainPrefix,
			},
			DelayPeriod: DefaultDelayPeriod,
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}
	var srcStubMsgID = make([]MsgID, 1)
	var dstStubMsgID = make([]MsgID, 1)

	type args struct {
		src           *ProvableChain
		dst           *ProvableChain
		srcConnection *types2.ConnectionEnd
		dstConnection *types2.ConnectionEnd
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		want        bool
		wantErr     bool
	}{
		// TODO: Add test cases.
		{
			name: "should check connection finality",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
				srcConnection: &types2.ConnectionEnd{
					ClientId: "hb-ibft2-0",
					Versions: nil,
					State:    0,
					Counterparty: types2.Counterparty{
						ClientId:     "hb-ibft2-0",
						ConnectionId: "connection-0",
						Prefix: commitmenttypes.MerklePrefix{
							KeyPrefix: []byte("ibc"),
						},
					},
					DelayPeriod: 0,
				},
				dstConnection: &types2.ConnectionEnd{
					ClientId: "hb-ibft2-0",
					Versions: nil,
					State:    0,
					Counterparty: types2.Counterparty{
						ClientId:     "hb-ibft2-0",
						ConnectionId: "connection-0",
						Prefix: commitmenttypes.MerklePrefix{
							KeyPrefix: []byte("ibc"),
						},
					},
					DelayPeriod: 0,
				},
			},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			got, err := checkConnectionFinality(tt.args.src, tt.args.dst, tt.args.srcConnection, tt.args.dstConnection)
			if (err != nil) != tt.wantErr {
				t.Errorf("checkConnectionFinality() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("checkConnectionFinality() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("checkConnectionFinality() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

func TestGetConnectionPairLogger(t *testing.T) {
	type args struct {
		src Chain
		dst Chain
	}
	tests := []struct {
		name string
		args args
		want *log.DcjpyLogger
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetConnectionPairLogger(tt.args.src, tt.args.dst); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetConnectionPairLogger() = %v, want %v", got, tt.want)
			}
		})
	}
}

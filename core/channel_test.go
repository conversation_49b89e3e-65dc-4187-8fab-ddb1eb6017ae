//go:generate mockgen -source=$GOFILE -package=core -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package core

import (
	"reflect"
	"testing"
	"time"

	sdk "github.com/cosmos/cosmos-sdk/types"
	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	types2 "github.com/cosmos/ibc-go/v7/modules/core/03-connection/types"
	types3 "github.com/cosmos/ibc-go/v7/modules/core/04-channel/types"
	"github.com/decurret-lab/dcbg-dcjpy-relayer/log"
	"github.com/google/go-cmp/cmp"
	gomock "go.uber.org/mock/gomock"
	"golang.org/x/exp/slog"
)

// TODO State が変更されず無限ループとなるのでテスト不可
func TestCreateChannel(t *testing.T) {
	t.Skip("cannot not possible because the state remains unchanged, resulting in an infinite loop..")

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcMockChain := NewMockChain(ctrl)
	dstMockChain := NewMockChain(ctrl)
	srcMockProver := NewMockProver(ctrl)
	dstMockProver := NewMockProver(ctrl)
	srcMockQueryContext := NewMockQueryContext(ctrl)
	dstMockQueryContext := NewMockQueryContext(ctrl)
	srcMockHeader := NewMockHeader(ctrl)
	dstMockHeader := NewMockHeader(ctrl)
	srcMockHeaders := []Header{srcMockHeader}
	dstMockHeaders := []Header{dstMockHeader}
	mockSyncHeaders := NewMockSyncHeaders(ctrl)

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	var stubAccAddress = sdk.AccAddress{}
	var stubHeight = &clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}
	var stubQueryConnectionResponse = &types2.QueryConnectionResponse{
		Connection: &types2.ConnectionEnd{
			ClientId: "hb-ibft2-0",
			Versions: make([]*types2.Version, 0),
			State:    types2.UNINITIALIZED,
			Counterparty: types2.Counterparty{
				ClientId:     "hb-ibft2-0",
				ConnectionId: connectionID,
				Prefix:       DefaultChainPrefix,
			},
			DelayPeriod: DefaultDelayPeriod,
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}
	var stubQueryChannelResponse = &types3.QueryChannelResponse{
		Channel: &types3.Channel{
			State:    0,
			Ordering: 0,
			Counterparty: types3.Counterparty{
				PortId:    "account-sync",
				ChannelId: "channel-0",
			},
			ConnectionHops: nil,
			Version:        "account-sync-0",
		},
		Proof: nil,
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}

	var srcStubMsgID = make([]MsgID, 1)
	var dstStubMsgID = make([]MsgID, 1)

	type args struct {
		pathName string
		src      *ProvableChain
		dst      *ProvableChain
		to       time.Duration
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		wantErr     bool
	}{
		{
			name: "should runs the channel creation messages on timeout until they pass.",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()

				srcMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubQueryChannelResponse, nil).AnyTimes()

				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()

				dstMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubQueryChannelResponse, nil).AnyTimes()

				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				pathName: "account-sync",
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
				to: 10,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt.prepareMock()
		t.Run(tt.name, func(t *testing.T) {
			if err := CreateChannel(tt.args.pathName, tt.args.src, tt.args.dst, tt.args.to); (err != nil) != tt.wantErr {
				t.Errorf("CreateChannel() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_createChannelStep(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcMockChain := NewMockChain(ctrl)
	dstMockChain := NewMockChain(ctrl)
	srcMockProver := NewMockProver(ctrl)
	dstMockProver := NewMockProver(ctrl)
	srcMockQueryContext := NewMockQueryContext(ctrl)
	dstMockQueryContext := NewMockQueryContext(ctrl)
	srcMockHeader := NewMockHeader(ctrl)
	dstMockHeader := NewMockHeader(ctrl)
	srcMockHeaders := []Header{srcMockHeader}
	dstMockHeaders := []Header{dstMockHeader}
	mockSyncHeaders := NewMockSyncHeaders(ctrl)

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	invalidPath := &PathEnd{
		ChainID:      "",
		ClientID:     "",
		ConnectionID: "",
		ChannelID:    "",
		PortID:       "",
		Order:        "",
		Version:      "",
	}

	var stubAccAddress = sdk.AccAddress{}
	var stubHeight = &clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}
	var stubQueryConnectionResponse = &types2.QueryConnectionResponse{
		Connection: &types2.ConnectionEnd{
			ClientId: "hb-ibft2-0",
			Versions: make([]*types2.Version, 0),
			State:    types2.UNINITIALIZED,
			Counterparty: types2.Counterparty{
				ClientId:     "hb-ibft2-0",
				ConnectionId: connectionID,
				Prefix:       DefaultChainPrefix,
			},
			DelayPeriod: DefaultDelayPeriod,
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}
	var stubUninitializedQueryChannelResponse = &types3.QueryChannelResponse{
		Channel: &types3.Channel{
			State:    types3.UNINITIALIZED,
			Ordering: 0,
			Counterparty: types3.Counterparty{
				PortId:    "account-sync",
				ChannelId: "channel-0",
			},
			ConnectionHops: nil,
			Version:        "account-sync-0",
		},
		Proof: nil,
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}
	//var stubInitQueryChannelResponse = &types3.QueryChannelResponse{
	//	Channel: &types3.Channel{
	//		State:    types3.INIT,
	//		Ordering: 0,
	//		Counterparty: types3.Counterparty{
	//			PortId:    "account-sync",
	//			ChannelId: "channel-0",
	//		},
	//		ConnectionHops: nil,
	//		Version:        "account-sync-0",
	//	},
	//	Proof: nil,
	//	ProofHeight: clienttypes.Height{
	//		RevisionNumber: 0,
	//		RevisionHeight: 100,
	//	},
	//}
	var stubInvalidQueryChannelResponse = &types3.QueryChannelResponse{
		Channel: &types3.Channel{
			State:    0,
			Ordering: 0,
			Counterparty: types3.Counterparty{
				PortId:    "",
				ChannelId: "",
			},
			ConnectionHops: nil,
			Version:        "",
		},
		Proof: nil,
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 0,
		},
	}

	var srcStubMsgID = make([]MsgID, 1)
	var dstStubMsgID = make([]MsgID, 1)

	type args struct {
		src *ProvableChain
		dst *ProvableChain
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		want        *RelayMsgs
		wantErr     bool
	}{
		{
			name: "should Src validatePaths error.",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(invalidPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()

				srcMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubUninitializedQueryChannelResponse, nil).AnyTimes()

				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()

				dstMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubUninitializedQueryChannelResponse, nil).AnyTimes()

				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should NewSyncHeaders error.",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				srcMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubUninitializedQueryChannelResponse, nil).AnyTimes()
				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				// Error
				dstMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()

				dstMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubUninitializedQueryChannelResponse, nil).AnyTimes()

				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should error updating headers.",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()

				srcMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubUninitializedQueryChannelResponse, nil).AnyTimes()
				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()
				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				dstMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubUninitializedQueryChannelResponse, nil).AnyTimes()
				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				// Error
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(nil, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should QueryConnectionPair error.",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				// Error
				srcMockChain.EXPECT().QueryChannel(gomock.Any()).Return(nil, nil).AnyTimes()
				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				dstMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubUninitializedQueryChannelResponse, nil).AnyTimes()
				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should checkConnectionFinality error.",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				// Error
				srcMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubInvalidQueryChannelResponse, nil).AnyTimes()
				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				dstMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubUninitializedQueryChannelResponse, nil).AnyTimes()
				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should runs the channel creation messages on timeout until they pass.",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				srcMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubUninitializedQueryChannelResponse, nil).AnyTimes()
				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				dstMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubUninitializedQueryChannelResponse, nil).AnyTimes()
				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
			},
			want: &RelayMsgs{
				Src: []sdk.Msg{
					srcPath.ChanInit(dstPath, stubAccAddress),
				},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			wantErr: false,
		},
		// INIT以降は、Prove Mock 内で Proof が取得できずのため実施不可
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			got, err := createChannelStep(tt.args.src, tt.args.dst)
			if (err != nil) != tt.wantErr {
				t.Errorf("createChannelStep() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("createChannelStep() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("createChannelStep() differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

func Test_logChannelStates(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcMockChain := NewMockChain(ctrl)
	dstMockChain := NewMockChain(ctrl)
	srcMockProver := NewMockProver(ctrl)
	dstMockProver := NewMockProver(ctrl)
	srcMockQueryContext := NewMockQueryContext(ctrl)
	dstMockQueryContext := NewMockQueryContext(ctrl)
	srcMockHeader := NewMockHeader(ctrl)
	dstMockHeader := NewMockHeader(ctrl)
	srcMockHeaders := []Header{srcMockHeader}
	dstMockHeaders := []Header{dstMockHeader}
	mockSyncHeaders := NewMockSyncHeaders(ctrl)

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	var stubAccAddress = sdk.AccAddress{}
	var stubHeight = &clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}
	var stubQueryConnectionResponse = &types2.QueryConnectionResponse{
		Connection: &types2.ConnectionEnd{
			ClientId: "hb-ibft2-0",
			Versions: make([]*types2.Version, 0),
			State:    types2.UNINITIALIZED,
			Counterparty: types2.Counterparty{
				ClientId:     "hb-ibft2-0",
				ConnectionId: connectionID,
				Prefix:       DefaultChainPrefix,
			},
			DelayPeriod: DefaultDelayPeriod,
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}
	var stubUninitializedQueryChannelResponse = &types3.QueryChannelResponse{
		Channel: &types3.Channel{
			State:    types3.UNINITIALIZED,
			Ordering: 0,
			Counterparty: types3.Counterparty{
				PortId:    "account-sync",
				ChannelId: "channel-0",
			},
			ConnectionHops: nil,
			Version:        "account-sync-0",
		},
		Proof: nil,
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}

	var srcStubMsgID = make([]MsgID, 1)
	var dstStubMsgID = make([]MsgID, 1)

	type args struct {
		src     *ProvableChain
		dst     *ProvableChain
		srcChan *types3.QueryChannelResponse
		dstChan *types3.QueryChannelResponse
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
	}{
		{
			name: "should logging ChannelStates.",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				srcMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubUninitializedQueryChannelResponse, nil).AnyTimes()
				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				dstMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubUninitializedQueryChannelResponse, nil).AnyTimes()
				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
				srcChan: &types3.QueryChannelResponse{
					Channel: &types3.Channel{
						State:    types3.UNINITIALIZED,
						Ordering: 0,
						Counterparty: types3.Counterparty{
							PortId:    "account-sync",
							ChannelId: "channel-0",
						},
						ConnectionHops: nil,
						Version:        "account-sync-0",
					},
					Proof: nil,
					ProofHeight: clienttypes.Height{
						RevisionNumber: 0,
						RevisionHeight: 0,
					},
				},
				dstChan: &types3.QueryChannelResponse{
					Channel: &types3.Channel{
						State:    types3.UNINITIALIZED,
						Ordering: 0,
						Counterparty: types3.Counterparty{
							PortId:    "account-sync",
							ChannelId: "channel-0",
						},
						ConnectionHops: nil,
						Version:        "account-sync-0",
					},
					Proof: nil,
					ProofHeight: clienttypes.Height{
						RevisionNumber: 0,
						RevisionHeight: 0,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			logChannelStates(tt.args.src, tt.args.dst, tt.args.srcChan, tt.args.dstChan)
		})
	}
}

func Test_checkChannelFinality(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcMockChain := NewMockChain(ctrl)
	dstMockChain := NewMockChain(ctrl)
	srcMockProver := NewMockProver(ctrl)
	dstMockProver := NewMockProver(ctrl)
	srcMockQueryContext := NewMockQueryContext(ctrl)
	dstMockQueryContext := NewMockQueryContext(ctrl)
	srcMockHeader := NewMockHeader(ctrl)
	dstMockHeader := NewMockHeader(ctrl)
	srcMockHeaders := []Header{srcMockHeader}
	dstMockHeaders := []Header{dstMockHeader}
	mockSyncHeaders := NewMockSyncHeaders(ctrl)

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	var stubAccAddress = sdk.AccAddress{}
	var stubHeight = &clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}
	var stubQueryConnectionResponse = &types2.QueryConnectionResponse{
		Connection: &types2.ConnectionEnd{
			ClientId: "hb-ibft2-0",
			Versions: make([]*types2.Version, 0),
			State:    types2.UNINITIALIZED,
			Counterparty: types2.Counterparty{
				ClientId:     "hb-ibft2-0",
				ConnectionId: connectionID,
				Prefix:       DefaultChainPrefix,
			},
			DelayPeriod: DefaultDelayPeriod,
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}
	var stubUninitializedQueryChannelResponse = &types3.QueryChannelResponse{
		Channel: &types3.Channel{
			State:    types3.UNINITIALIZED,
			Ordering: 0,
			Counterparty: types3.Counterparty{
				PortId:    "account-sync",
				ChannelId: "channel-0",
			},
			ConnectionHops: nil,
			Version:        "account-sync-0",
		},
		Proof: nil,
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}

	var srcStubMsgID = make([]MsgID, 1)
	var dstStubMsgID = make([]MsgID, 1)

	type args struct {
		src        *ProvableChain
		dst        *ProvableChain
		srcChannel *types3.Channel
		dstChannel *types3.Channel
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		want        bool
		wantErr     bool
	}{
		{
			name: "should checkChannelFinality.",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				srcMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubUninitializedQueryChannelResponse, nil).AnyTimes()
				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				dstMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubUninitializedQueryChannelResponse, nil).AnyTimes()
				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  srcMockChain,
					Prover: srcMockProver,
				},
				dst: &ProvableChain{
					Chain:  dstMockChain,
					Prover: dstMockProver,
				},
				srcChannel: &types3.Channel{
					State:    types3.UNINITIALIZED,
					Ordering: 0,
					Counterparty: types3.Counterparty{
						PortId:    "account-sync",
						ChannelId: "channel-0",
					},
					ConnectionHops: nil,
					Version:        "account-sync-0",
				},
				dstChannel: &types3.Channel{
					State:    types3.UNINITIALIZED,
					Ordering: 0,
					Counterparty: types3.Counterparty{
						PortId:    "account-sync",
						ChannelId: "channel-0",
					},
					ConnectionHops: nil,
					Version:        "account-sync-0",
				},
			},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			got, err := checkChannelFinality(tt.args.src, tt.args.dst, tt.args.srcChannel, tt.args.dstChannel)
			if (err != nil) != tt.wantErr {
				t.Errorf("checkChannelFinality() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("checkChannelFinality() = %v, want %v", got, tt.want)
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("checkChannelFinality differs: (-got +want)n%s", diff)
				}
			}
		})
	}
}

func TestGetChannelLogger(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcMockChain := NewMockChain(ctrl)
	dstMockChain := NewMockChain(ctrl)
	srcMockProver := NewMockProver(ctrl)
	dstMockProver := NewMockProver(ctrl)
	srcMockQueryContext := NewMockQueryContext(ctrl)
	dstMockQueryContext := NewMockQueryContext(ctrl)
	srcMockHeader := NewMockHeader(ctrl)
	dstMockHeader := NewMockHeader(ctrl)
	srcMockHeaders := []Header{srcMockHeader}
	dstMockHeaders := []Header{dstMockHeader}
	mockSyncHeaders := NewMockSyncHeaders(ctrl)

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	var stubAccAddress = sdk.AccAddress{}
	var stubHeight = &clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}
	var stubQueryConnectionResponse = &types2.QueryConnectionResponse{
		Connection: &types2.ConnectionEnd{
			ClientId: "hb-ibft2-0",
			Versions: make([]*types2.Version, 0),
			State:    types2.UNINITIALIZED,
			Counterparty: types2.Counterparty{
				ClientId:     "hb-ibft2-0",
				ConnectionId: connectionID,
				Prefix:       DefaultChainPrefix,
			},
			DelayPeriod: DefaultDelayPeriod,
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}
	var stubUninitializedQueryChannelResponse = &types3.QueryChannelResponse{
		Channel: &types3.Channel{
			State:    types3.UNINITIALIZED,
			Ordering: 0,
			Counterparty: types3.Counterparty{
				PortId:    "account-sync",
				ChannelId: "channel-0",
			},
			ConnectionHops: nil,
			Version:        "account-sync-0",
		},
		Proof: nil,
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}

	var srcStubMsgID = make([]MsgID, 1)
	var dstStubMsgID = make([]MsgID, 1)

	type args struct {
		c Chain
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		want        *log.DcjpyLogger
	}{
		{
			name: "should get channel logger",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				srcMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubUninitializedQueryChannelResponse, nil).AnyTimes()
				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				dstMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubUninitializedQueryChannelResponse, nil).AnyTimes()
				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				c: srcMockChain,
			},
			want: &log.DcjpyLogger{
				Logger: &slog.Logger{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			got := GetChannelLogger(tt.args.c)
			if got == nil {
				t.Errorf("GetChannelLogger() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetChannelPairLogger(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcMockChain := NewMockChain(ctrl)
	dstMockChain := NewMockChain(ctrl)
	srcMockProver := NewMockProver(ctrl)
	dstMockProver := NewMockProver(ctrl)
	srcMockQueryContext := NewMockQueryContext(ctrl)
	dstMockQueryContext := NewMockQueryContext(ctrl)
	srcMockHeader := NewMockHeader(ctrl)
	dstMockHeader := NewMockHeader(ctrl)
	srcMockHeaders := []Header{srcMockHeader}
	dstMockHeaders := []Header{dstMockHeader}
	mockSyncHeaders := NewMockSyncHeaders(ctrl)

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	var stubAccAddress = sdk.AccAddress{}
	var stubHeight = &clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}
	var stubQueryConnectionResponse = &types2.QueryConnectionResponse{
		Connection: &types2.ConnectionEnd{
			ClientId: "hb-ibft2-0",
			Versions: make([]*types2.Version, 0),
			State:    types2.UNINITIALIZED,
			Counterparty: types2.Counterparty{
				ClientId:     "hb-ibft2-0",
				ConnectionId: connectionID,
				Prefix:       DefaultChainPrefix,
			},
			DelayPeriod: DefaultDelayPeriod,
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}
	var stubUninitializedQueryChannelResponse = &types3.QueryChannelResponse{
		Channel: &types3.Channel{
			State:    types3.UNINITIALIZED,
			Ordering: 0,
			Counterparty: types3.Counterparty{
				PortId:    "account-sync",
				ChannelId: "channel-0",
			},
			ConnectionHops: nil,
			Version:        "account-sync-0",
		},
		Proof: nil,
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}

	var srcStubMsgID = make([]MsgID, 1)
	var dstStubMsgID = make([]MsgID, 1)

	type args struct {
		src Chain
		dst Chain
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		want        *log.DcjpyLogger
	}{
		{
			name: "should return ChannelPairLogger.",
			prepareMock: func() {
				srcMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				srcMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()
				dstMockQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				dstMockQueryContext.EXPECT().Height().Return(stubHeight).AnyTimes()

				srcMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()
				dstMockHeader.EXPECT().GetHeight().Return(stubHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(srcMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(dstMockQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupBothHeadersForUpdate(gomock.Any(), gomock.Any()).Return(srcMockHeaders, dstMockHeaders, nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				srcMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				srcMockChain.EXPECT().Path().Return(srcPath).AnyTimes()
				srcMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				srcMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				srcMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				srcMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubUninitializedQueryChannelResponse, nil).AnyTimes()
				srcMockChain.EXPECT().SendMsgs(gomock.Any()).Return(srcStubMsgID, nil).AnyTimes()

				dstMockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				dstMockChain.EXPECT().Path().Return(dstPath).AnyTimes()
				dstMockChain.EXPECT().GetAddress().Return(stubAccAddress, nil).AnyTimes()
				dstMockChain.EXPECT().LatestHeight().Return(stubHeight, nil).AnyTimes()
				dstMockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockChain.EXPECT().QueryConnection(gomock.Any()).Return(stubQueryConnectionResponse, nil).AnyTimes()
				dstMockChain.EXPECT().QueryChannel(gomock.Any()).Return(stubUninitializedQueryChannelResponse, nil).AnyTimes()
				dstMockChain.EXPECT().SendMsgs(gomock.Any()).Return(dstStubMsgID, nil).AnyTimes()

				srcMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				srcMockProver.EXPECT().GetLatestFinalizedHeader().Return(srcMockHeader, nil).AnyTimes()
				srcMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				dstMockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				dstMockProver.EXPECT().GetLatestFinalizedHeader().Return(dstMockHeader, nil).AnyTimes()
				dstMockProver.EXPECT().SetupHeadersForUpdate(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: srcMockChain,
				dst: dstMockChain,
			},
			want: &log.DcjpyLogger{
				Logger: &slog.Logger{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			got := GetChannelPairLogger(tt.args.src, tt.args.dst)
			if got == nil {
				t.Errorf("GetChannelPairLogger() = %v, want %v", got, tt.want)
			}
		})
	}
}

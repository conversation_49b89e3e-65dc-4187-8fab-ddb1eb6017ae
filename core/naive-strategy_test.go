//go:generate mockgen -source=$GOFILE -package=core -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package core

import (
	"context"
	"reflect"
	"testing"

	sdk "github.com/cosmos/cosmos-sdk/types"
	"github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	chantypes "github.com/cosmos/ibc-go/v7/modules/core/04-channel/types"
	host "github.com/cosmos/ibc-go/v7/modules/core/24-host"
	"github.com/google/go-cmp/cmp"
	"go.uber.org/mock/gomock"
)

func TestNewNaiveStrategy(t *testing.T) {
	type args struct {
		srcNoAck bool
		dstNoAck bool
	}
	tests := []struct {
		name string
		args args
		want *NaiveStrategy
	}{
		{
			name: "should returns NaiveStrategy with NoAck false.",
			args: args{
				srcNoAck: false,
				dstNoAck: false,
			},
			want: &NaiveStrategy{
				srcNoAck: false,
				dstNoAck: false,
			},
		},
		{
			name: "should returns NaiveStrategy with NoAck true.",
			args: args{
				srcNoAck: true,
				dstNoAck: true,
			},
			want: &NaiveStrategy{
				srcNoAck: true,
				dstNoAck: true,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewNaiveStrategy(tt.args.srcNoAck, tt.args.dstNoAck); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewNaiveStrategy() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNaiveStrategy_GetType(t *testing.T) {
	type fields struct {
		Ordered      bool
		MaxTxSize    uint64
		MaxMsgLength uint64
		srcNoAck     bool
		dstNoAck     bool
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "should return the type when NaiveStrategy's NoAck is false.",
			fields: fields{
				srcNoAck: false,
				dstNoAck: false,
			},
			want: "naive",
		},
		{
			name: "should return the type when NaiveStrategy's NoAck is true.",
			fields: fields{
				srcNoAck: true,
				dstNoAck: true,
			},
			want: "naive",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &NaiveStrategy{
				Ordered:      tt.fields.Ordered,
				MaxTxSize:    tt.fields.MaxTxSize,
				MaxMsgLength: tt.fields.MaxMsgLength,
				srcNoAck:     tt.fields.srcNoAck,
				dstNoAck:     tt.fields.dstNoAck,
			}
			if got := st.GetType(); got != tt.want {
				t.Errorf("NaiveStrategy.GetType() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNaiveStrategy_SetupRelay(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockDstChain := NewMockChain(ctrl)
	mockSrcProver := NewMockProver(ctrl)
	mockDstProver := NewMockProver(ctrl)

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
	mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
	mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()

	mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
	mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
	mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()

	mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()

	mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()

	type fields struct {
		srcNoAck bool
		dstNoAck bool
	}
	type args struct {
		ctx context.Context
		src *ProvableChain
		dst *ProvableChain
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "Should performs chain-specific setup before starting the relay",
			fields: fields{
				srcNoAck: false,
				dstNoAck: false,
			},
			args: args{
				ctx: nil,
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			st := &NaiveStrategy{
				srcNoAck: tt.fields.srcNoAck,
				dstNoAck: tt.fields.dstNoAck,
			}
			if err := st.SetupRelay(tt.args.ctx, tt.args.src, tt.args.dst); (err != nil) != tt.wantErr {
				t.Errorf("NaiveStrategy.SetupRelay() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_getQueryContext(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockChain := NewMockChain(ctrl)
	mockProver := NewMockProver(ctrl)
	mockSyncHeaders := NewMockSyncHeaders(ctrl)
	mockQueryContextTrue := NewMockQueryContext(ctrl)
	//mockQueryContextFalse := NewMockQueryContext(ctrl)

	srcChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	wantHeight := &clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}

	mockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
	mockChain.EXPECT().Path().Return(srcPath).AnyTimes()
	mockChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
	mockChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()

	mockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()

	mockQueryContextTrue.EXPECT().Context().Return(nil).AnyTimes()
	mockQueryContextTrue.EXPECT().Height().Return(wantHeight).AnyTimes()
	mockSyncHeaders.EXPECT().GetQueryContext(srcChainID).Return(mockQueryContextTrue).AnyTimes()
	mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcChainID).Return(nil).AnyTimes()

	//mockQueryContextFalse.EXPECT().Context().Return(nil).AnyTimes()
	//mockQueryContextFalse.EXPECT().Height().Return(wantHeight).AnyTimes()

	type args struct {
		chain              *ProvableChain
		sh                 SyncHeaders
		useFinalizedHeader bool
	}
	tests := []struct {
		name    string
		args    args
		want    QueryContext
		wantErr bool
	}{
		{
			name: "Should return the query context when useFinalizedHeader is true.",
			args: args{
				chain: &ProvableChain{
					Chain:  mockChain,
					Prover: mockProver,
				},
				sh:                 mockSyncHeaders,
				useFinalizedHeader: true,
			},
			want:    mockQueryContextTrue,
			wantErr: false,
		},
		// TODO 実行結果の構造が変わるので保留
		//{
		//	name: "Should return the query context when useFinalizedHeader is false.",
		//	args: args{
		//		chain: &ProvableChain{
		//			Chain:  mockChain,
		//			Prover: mockProver,
		//		},
		//		sh:                 mockSyncHeaders,
		//		useFinalizedHeader: false,
		//	},
		//	want:    mockQueryContextFalse,
		//	wantErr: false,
		//},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := getQueryContext(tt.args.chain, tt.args.sh, tt.args.useFinalizedHeader)
			if (err != nil) != tt.wantErr {
				t.Errorf("getQueryContext() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getQueryContext() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNaiveStrategy_UnrelayedPackets(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockDstChain := NewMockChain(ctrl)
	mockSrcProver := NewMockProver(ctrl)
	mockDstProver := NewMockProver(ctrl)
	mockSyncHeaders := NewMockSyncHeaders(ctrl)
	mockSrcQueryContext := NewMockQueryContext(ctrl)
	mockDstQueryContext := NewMockQueryContext(ctrl)

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	wantHeight := &clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}

	srcPacketInfoList := PacketInfoList{
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{},
			EventHeight:     clienttypes.Height{},
		},
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           2,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{2},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{},
			EventHeight:     clienttypes.Height{},
		},
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           3,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{3},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{},
			EventHeight:     clienttypes.Height{},
		},
	}

	type fields struct {
		srcNoAck bool
		dstNoAck bool
	}
	type args struct {
		src                          *ProvableChain
		dst                          *ProvableChain
		sh                           SyncHeaders
		includeRelayedButUnfinalized bool
	}
	tests := []struct {
		name        string
		fields      fields
		prepareMock func()
		args        args
		want        *RelayPackets
		wantErr     bool
	}{
		{
			name: "If includeRelayedButUnfinalized is true, return the packets and heights that were sent but not received in the latest final block on the counterparty chain.",
			fields: fields{
				srcNoAck: false,
				dstNoAck: false,
			},
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(srcPacketInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				sh:                           mockSyncHeaders,
				includeRelayedButUnfinalized: true,
			},
			want: &RelayPackets{
				Src: srcPacketInfoList,
				Dst: nil,
			},
			wantErr: false,
		},
		{
			name: "Returns nothing if includeRelayedButUnfinalized is false.",
			fields: fields{
				srcNoAck: false,
				dstNoAck: false,
			},
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(srcPacketInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				sh:                           mockSyncHeaders,
				includeRelayedButUnfinalized: false,
			},
			want: &RelayPackets{
				Src: nil,
				Dst: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			st := &NaiveStrategy{
				srcNoAck: tt.fields.srcNoAck,
				dstNoAck: tt.fields.dstNoAck,
			}
			got, err := st.UnrelayedPackets(tt.args.src, tt.args.dst, tt.args.sh, tt.args.includeRelayedButUnfinalized)
			if (err != nil) != tt.wantErr {
				t.Errorf("NaiveStrategy.UnrelayedPackets() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("NaiveStrategy.UnrelayedPackets() differs: (-got +want)n%s", diff)
				}
				t.Errorf("NaiveStrategy.UnrelayedPackets() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNaiveStrategy_RelayPackets(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockDstChain := NewMockChain(ctrl)
	mockSrcProver := NewMockProver(ctrl)
	mockDstProver := NewMockProver(ctrl)
	mockSyncHeaders := NewMockSyncHeaders(ctrl)
	mockSrcQueryContext := NewMockQueryContext(ctrl)
	mockDstQueryContext := NewMockQueryContext(ctrl)
	mockCodec := MakeCodec()

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	wantHeight := clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}

	packetInfoList := PacketInfoList{
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{},
			EventHeight:     clienttypes.Height{},
		},
	}

	type fields struct {
		srcNoAck bool
		dstNoAck bool
	}
	type args struct {
		src               *ProvableChain
		dst               *ProvableChain
		rp                *RelayPackets
		sh                SyncHeaders
		doExecuteRelaySrc bool
		doExecuteRelayDst bool
	}
	tests := []struct {
		name        string
		fields      fields
		prepareMock func()
		args        args
		want        *RelayMsgs
		wantErr     bool
	}{
		{
			name: "Also does not create RelayPackets.",
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			fields: fields{
				srcNoAck: false,
				dstNoAck: false,
			},
			args: args{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				rp: &RelayPackets{
					Src: PacketInfoList{
						&PacketInfo{
							Packet: chantypes.Packet{
								Sequence:           1,
								SourcePort:         portID,
								SourceChannel:      channelID,
								DestinationPort:    portID,
								DestinationChannel: channelID,
								Data:               []byte{},
								TimeoutHeight: types.Height{
									RevisionNumber: 0,
									RevisionHeight: 100,
								},
								TimeoutTimestamp: 0,
							},
							Acknowledgement: []byte{},
							EventHeight:     clienttypes.Height{},
						},
						&PacketInfo{
							Packet: chantypes.Packet{
								Sequence:           2,
								SourcePort:         portID,
								SourceChannel:      channelID,
								DestinationPort:    portID,
								DestinationChannel: channelID,
								Data:               []byte{},
								TimeoutHeight: types.Height{
									RevisionNumber: 0,
									RevisionHeight: 100,
								},
								TimeoutTimestamp: 0,
							},
							Acknowledgement: []byte{},
							EventHeight:     clienttypes.Height{},
						},
						&PacketInfo{
							Packet: chantypes.Packet{
								Sequence:           3,
								SourcePort:         portID,
								SourceChannel:      channelID,
								DestinationPort:    portID,
								DestinationChannel: channelID,
								Data:               []byte{},
								TimeoutHeight: types.Height{
									RevisionNumber: 0,
									RevisionHeight: 100,
								},
								TimeoutTimestamp: 0,
							},
							Acknowledgement: []byte{},
							EventHeight:     clienttypes.Height{},
						},
					},
					Dst: PacketInfoList{},
				},
				sh:                mockSyncHeaders,
				doExecuteRelaySrc: false,
				doExecuteRelayDst: false,
			},
			want: &RelayMsgs{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			wantErr: false,
		},
		{
			name: "Create RelayPackets for SRC.",
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			fields: fields{
				srcNoAck: false,
				dstNoAck: false,
			},
			args: args{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				rp: &RelayPackets{
					Src: PacketInfoList{
						&PacketInfo{
							Packet: chantypes.Packet{
								Sequence:           1,
								SourcePort:         portID,
								SourceChannel:      channelID,
								DestinationPort:    portID,
								DestinationChannel: channelID,
								Data:               []byte{},
								TimeoutHeight: types.Height{
									RevisionNumber: 0,
									RevisionHeight: 100,
								},
								TimeoutTimestamp: 0,
							},
							Acknowledgement: []byte{},
							EventHeight:     clienttypes.Height{},
						},
						&PacketInfo{
							Packet: chantypes.Packet{
								Sequence:           2,
								SourcePort:         portID,
								SourceChannel:      channelID,
								DestinationPort:    portID,
								DestinationChannel: channelID,
								Data:               []byte{},
								TimeoutHeight: types.Height{
									RevisionNumber: 0,
									RevisionHeight: 100,
								},
								TimeoutTimestamp: 0,
							},
							Acknowledgement: []byte{},
							EventHeight:     clienttypes.Height{},
						},
						&PacketInfo{
							Packet: chantypes.Packet{
								Sequence:           3,
								SourcePort:         portID,
								SourceChannel:      channelID,
								DestinationPort:    portID,
								DestinationChannel: channelID,
								Data:               []byte{},
								TimeoutHeight: types.Height{
									RevisionNumber: 0,
									RevisionHeight: 100,
								},
								TimeoutTimestamp: 0,
							},
							Acknowledgement: []byte{},
							EventHeight:     clienttypes.Height{},
						},
					},
					Dst: PacketInfoList{},
				},
				sh:                mockSyncHeaders,
				doExecuteRelaySrc: true,
				doExecuteRelayDst: false,
			},
			want: &RelayMsgs{
				Src:          nil,
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			st := &NaiveStrategy{
				srcNoAck: tt.fields.srcNoAck,
				dstNoAck: tt.fields.dstNoAck,
			}
			got, err := st.RelayPackets(tt.args.src, tt.args.dst, tt.args.rp, tt.args.sh, tt.args.doExecuteRelaySrc, tt.args.doExecuteRelayDst)
			if (err != nil) != tt.wantErr {
				t.Errorf("NaiveStrategy.RelayPackets() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("NaiveStrategy.RelayPackets() differs: (-got +want)n%s", diff)
				}
				t.Errorf("NaiveStrategy.RelayPackets() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNaiveStrategy_UnrelayedAcknowledgements(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockDstChain := NewMockChain(ctrl)
	mockSrcProver := NewMockProver(ctrl)
	mockDstProver := NewMockProver(ctrl)
	mockSyncHeaders := NewMockSyncHeaders(ctrl)
	mockSrcQueryContext := NewMockQueryContext(ctrl)
	mockDstQueryContext := NewMockQueryContext(ctrl)
	mockCodec := MakeCodec()

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	wantHeight := clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}
	packetInfoList := PacketInfoList{
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{1},
			EventHeight:     clienttypes.Height{},
		},
	}

	type fields struct {
		srcNoAck bool
		dstNoAck bool
	}
	type args struct {
		src                          *ProvableChain
		dst                          *ProvableChain
		sh                           SyncHeaders
		includeRelayedButUnfinalized bool
	}
	tests := []struct {
		name        string
		fields      fields
		prepareMock func()
		args        args
		want        *RelayPackets
		wantErr     bool
	}{
		{
			name: "If includeRelayedButUnfinalized is true, return the packets and heights that were sent but not received in the latest final block on the counterparty chain.",
			fields: fields{
				srcNoAck: false,
				dstNoAck: false,
			},
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				sh:                           mockSyncHeaders,
				includeRelayedButUnfinalized: true,
			},
			want: &RelayPackets{
				Src: nil,
				Dst: nil,
			},
			wantErr: false,
		},
		{
			name: "Returns nothing if includeRelayedButUnfinalized is false.",
			fields: fields{
				srcNoAck: false,
				dstNoAck: false,
			},
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				sh:                           mockSyncHeaders,
				includeRelayedButUnfinalized: true,
			},
			want: &RelayPackets{
				Src: nil,
				Dst: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			st := &NaiveStrategy{
				srcNoAck: tt.fields.srcNoAck,
				dstNoAck: tt.fields.dstNoAck,
			}
			got, err := st.UnrelayedAcknowledgements(tt.args.src, tt.args.dst, tt.args.sh, tt.args.includeRelayedButUnfinalized)
			if (err != nil) != tt.wantErr {
				t.Errorf("NaiveStrategy.UnrelayedAcknowledgements() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("NaiveStrategy.UnrelayedAcknowledgements() differs: (-got +want)n%s", diff)
				}
				t.Errorf("NaiveStrategy.UnrelayedAcknowledgements() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_collectPackets(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockDstChain := NewMockChain(ctrl)
	mockSrcProver := NewMockProver(ctrl)
	mockDstProver := NewMockProver(ctrl)
	mockSyncHeaders := NewMockSyncHeaders(ctrl)
	mockSrcQueryContext := NewMockQueryContext(ctrl)
	mockDstQueryContext := NewMockQueryContext(ctrl)
	mockCodec := MakeCodec()

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	wantHeight := clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}

	packetInfoList := PacketInfoList{
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{},
			EventHeight:     clienttypes.Height{},
		},
	}

	packetCommitmentPath := host.PacketCommitmentPath(
		packetInfoList[0].SourcePort,
		packetInfoList[0].SourceChannel,
		packetInfoList[0].Sequence,
	)

	commitment := chantypes.CommitPacket(
		mockCodec,
		packetInfoList[0],
	)

	proof := []byte{1}

	type args struct {
		ctx     QueryContext
		chain   *ProvableChain
		packets PacketInfoList
		signer  sdk.AccAddress
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		want        []sdk.Msg
		wantErr     bool
	}{
		{
			name: "Collect packets to send.",
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, commitment).Return(proof, wantHeight, nil).AnyTimes()

				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, commitment).Return(proof, wantHeight, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			args: args{
				ctx: mockSrcQueryContext,
				chain: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				packets: PacketInfoList{
					&PacketInfo{
						Packet: chantypes.Packet{
							Sequence:           1,
							SourcePort:         portID,
							SourceChannel:      channelID,
							DestinationPort:    portID,
							DestinationChannel: channelID,
							Data:               []byte{1},
							TimeoutHeight: types.Height{
								RevisionNumber: 0,
								RevisionHeight: 100,
							},
							TimeoutTimestamp: 0,
						},
						Acknowledgement: []byte{},
						EventHeight:     clienttypes.Height{},
					},
				},
				signer: []byte{1},
			},
			want: []sdk.Msg{
				chantypes.NewMsgRecvPacket(
					packetInfoList[0].Packet,
					proof,
					wantHeight,
					"cosmos1qyfkm2y3",
				),
			},
			wantErr: false,
		},
		{
			name: "Packets to be sent are not collected.",
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, commitment).Return(proof, wantHeight, nil).AnyTimes()

				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, commitment).Return(proof, wantHeight, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			args: args{
				ctx: mockSrcQueryContext,
				chain: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				packets: PacketInfoList{},
				signer:  []byte{1},
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			got, err := collectPackets(tt.args.ctx, tt.args.chain, tt.args.packets, tt.args.signer)
			if (err != nil) != tt.wantErr {
				t.Errorf("collectPackets() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("collectPackets() differs: (-got +want)n%s", diff)
				}
				t.Errorf("collectPackets() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_logPacketsRelayed(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockDstChain := NewMockChain(ctrl)

	srcChainID := "********"
	dstChainID := "********"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-0",
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     "hb-ibft2-0",
		ConnectionID: "connection-0",
		ChannelID:    "channel-0",
		PortID:       "account-sync",
		Order:        "unordered",
		Version:      "account-sync-0",
	}

	type args struct {
		src Chain
		dst Chain
		num int
		obj string
		dir string
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
	}{
		{
			name: "should output log for PacketsRelayed",
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
			},
			args: args{
				src: mockSrcChain,
				dst: mockDstChain,
				num: 1,
				obj: "PacketsRelayed Obj",
				dir: "dst",
			},
		},
	}
	for _, tt := range tests {
		tt.prepareMock()
		t.Run(tt.name, func(t *testing.T) {
			logPacketsRelayed(tt.args.src, tt.args.dst, tt.args.num, tt.args.obj, tt.args.dir)
		})
	}
}

func TestNaiveStrategy_RelayAcknowledgements(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockDstChain := NewMockChain(ctrl)
	mockSrcProver := NewMockProver(ctrl)
	mockDstProver := NewMockProver(ctrl)
	mockSyncHeaders := NewMockSyncHeaders(ctrl)
	mockSrcQueryContext := NewMockQueryContext(ctrl)
	mockDstQueryContext := NewMockQueryContext(ctrl)

	mockCodec := MakeCodec()

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	wantHeight := clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}

	packetInfoList := PacketInfoList{
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{1},
			EventHeight:     clienttypes.Height{},
		},
	}

	packetCommitmentPath := host.PacketCommitmentPath(
		packetInfoList[0].SourcePort,
		packetInfoList[0].SourceChannel,
		packetInfoList[0].Sequence,
	)

	packetAcknowledgementPath := host.PacketAcknowledgementPath(
		packetInfoList[0].DestinationPort,
		packetInfoList[0].DestinationChannel,
		packetInfoList[0].Sequence,
	)

	srcCommitment := chantypes.CommitPacket(
		mockCodec,
		packetInfoList[0],
	)
	dstCommitment := chantypes.CommitAcknowledgement(
		packetInfoList[0].Acknowledgement,
	)

	proof := []byte{1}

	type fields struct {
		srcNoAck bool
		dstNoAck bool
	}
	type args struct {
		src             *ProvableChain
		dst             *ProvableChain
		rp              *RelayPackets
		sh              SyncHeaders
		doExecuteAckSrc bool
		doExecuteAckDst bool
	}
	tests := []struct {
		name        string
		fields      fields
		prepareMock func()
		args        args
		want        *RelayMsgs
		wantErr     bool
	}{
		{
			name: "Not collected because there is no ACK.",
			fields: fields{
				srcNoAck: false,
				dstNoAck: false,
			},
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, srcCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				rp: &RelayPackets{
					Src: nil,
					Dst: nil,
				},
				sh:              mockSyncHeaders,
				doExecuteAckSrc: false,
				doExecuteAckDst: false,
			},
			want: &RelayMsgs{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			wantErr: false,
		},
		{
			name: "Collect DST ACK.",
			fields: fields{
				srcNoAck: false,
				dstNoAck: false,
			},
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				rp: &RelayPackets{
					Src: packetInfoList,
					Dst: nil,
				},
				sh:              mockSyncHeaders,
				doExecuteAckSrc: false,
				doExecuteAckDst: true,
			},
			want: &RelayMsgs{
				Src: []sdk.Msg{},
				Dst: []sdk.Msg{
					chantypes.NewMsgAcknowledgement(
						packetInfoList[0].Packet,
						packetInfoList[0].Acknowledgement,
						proof,
						wantHeight,
						"",
					),
				},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			wantErr: false,
		},
		{
			name: "Collect SRC ACK.",
			fields: fields{
				srcNoAck: false,
				dstNoAck: false,
			},
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				rp: &RelayPackets{
					Src: nil,
					Dst: packetInfoList,
				},
				sh:              mockSyncHeaders,
				doExecuteAckSrc: true,
				doExecuteAckDst: false,
			},
			want: &RelayMsgs{
				Src: []sdk.Msg{
					chantypes.NewMsgAcknowledgement(
						packetInfoList[0].Packet,
						packetInfoList[0].Acknowledgement,
						proof,
						wantHeight,
						"",
					),
				},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			st := &NaiveStrategy{
				srcNoAck: tt.fields.srcNoAck,
				dstNoAck: tt.fields.dstNoAck,
			}
			got, err := st.RelayAcknowledgements(tt.args.src, tt.args.dst, tt.args.rp, tt.args.sh, tt.args.doExecuteAckSrc, tt.args.doExecuteAckDst)
			if (err != nil) != tt.wantErr {
				t.Errorf("NaiveStrategy.RelayAcknowledgements() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("NaiveStrategy.RelayAcknowledgements() differs: (-got +want)n%s", diff)
				}
				t.Errorf("NaiveStrategy.RelayAcknowledgements() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_collectAcks(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockDstChain := NewMockChain(ctrl)
	mockSrcProver := NewMockProver(ctrl)
	mockDstProver := NewMockProver(ctrl)
	mockSyncHeaders := NewMockSyncHeaders(ctrl)
	mockSrcQueryContext := NewMockQueryContext(ctrl)
	mockDstQueryContext := NewMockQueryContext(ctrl)

	mockCodec := MakeCodec()

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	wantHeight := clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}

	packetInfoList := PacketInfoList{
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{1},
			EventHeight:     clienttypes.Height{},
		},
	}

	packetAcknowledgementPath := host.PacketAcknowledgementPath(
		packetInfoList[0].DestinationPort,
		packetInfoList[0].DestinationChannel,
		packetInfoList[0].Sequence,
	)

	dstCommitment := chantypes.CommitAcknowledgement(
		packetInfoList[0].Acknowledgement,
	)

	proof := []byte{1}

	type args struct {
		ctx     QueryContext
		chain   *ProvableChain
		packets PacketInfoList
		signer  sdk.AccAddress
	}
	tests := []struct {
		name        string
		args        args
		prepareMock func()
		want        []sdk.Msg
		wantErr     bool
	}{
		{
			name: "Collect No Ack to send.",
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			args: args{
				ctx: mockSrcQueryContext,
				chain: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				packets: nil,
				signer:  nil,
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "Collect Src Ack to send.",
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			args: args{
				ctx: mockSrcQueryContext,
				chain: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				packets: packetInfoList,
				signer:  nil,
			},
			want: []sdk.Msg{
				chantypes.NewMsgAcknowledgement(
					packetInfoList[0].Packet,
					packetInfoList[0].Acknowledgement,
					proof,
					wantHeight,
					"",
				),
			},
			wantErr: false,
		},
		{
			name: "Collect Dst Ack to send.",
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			args: args{
				ctx: mockDstQueryContext,
				chain: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				packets: packetInfoList,
				signer:  nil,
			},
			want: []sdk.Msg{
				chantypes.NewMsgAcknowledgement(
					packetInfoList[0].Packet,
					packetInfoList[0].Acknowledgement,
					proof,
					wantHeight,
					"",
				),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			got, err := collectAcks(tt.args.ctx, tt.args.chain, tt.args.packets, tt.args.signer)
			if (err != nil) != tt.wantErr {
				t.Errorf("collectAcks() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("collectAcks() differs: (-got +want)n%s", diff)
				}
				t.Errorf("collectAcks() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNaiveStrategy_UpdateClients(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockDstChain := NewMockChain(ctrl)
	mockSrcProver := NewMockProver(ctrl)
	mockDstProver := NewMockProver(ctrl)
	mockSyncHeaders := NewMockSyncHeaders(ctrl)
	mockSrcQueryContext := NewMockQueryContext(ctrl)
	mockDstQueryContext := NewMockQueryContext(ctrl)

	mockCodec := MakeCodec()

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	srcProvableChain := &ProvableChain{
		Chain:  mockSrcChain,
		Prover: mockSrcProver,
	}

	dstProvableChain := &ProvableChain{
		Chain:  mockDstChain,
		Prover: mockDstProver,
	}

	wantHeight := clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}

	packetInfoList := PacketInfoList{
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{1},
			EventHeight:     clienttypes.Height{},
		},
	}

	packetCommitmentPath := host.PacketCommitmentPath(
		packetInfoList[0].SourcePort,
		packetInfoList[0].SourceChannel,
		packetInfoList[0].Sequence,
	)

	packetAcknowledgementPath := host.PacketAcknowledgementPath(
		packetInfoList[0].DestinationPort,
		packetInfoList[0].DestinationChannel,
		packetInfoList[0].Sequence,
	)

	srcCommitment := chantypes.CommitPacket(
		mockCodec,
		packetInfoList[0],
	)
	dstCommitment := chantypes.CommitAcknowledgement(
		packetInfoList[0].Acknowledgement,
	)

	proof := []byte{1}

	type fields struct {
		srcNoAck bool
		dstNoAck bool
	}
	type args struct {
		src               *ProvableChain
		dst               *ProvableChain
		doExecuteRelaySrc bool
		doExecuteRelayDst bool
		doExecuteAckSrc   bool
		doExecuteAckDst   bool
		sh                SyncHeaders
		doRefresh         bool
	}
	tests := []struct {
		name        string
		fields      fields
		prepareMock func()
		args        args
		want        *RelayMsgs
		wantErr     bool
	}{
		{
			name: "No UpdateClients.",
			fields: fields{
				srcNoAck: false,
				dstNoAck: false,
			},
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, srcCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				doExecuteRelaySrc: false,
				doExecuteRelayDst: false,
				doExecuteAckSrc:   false,
				doExecuteAckDst:   false,
				sh:                mockSyncHeaders,
				doRefresh:         false,
			},
			want: &RelayMsgs{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			wantErr: false,
		},
		{
			name: "Src UpdateClients.",
			fields: fields{
				srcNoAck: false,
				dstNoAck: false,
			},
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, srcCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(srcProvableChain, dstProvableChain).Return(nil, nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				doExecuteRelaySrc: true,
				doExecuteRelayDst: false,
				doExecuteAckSrc:   false,
				doExecuteAckDst:   false,
				sh:                mockSyncHeaders,
				doRefresh:         false,
			},
			want: &RelayMsgs{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			wantErr: false,
		},
		{
			name: "Dst UpdateClients.",
			fields: fields{
				srcNoAck: false,
				dstNoAck: false,
			},
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, srcCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(srcProvableChain, dstProvableChain).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				doExecuteRelaySrc: false,
				doExecuteRelayDst: true,
				doExecuteAckSrc:   false,
				doExecuteAckDst:   false,
				sh:                mockSyncHeaders,
				doRefresh:         false,
			},
			want: &RelayMsgs{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			wantErr: false,
		},
		{
			name: "Src Ack UpdateClients.",
			fields: fields{
				srcNoAck: false,
				dstNoAck: false,
			},
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, srcCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(srcProvableChain, dstProvableChain).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				doExecuteRelaySrc: false,
				doExecuteRelayDst: false,
				doExecuteAckSrc:   true,
				doExecuteAckDst:   false,
				sh:                mockSyncHeaders,
				doRefresh:         false,
			},
			want: &RelayMsgs{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			wantErr: false,
		},
		{
			name: "Dst Ack UpdateClients.",
			fields: fields{
				srcNoAck: false,
				dstNoAck: false,
			},
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, srcCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(srcProvableChain, dstProvableChain).Return(nil, nil).AnyTimes()
			},
			args: args{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				doExecuteRelaySrc: false,
				doExecuteRelayDst: false,
				doExecuteAckSrc:   false,
				doExecuteAckDst:   true,
				sh:                mockSyncHeaders,
				doRefresh:         false,
			},
			want: &RelayMsgs{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			wantErr: false,
		},
		{
			name: "doRefresh UpdateClients.",
			fields: fields{
				srcNoAck: false,
				dstNoAck: false,
			},
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, srcCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockSrcProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(false, nil).AnyTimes()

				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockDstProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(true, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(srcProvableChain, dstProvableChain).Return(nil, nil).AnyTimes()

			},
			args: args{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				doExecuteRelaySrc: false,
				doExecuteRelayDst: false,
				doExecuteAckSrc:   false,
				doExecuteAckDst:   false,
				sh:                mockSyncHeaders,
				doRefresh:         true,
			},
			want: &RelayMsgs{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			wantErr: false,
		},
		{
			name: "SRC & DST & doRefresh UpdateClients.",
			fields: fields{
				srcNoAck: false,
				dstNoAck: false,
			},
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, srcCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockSrcProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(false, nil).AnyTimes()

				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockDstProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(true, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(srcProvableChain, dstProvableChain).Return(nil, nil).AnyTimes()

			},
			args: args{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				doExecuteRelaySrc: true,
				doExecuteRelayDst: true,
				doExecuteAckSrc:   true,
				doExecuteAckDst:   true,
				sh:                mockSyncHeaders,
				doRefresh:         true,
			},
			want: &RelayMsgs{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			st := &NaiveStrategy{
				srcNoAck: tt.fields.srcNoAck,
				dstNoAck: tt.fields.dstNoAck,
			}
			got, err := st.UpdateClients(tt.args.src, tt.args.dst, tt.args.doExecuteRelaySrc, tt.args.doExecuteRelayDst, tt.args.doExecuteAckSrc, tt.args.doExecuteAckDst, tt.args.sh, tt.args.doRefresh)
			if (err != nil) != tt.wantErr {
				t.Errorf("NaiveStrategy.UpdateClients() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("NaiveStrategy.UpdateClients() differs: (-got +want)n%s", diff)
				}
				t.Errorf("NaiveStrategy.UpdateClients() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TODO ethereum/tx.go の SendMsgs が参照できないため除外
//func TestNaiveStrategy_Send(t *testing.T) {
//
//	// Generating a Controller that manages mock calls
//	ctrl := gomock.NewController(t)
//
//	// Generate a mock instance
//	mockSrcChain := NewMockChain(ctrl)
//	mockDstChain := NewMockChain(ctrl)
//	mockSrcProver := NewMockProver(ctrl)
//	mockDstProver := NewMockProver(ctrl)
//	mockSyncHeaders := NewMockSyncHeaders(ctrl)
//	mockSrcQueryContext := NewMockQueryContext(ctrl)
//	mockDstQueryContext := NewMockQueryContext(ctrl)
//
//	mockCodec := MakeCodec()
//
//	srcChainID := "********"
//	dstChainID := "********"
//	clientID := "hb-ibft2-0"
//	connectionID := "connection-0"
//	channelID := "channel-0"
//	portID := "account-sync"
//	order := "unordered"
//	version := "account-sync-0"
//
//	srcPath := &PathEnd{
//		ChainID:      srcChainID,
//		ClientID:     clientID,
//		ConnectionID: connectionID,
//		ChannelID:    channelID,
//		PortID:       portID,
//		Order:        order,
//		Version:      version,
//	}
//
//	dstPath := &PathEnd{
//		ChainID:      dstChainID,
//		ClientID:     clientID,
//		ConnectionID: connectionID,
//		ChannelID:    channelID,
//		PortID:       portID,
//		Order:        order,
//		Version:      version,
//	}
//
//	srcProvableChain := &ProvableChain{
//		Chain:  mockSrcChain,
//		Prover: mockSrcProver,
//	}
//
//	dstProvableChain := &ProvableChain{
//		Chain:  mockDstChain,
//		Prover: mockDstProver,
//	}
//
//	wantHeight := clienttypes.Height{
//		RevisionNumber: 0,
//		RevisionHeight: 100,
//	}
//
//	packetInfoList := PacketInfoList{
//		&PacketInfo{
//			Packet: chantypes.Packet{
//				Sequence:           1,
//				SourcePort:         portID,
//				SourceChannel:      channelID,
//				DestinationPort:    portID,
//				DestinationChannel: channelID,
//				Data:               []byte{1},
//				TimeoutHeight: types.Height{
//					RevisionNumber: 0,
//					RevisionHeight: 100,
//				},
//				TimeoutTimestamp: 0,
//			},
//			Acknowledgement: []byte{1},
//			EventHeight:     clienttypes.Height{},
//		},
//	}
//
//	packetCommitmentPath := host.PacketCommitmentPath(
//		packetInfoList[0].SourcePort,
//		packetInfoList[0].SourceChannel,
//		packetInfoList[0].Sequence,
//	)
//
//	packetAcknowledgementPath := host.PacketAcknowledgementPath(
//		packetInfoList[0].DestinationPort,
//		packetInfoList[0].DestinationChannel,
//		packetInfoList[0].Sequence,
//	)
//
//	srcCommitment := chantypes.CommitPacket(
//		mockCodec,
//		packetInfoList[0],
//	)
//	dstCommitment := chantypes.CommitAcknowledgement(
//		packetInfoList[0].Acknowledgement,
//	)
//
//	proof := []byte{1}
//
//	relayMsgs := RelayMsgs{
//		Src: []sdk.Msg{
//			chantypes.NewMsgRecvPacket(
//				packetInfoList[0].Packet,
//				proof,
//				wantHeight,
//				"cosmos1qyfkm2y3",
//			),
//		},
//		Dst: []sdk.Msg{
//			chantypes.NewMsgAcknowledgement(
//				packetInfoList[0].Packet,
//				packetInfoList[0].Acknowledgement,
//				proof,
//				wantHeight,
//				"",
//			),
//		},
//	}
//
//	wantSrcMsgIDs := make([]MsgID, len(relayMsgs.Src))
//	wantDstMsgIDs := make([]MsgID, len(relayMsgs.Dst))
//
//	type fields struct {
//		Ordered      bool
//		MaxTxSize    uint64
//		MaxMsgLength uint64
//		srcNoAck     bool
//		dstNoAck     bool
//	}
//	type args struct {
//		src  Chain
//		dst  Chain
//		msgs *RelayMsgs
//	}
//	tests := []struct {
//		name        string
//		fields      fields
//		prepareMock func()
//		args        args
//	}{
//		{
//			name: "",
//			fields: fields{
//				Ordered:      false,
//				MaxTxSize:    0,
//				MaxMsgLength: 0,
//				srcNoAck:     false,
//				dstNoAck:     false,
//			},
//			prepareMock: func() {
//				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
//				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
//				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
//				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
//				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
//				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
//				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
//				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
//				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
//				mockSrcChain.EXPECT().SendMsgs(gomock.Any()).Return(nil, nil).AnyTimes()
//
//				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
//				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
//				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
//				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
//				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
//				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
//				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
//				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
//				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
//				mockDstChain.EXPECT().SendMsgs(gomock.Any()).Return(nil, nil).AnyTimes()
//
//				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
//				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, srcCommitment).Return(proof, wantHeight, nil).AnyTimes()
//				mockSrcProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(false, nil).AnyTimes()
//
//				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
//				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()
//				mockDstProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(true, nil).AnyTimes()
//
//				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
//				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
//				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
//				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
//
//				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
//				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
//				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
//				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
//				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(srcProvableChain, dstProvableChain).Return(nil, nil).AnyTimes()
//
//			},
//			args: args{
//				src: mockSrcChain,
//				dst: mockDstChain,
//				msgs: &RelayMsgs{
//					Src: []sdk.Msg{
//						chantypes.NewMsgRecvPacket(
//							packetInfoList[0].Packet,
//							proof,
//							wantHeight,
//							"cosmos1qyfkm2y3",
//						),
//					},
//					Dst: []sdk.Msg{
//						chantypes.NewMsgAcknowledgement(
//							packetInfoList[0].Packet,
//							packetInfoList[0].Acknowledgement,
//							proof,
//							wantHeight,
//							"",
//						),
//					},
//					MaxTxSize:    0,
//					MaxMsgLength: 0,
//					Last:         false,
//					Succeeded:    false,
//					SrcMsgIDs:    wantSrcMsgIDs,
//					DstMsgIDs:    wantDstMsgIDs,
//				},
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			tt.prepareMock()
//			st := &NaiveStrategy{
//				Ordered:      tt.fields.Ordered,
//				MaxTxSize:    tt.fields.MaxTxSize,
//				MaxMsgLength: tt.fields.MaxMsgLength,
//				srcNoAck:     tt.fields.srcNoAck,
//				dstNoAck:     tt.fields.dstNoAck,
//			}
//			st.Send(tt.args.src, tt.args.dst, tt.args.msgs)
//		})
//	}
//}

// TODO 未使用のため除外
//func Test_naiveStrategyMetrics_updateBacklogMetrics(t *testing.T) {
//	type fields struct {
//		srcBacklog PacketInfoList
//		dstBacklog PacketInfoList
//	}
//	type args struct {
//		ctx           context.Context
//		src           ChainInfo
//		dst           ChainInfo
//		newSrcBacklog PacketInfoList
//		newDstBacklog PacketInfoList
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			st := &naiveStrategyMetrics{
//				srcBacklog: tt.fields.srcBacklog,
//				dstBacklog: tt.fields.dstBacklog,
//			}
//			if err := st.updateBacklogMetrics(tt.args.ctx, tt.args.src, tt.args.dst, tt.args.newSrcBacklog, tt.args.newDstBacklog); (err != nil) != tt.wantErr {
//				t.Errorf("naiveStrategyMetrics.updateBacklogMetrics() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}

//go:generate mockgen -source=$GOFILE -package=core -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package core

import (
	"reflect"
	"testing"

	"go.uber.org/mock/gomock"
)

func TestNewSyncHeaders(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcChainInfoLightClient := NewMockChainInfoLightClient(ctrl)
	dstChainInfoLightClient := NewMockChainInfoLightClient(ctrl)

	// set expectations
	srcChainID := "src-chain-id"
	dstChainID := "dst-chain-id"
	srcChainInfoLightClient.EXPECT().ChainID().Return(srcChainID).AnyTimes()
	dstChainInfoLightClient.EXPECT().ChainID().Return(dstChainID).AnyTimes()
	srcChainInfoLightClient.EXPECT().GetLatestFinalizedHeader().Return(nil, nil).AnyTimes()
	dstChainInfoLightClient.EXPECT().GetLatestFinalizedHeader().Return(nil, nil).AnyTimes()

	type args struct {
		src ChainInfoLightClient
		dst ChainInfoLightClient
	}
	tests := []struct {
		name    string
		args    args
		want    SyncHeaders
		wantErr bool
	}{
		{
			name: "should returns a new instance of SyncHeaders",
			args: args{
				src: srcChainInfoLightClient,
				dst: dstChainInfoLightClient,
			},
			want: &syncHeaders{
				latestFinalizedHeaders: map[string]Header{
					srcChainID: nil,
					dstChainID: nil,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewSyncHeaders(tt.args.src, tt.args.dst)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewSyncHeaders() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewSyncHeaders() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_syncHeaders_Updates(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcChainInfoLightClient := NewMockChainInfoLightClient(ctrl)
	dstChainInfoLightClient := NewMockChainInfoLightClient(ctrl)

	// set expectations
	srcChainID := "src-chain-id"
	dstChainID := "dst-chain-id"
	srcChainInfoLightClient.EXPECT().ChainID().Return(srcChainID).AnyTimes()
	dstChainInfoLightClient.EXPECT().ChainID().Return(dstChainID).AnyTimes()
	srcChainInfoLightClient.EXPECT().GetLatestFinalizedHeader().Return(nil, nil).AnyTimes()
	dstChainInfoLightClient.EXPECT().GetLatestFinalizedHeader().Return(nil, nil).AnyTimes()

	type fields struct {
		latestFinalizedHeaders map[string]Header
	}
	type args struct {
		src ChainInfoLightClient
		dst ChainInfoLightClient
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "should updates the headers on both chains.",
			fields: fields{
				latestFinalizedHeaders: map[string]Header{
					srcChainID: nil,
					dstChainID: nil,
				},
			},
			args: args{
				src: srcChainInfoLightClient,
				dst: dstChainInfoLightClient,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sh := &syncHeaders{
				latestFinalizedHeaders: tt.fields.latestFinalizedHeaders,
			}
			if err := sh.Updates(tt.args.src, tt.args.dst); (err != nil) != tt.wantErr {
				t.Errorf("syncHeaders.Updates() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_syncHeaders_GetLatestFinalizedHeader(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcMockHeader := NewMockHeader(ctrl)
	dstMockHeader := NewMockHeader(ctrl)

	// set expectations
	srcChainID := "src-chain-id"
	dstChainID := "dst-chain-id"
	srcMockHeader.EXPECT().GetHeight().Return(nil).AnyTimes()
	dstMockHeader.EXPECT().GetHeight().Return(nil).AnyTimes()

	type fields struct {
		latestFinalizedHeaders map[string]Header
	}
	type args struct {
		chainID string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   Header
	}{
		{
			name: "should returns the src latest finalized header of the chain.",
			fields: fields{
				latestFinalizedHeaders: map[string]Header{
					srcChainID: srcMockHeader,
					dstChainID: dstMockHeader,
				},
			},
			args: args{
				chainID: srcChainID,
			},
			want: srcMockHeader,
		},
		{
			name: "should returns the dst latest finalized header of the chain.",
			fields: fields{
				latestFinalizedHeaders: map[string]Header{
					srcChainID: srcMockHeader,
					dstChainID: dstMockHeader,
				},
			},
			args: args{
				chainID: dstChainID,
			},
			want: dstMockHeader,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sh := syncHeaders{
				latestFinalizedHeaders: tt.fields.latestFinalizedHeaders,
			}
			if got := sh.GetLatestFinalizedHeader(tt.args.chainID); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("syncHeaders.GetLatestFinalizedHeader() = %v, want %v", got, tt.want)
			}
		})
	}
}

//func Test_syncHeaders_GetQueryContext(t *testing.T) {
//
//	// Generating a Controller that manages mock calls
//	ctrl := gomock.NewController(t)
//
//	// Generate a mock instance
//	srcMockHeader := NewMockHeader(ctrl)
//	dstMockHeader := NewMockHeader(ctrl)
//	mockQueryContext := NewMockQueryContext(ctrl)
//	//mockSyncHeaders := NewMockSyncHeaders(ctrl)
//
//	// set expectations
//	srcChainID := "src-chain-id"
//	dstChainID := "dst-chain-id"
//
//	srcMockHeader.EXPECT().GetHeight().Return(nil).AnyTimes()
//	dstMockHeader.EXPECT().GetHeight().Return(nil).AnyTimes()
//
//	type fields struct {
//		latestFinalizedHeaders map[string]Header
//	}
//	type args struct {
//		chainID string
//	}
//	tests := []struct {
//		name          string
//		fields        fields
//		args          args
//		prepareMockFn func(m *MockSyncHeaders)
//		want          QueryContext
//	}{
//		{
//			name: "should builds a query context based on the latest finalized header.",
//			fields: fields{
//				latestFinalizedHeaders: map[string]Header{
//					srcChainID: srcMockHeader,
//					dstChainID: dstMockHeader,
//				},
//			},
//			args: args{
//				chainID: dstChainID,
//			},
//			want: mockQueryContext,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			sh := syncHeaders{
//				latestFinalizedHeaders: tt.fields.latestFinalizedHeaders,
//			}
//			if got := sh.GetQueryContext(tt.args.chainID); !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("syncHeaders.GetQueryContext() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
// TODO prover 側の SetupHeadersForUpdate が見つからず進まない
//func Test_syncHeaders_SetupHeadersForUpdate(t *testing.T) {
//
//	// Generating a Controller that manages mock calls
//	ctrl := gomock.NewController(t)
//
//	// Generate a mock instance
//	srcMockChainLightClient := NewMockChainLightClient(ctrl)
//	dstMockChainLightClient := NewMockChainLightClient(ctrl)
//	srcMockChainInfo := NewMockChainInfo(ctrl)
//	dstMockChainInfo := NewMockChainInfo(ctrl)
//	srcMockHeader := NewMockHeader(ctrl)
//	dstMockHeader := NewMockHeader(ctrl)
//
//	// set expectations
//	srcChainID := "src-chain-id"
//	dstChainID := "dst-chain-id"
//
//	srcMockHeader.EXPECT().GetHeight().Return(nil).AnyTimes()
//	dstMockHeader.EXPECT().GetHeight().Return(nil).AnyTimes()
//
//	srcMockChainInfo.EXPECT().ChainID().Return(srcChainID).AnyTimes()
//	dstMockChainInfo.EXPECT().ChainID().Return(dstChainID).AnyTimes()
//
//	srcMockChainLightClient.EXPECT().ChainID().Return(srcChainID).AnyTimes()
//	dstMockChainLightClient.EXPECT().ChainID().Return(dstChainID).AnyTimes()
//
//	type fields struct {
//		latestFinalizedHeaders map[string]Header
//	}
//	type args struct {
//		src ChainLightClient
//		dst ChainLightClient
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    []Header
//		wantErr bool
//	}{
//		{
//			name: "should returns `src` chain's headers to update the client on `dst` chain.",
//			fields: fields{
//				latestFinalizedHeaders: map[string]Header{
//					srcChainID: srcMockHeader,
//					dstChainID: dstMockHeader,
//				},
//			},
//			args: args{
//				src: srcMockChainLightClient,
//				dst: dstMockChainLightClient,
//			},
//			want: []Header{
//				dstMockHeader,
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			sh := syncHeaders{
//				latestFinalizedHeaders: tt.fields.latestFinalizedHeaders,
//			}
//			got, err := sh.SetupHeadersForUpdate(tt.args.src, tt.args.dst)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("syncHeaders.SetupHeadersForUpdate() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("syncHeaders.SetupHeadersForUpdate() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}
//
//// TODO prover 側の SetupHeadersForUpdate が見つからず進まない
//func Test_syncHeaders_SetupBothHeadersForUpdate(t *testing.T) {
//
//	// Generating a Controller that manages mock calls
//	ctrl := gomock.NewController(t)
//
//	// Generate a mock instance
//	srcMockChainLightClient := NewMockChainLightClient(ctrl)
//	dstMockChainLightClient := NewMockChainLightClient(ctrl)
//	srcMockChainInfo := NewMockChainInfo(ctrl)
//	dstMockChainInfo := NewMockChainInfo(ctrl)
//	srcMockHeader := NewMockHeader(ctrl)
//	dstMockHeader := NewMockHeader(ctrl)
//
//	// set expectations
//	srcChainID := "src-chain-id"
//	dstChainID := "dst-chain-id"
//
//	srcMockHeader.EXPECT().GetHeight().Return(nil).AnyTimes()
//	dstMockHeader.EXPECT().GetHeight().Return(nil).AnyTimes()
//
//	srcMockChainInfo.EXPECT().ChainID().Return(srcChainID).AnyTimes()
//	dstMockChainInfo.EXPECT().ChainID().Return(dstChainID).AnyTimes()
//
//	srcMockChainLightClient.EXPECT().ChainID().Return(srcChainID).AnyTimes()
//	dstMockChainLightClient.EXPECT().ChainID().Return(dstChainID).AnyTimes()
//
//	type fields struct {
//		latestFinalizedHeaders map[string]Header
//	}
//	type args struct {
//		src ChainLightClient
//		dst ChainLightClient
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    []Header
//		want1   []Header
//		wantErr bool
//	}{
//		{
//			name: "should returns both `src` and `dst` chain's headers to update the clients on each chain.",
//			fields: fields{
//				latestFinalizedHeaders: map[string]Header{
//					srcChainID: srcMockHeader,
//					dstChainID: dstMockHeader,
//				},
//			},
//			args: args{
//				src: srcMockChainLightClient,
//				dst: dstMockChainLightClient,
//			},
//			want: []Header{
//				srcMockHeader,
//			},
//			want1: []Header{
//				dstMockHeader,
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			sh := syncHeaders{
//				latestFinalizedHeaders: tt.fields.latestFinalizedHeaders,
//			}
//			got, got1, err := sh.SetupBothHeadersForUpdate(tt.args.src, tt.args.dst)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("syncHeaders.SetupBothHeadersForUpdate() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("syncHeaders.SetupBothHeadersForUpdate() got = %v, want %v", got, tt.want)
//			}
//			if !reflect.DeepEqual(got1, tt.want1) {
//				t.Errorf("syncHeaders.SetupBothHeadersForUpdate() got1 = %v, want %v", got1, tt.want1)
//			}
//		})
//	}
//}

func Test_ensureDifferentChains(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	srcMockChainInfo := NewMockChainInfo(ctrl)
	dstMockChainInfo := NewMockChainInfo(ctrl)

	// set expectations
	srcChainID := "src-chain-id"
	dstChainID := "dst-chain-id"

	srcMockChainInfo.EXPECT().ChainID().Return(srcChainID).AnyTimes()
	dstMockChainInfo.EXPECT().ChainID().Return(dstChainID).AnyTimes()

	type args struct {
		src ChainInfo
		dst ChainInfo
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "should Ensure different chains.",
			args: args{
				src: srcMockChainInfo,
				dst: dstMockChainInfo,
			},
			wantErr: false,
		},
		{
			name: "should Ensure same src chain.",
			args: args{
				src: srcMockChainInfo,
				dst: srcMockChainInfo,
			},
			wantErr: true,
		},
		{
			name: "should Ensure same dst chain.",
			args: args{
				src: srcMockChainInfo,
				dst: srcMockChainInfo,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := ensureDifferentChains(tt.args.src, tt.args.dst); (err != nil) != tt.wantErr {
				t.Errorf("ensureDifferentChains() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

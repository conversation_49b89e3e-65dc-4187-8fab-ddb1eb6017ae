//go:generate mockgen -source=$GOFILE -package=mock_core -destination=./mock_core/mock_$GOFILE -self_package=github.com/decurret-lab/dcbg-dcjpy-relayer/core
//go:generate gotests -w -all $GOFILE
package core

import (
	"reflect"
	"testing"

	codectypes "github.com/cosmos/cosmos-sdk/codec/types"
	"github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	conntypes "github.com/cosmos/ibc-go/v7/modules/core/03-connection/types"
	chantypes "github.com/cosmos/ibc-go/v7/modules/core/04-channel/types"
	commitmenttypes "github.com/cosmos/ibc-go/v7/modules/core/23-commitment/types"
	host "github.com/cosmos/ibc-go/v7/modules/core/24-host"
	ibcexported "github.com/cosmos/ibc-go/v7/modules/core/exported"
	"github.com/google/go-cmp/cmp"
	"go.uber.org/mock/gomock"
)

func TestQueryClientStatePair(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockDstChain := NewMockChain(ctrl)
	mockSrcProver := NewMockProver(ctrl)
	mockDstProver := NewMockProver(ctrl)
	mockSyncHeaders := NewMockSyncHeaders(ctrl)
	mockSrcQueryContext := NewMockQueryContext(ctrl)
	mockDstQueryContext := NewMockQueryContext(ctrl)

	mockCodec := MakeCodec()

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	srcProvableChain := &ProvableChain{
		Chain:  mockSrcChain,
		Prover: mockSrcProver,
	}

	dstProvableChain := &ProvableChain{
		Chain:  mockDstChain,
		Prover: mockDstProver,
	}

	wantHeight := clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}

	packetInfoList := PacketInfoList{
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{1},
			EventHeight:     clienttypes.Height{},
		},
	}

	packetCommitmentPath := host.PacketCommitmentPath(
		packetInfoList[0].SourcePort,
		packetInfoList[0].SourceChannel,
		packetInfoList[0].Sequence,
	)

	packetAcknowledgementPath := host.PacketAcknowledgementPath(
		packetInfoList[0].DestinationPort,
		packetInfoList[0].DestinationChannel,
		packetInfoList[0].Sequence,
	)

	srcCommitment := chantypes.CommitPacket(
		mockCodec,
		packetInfoList[0],
	)
	dstCommitment := chantypes.CommitAcknowledgement(
		packetInfoList[0].Acknowledgement,
	)

	proof := []byte{1}

	mockCsRes := &clienttypes.QueryClientStateResponse{
		ClientState: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.config.ChainConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		Proof: nil,
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}

	type args struct {
		srcCtx QueryContext
		dstCtx QueryContext
		src    interface {
			Chain
			StateProver
		}
		dst interface {
			Chain
			StateProver
		}
		prove bool
	}
	tests := []struct {
		name         string
		prepareMock  func()
		args         args
		wantSrcCsRes *clienttypes.QueryClientStateResponse
		wantDstCsRes *clienttypes.QueryClientStateResponse
		wantErr      bool
	}{
		{
			name: "should return a pair of connection responses.",
			prepareMock: func() {
				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryClientState(gomock.Any()).Return(mockCsRes, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryClientState(gomock.Any()).Return(mockCsRes, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, srcCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockSrcProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(false, nil).AnyTimes()

				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockDstProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(true, nil).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(srcProvableChain, dstProvableChain).Return(nil, nil).AnyTimes()

			},
			args: args{
				srcCtx: mockSrcQueryContext,
				dstCtx: mockDstQueryContext,
				src:    srcProvableChain,
				dst:    dstProvableChain,
				prove:  false,
			},
			wantSrcCsRes: &clienttypes.QueryClientStateResponse{
				ClientState: &codectypes.Any{
					TypeUrl:              "/relayer.chains.ethereum.config.ChainConfig",
					Value:                nil,
					XXX_NoUnkeyedLiteral: struct{}{},
					XXX_unrecognized:     nil,
					XXX_sizecache:        0,
				},
				Proof: nil,
				ProofHeight: clienttypes.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
			},
			wantDstCsRes: &clienttypes.QueryClientStateResponse{
				ClientState: &codectypes.Any{
					TypeUrl:              "/relayer.chains.ethereum.config.ChainConfig",
					Value:                nil,
					XXX_NoUnkeyedLiteral: struct{}{},
					XXX_unrecognized:     nil,
					XXX_sizecache:        0,
				},
				Proof: nil,
				ProofHeight: clienttypes.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			gotSrcCsRes, gotDstCsRes, err := QueryClientStatePair(tt.args.srcCtx, tt.args.dstCtx, tt.args.src, tt.args.dst, tt.args.prove)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryClientStatePair() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotSrcCsRes, tt.wantSrcCsRes) {
				if diff := cmp.Diff(gotSrcCsRes, tt.wantSrcCsRes); diff != "" {
					t.Errorf("QueryClientStatePair() gotSrcCsRes differs: (-got +want)n%s", diff)
				}
				t.Errorf("QueryClientStatePair() gotSrcCsRes = %v, want %v", gotSrcCsRes, tt.wantSrcCsRes)
			}
			if !reflect.DeepEqual(gotDstCsRes, tt.wantDstCsRes) {
				if diff := cmp.Diff(gotDstCsRes, tt.wantDstCsRes); diff != "" {
					t.Errorf("QueryClientStatePair() gotSrcCsRes differs: (-got +want)n%s", diff)
				}
				t.Errorf("QueryClientStatePair() gotDstCsRes = %v, want %v", gotDstCsRes, tt.wantDstCsRes)
			}
		})
	}
}

func TestQueryClientConsensusStatePair(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockDstChain := NewMockChain(ctrl)
	mockSrcProver := NewMockProver(ctrl)
	mockDstProver := NewMockProver(ctrl)
	mockSyncHeaders := NewMockSyncHeaders(ctrl)
	mockSrcQueryContext := NewMockQueryContext(ctrl)
	mockDstQueryContext := NewMockQueryContext(ctrl)

	mockCodec := MakeCodec()

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	srcProvableChain := &ProvableChain{
		Chain:  mockSrcChain,
		Prover: mockSrcProver,
	}

	dstProvableChain := &ProvableChain{
		Chain:  mockDstChain,
		Prover: mockDstProver,
	}

	wantHeight := clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}

	packetInfoList := PacketInfoList{
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{1},
			EventHeight:     clienttypes.Height{},
		},
	}

	packetCommitmentPath := host.PacketCommitmentPath(
		packetInfoList[0].SourcePort,
		packetInfoList[0].SourceChannel,
		packetInfoList[0].Sequence,
	)

	packetAcknowledgementPath := host.PacketAcknowledgementPath(
		packetInfoList[0].DestinationPort,
		packetInfoList[0].DestinationChannel,
		packetInfoList[0].Sequence,
	)

	srcCommitment := chantypes.CommitPacket(
		mockCodec,
		packetInfoList[0],
	)
	dstCommitment := chantypes.CommitAcknowledgement(
		packetInfoList[0].Acknowledgement,
	)

	proof := []byte{1}

	mockCsRes := &clienttypes.QueryConsensusStateResponse{
		ConsensusState: &codectypes.Any{
			TypeUrl:              "/relayer.chains.ethereum.config.ChainConfig",
			Value:                nil,
			XXX_NoUnkeyedLiteral: struct{}{},
			XXX_unrecognized:     nil,
			XXX_sizecache:        0,
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}

	type args struct {
		srcCtx QueryContext
		dstCtx QueryContext
		src    interface {
			Chain
			StateProver
		}
		dst interface {
			Chain
			StateProver
		}
		srcClientConsH ibcexported.Height
		dstClientConsH ibcexported.Height
		prove          bool
	}
	tests := []struct {
		name         string
		prepareMock  func()
		args         args
		wantSrcCsRes *clienttypes.QueryConsensusStateResponse
		wantDstCsRes *clienttypes.QueryConsensusStateResponse
		wantErr      bool
	}{
		{
			name: "allows for the querying of multiple client states at the same time",
			prepareMock: func() {
				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryClientState(gomock.Any()).AnyTimes()
				mockSrcChain.EXPECT().QueryClientConsensusState(gomock.Any(), gomock.Any()).Return(mockCsRes, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryClientState(gomock.Any()).AnyTimes()
				mockDstChain.EXPECT().QueryClientConsensusState(gomock.Any(), gomock.Any()).Return(mockCsRes, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, srcCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockSrcProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(false, nil).AnyTimes()

				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockDstProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(true, nil).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(srcProvableChain, dstProvableChain).Return(nil, nil).AnyTimes()

			},
			args: args{
				srcCtx:         mockSrcQueryContext,
				dstCtx:         mockDstQueryContext,
				src:            srcProvableChain,
				dst:            dstProvableChain,
				srcClientConsH: nil,
				dstClientConsH: nil,
				prove:          false,
			},
			wantSrcCsRes: &clienttypes.QueryConsensusStateResponse{
				ConsensusState: &codectypes.Any{
					TypeUrl:              "/relayer.chains.ethereum.config.ChainConfig",
					Value:                nil,
					XXX_NoUnkeyedLiteral: struct{}{},
					XXX_unrecognized:     nil,
					XXX_sizecache:        0,
				},
				Proof: []byte{1},
				ProofHeight: clienttypes.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
			},
			wantDstCsRes: &clienttypes.QueryConsensusStateResponse{
				ConsensusState: &codectypes.Any{
					TypeUrl:              "/relayer.chains.ethereum.config.ChainConfig",
					Value:                nil,
					XXX_NoUnkeyedLiteral: struct{}{},
					XXX_unrecognized:     nil,
					XXX_sizecache:        0,
				},
				Proof: []byte{1},
				ProofHeight: clienttypes.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			gotSrcCsRes, gotDstCsRes, err := QueryClientConsensusStatePair(tt.args.srcCtx, tt.args.dstCtx, tt.args.src, tt.args.dst, tt.args.srcClientConsH, tt.args.dstClientConsH, tt.args.prove)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryClientConsensusStatePair() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotSrcCsRes, tt.wantSrcCsRes) {
				if diff := cmp.Diff(gotSrcCsRes, tt.wantSrcCsRes); diff != "" {
					t.Errorf("QueryClientConsensusStatePair() differs: (-got +want)n%s", diff)
				}
				t.Errorf("QueryClientConsensusStatePair() gotSrcCsRes = %v, want %v", gotSrcCsRes, tt.wantSrcCsRes)
			}
			if !reflect.DeepEqual(gotDstCsRes, tt.wantDstCsRes) {
				if diff := cmp.Diff(gotDstCsRes, tt.wantDstCsRes); diff != "" {
					t.Errorf("QueryClientConsensusStatePair() differs: (-got +want)n%s", diff)
				}
				t.Errorf("QueryClientConsensusStatePair() gotDstCsRes = %v, want %v", gotDstCsRes, tt.wantDstCsRes)
			}
		})
	}
}

func TestQueryConnectionPair(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockDstChain := NewMockChain(ctrl)
	mockSrcProver := NewMockProver(ctrl)
	mockDstProver := NewMockProver(ctrl)
	mockSyncHeaders := NewMockSyncHeaders(ctrl)
	mockSrcQueryContext := NewMockQueryContext(ctrl)
	mockDstQueryContext := NewMockQueryContext(ctrl)

	mockCodec := MakeCodec()

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	srcProvableChain := &ProvableChain{
		Chain:  mockSrcChain,
		Prover: mockSrcProver,
	}

	dstProvableChain := &ProvableChain{
		Chain:  mockDstChain,
		Prover: mockDstProver,
	}

	wantHeight := clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}

	packetInfoList := PacketInfoList{
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{1},
			EventHeight:     clienttypes.Height{},
		},
	}

	packetCommitmentPath := host.PacketCommitmentPath(
		packetInfoList[0].SourcePort,
		packetInfoList[0].SourceChannel,
		packetInfoList[0].Sequence,
	)

	packetAcknowledgementPath := host.PacketAcknowledgementPath(
		packetInfoList[0].DestinationPort,
		packetInfoList[0].DestinationChannel,
		packetInfoList[0].Sequence,
	)

	srcCommitment := chantypes.CommitPacket(
		mockCodec,
		packetInfoList[0],
	)
	dstCommitment := chantypes.CommitAcknowledgement(
		packetInfoList[0].Acknowledgement,
	)

	proof := []byte{1}

	mockSrcConn := &conntypes.QueryConnectionResponse{
		Connection: &conntypes.ConnectionEnd{
			ClientId: "hb-ibft2-0",
			Versions: nil,
			State:    0,
			Counterparty: conntypes.Counterparty{
				ClientId:     "hb-ibft2-0",
				ConnectionId: "connection-0",
				Prefix: commitmenttypes.MerklePrefix{
					KeyPrefix: []byte("ibc"),
				},
			},
			DelayPeriod: 0,
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}
	mockDstConn := &conntypes.QueryConnectionResponse{
		Connection: &conntypes.ConnectionEnd{
			ClientId: "hb-ibft2-0",
			Versions: nil,
			State:    0,
			Counterparty: conntypes.Counterparty{
				ClientId:     "hb-ibft2-0",
				ConnectionId: "connection-0",
				Prefix: commitmenttypes.MerklePrefix{
					KeyPrefix: []byte("ibc"),
				},
			},
			DelayPeriod: 0,
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}

	type args struct {
		srcCtx QueryContext
		dstCtx QueryContext
		src    interface {
			Chain
			StateProver
		}
		dst interface {
			Chain
			StateProver
		}
		prove bool
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		wantSrcConn *conntypes.QueryConnectionResponse
		wantDstConn *conntypes.QueryConnectionResponse
		wantErr     bool
	}{
		{
			name: "should returns a pair of connection responses.",
			prepareMock: func() {
				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryClientState(gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryConnection(gomock.Any()).Return(mockSrcConn, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryClientState(gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryConnection(gomock.Any()).Return(mockDstConn, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, srcCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockSrcProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(false, nil).AnyTimes()

				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockDstProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(true, nil).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(srcProvableChain, dstProvableChain).Return(nil, nil).AnyTimes()

			},
			args: args{
				srcCtx: mockSrcQueryContext,
				dstCtx: mockDstQueryContext,
				src:    srcProvableChain,
				dst:    dstProvableChain,
				prove:  false,
			},
			wantSrcConn: &conntypes.QueryConnectionResponse{
				Connection: &conntypes.ConnectionEnd{
					ClientId: "hb-ibft2-0",
					Versions: nil,
					State:    0,
					Counterparty: conntypes.Counterparty{
						ClientId:     "hb-ibft2-0",
						ConnectionId: "connection-0",
						Prefix: commitmenttypes.MerklePrefix{
							KeyPrefix: []byte("ibc"),
						},
					},
					DelayPeriod: 0,
				},
				Proof: []byte{1},
				ProofHeight: clienttypes.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
			},
			wantDstConn: &conntypes.QueryConnectionResponse{
				Connection: &conntypes.ConnectionEnd{
					ClientId: "hb-ibft2-0",
					Versions: nil,
					State:    0,
					Counterparty: conntypes.Counterparty{
						ClientId:     "hb-ibft2-0",
						ConnectionId: "connection-0",
						Prefix: commitmenttypes.MerklePrefix{
							KeyPrefix: []byte("ibc"),
						},
					},
					DelayPeriod: 0,
				},
				Proof: []byte{1},
				ProofHeight: clienttypes.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			gotSrcConn, gotDstConn, err := QueryConnectionPair(tt.args.srcCtx, tt.args.dstCtx, tt.args.src, tt.args.dst, tt.args.prove)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryConnectionPair() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotSrcConn, tt.wantSrcConn) {
				if diff := cmp.Diff(gotSrcConn, tt.wantSrcConn); diff != "" {
					t.Errorf("QueryConnectionPair() differs: (-got +want)n%s", diff)
				}
				t.Errorf("QueryConnectionPair() gotSrcConn = %v, want %v", gotSrcConn, tt.wantSrcConn)
			}
			if !reflect.DeepEqual(gotDstConn, tt.wantDstConn) {
				if diff := cmp.Diff(gotDstConn, tt.wantDstConn); diff != "" {
					t.Errorf("QueryConnectionPair() differs: (-got +want)n%s", diff)
				}
				t.Errorf("QueryConnectionPair() gotDstConn = %v, want %v", gotDstConn, tt.wantDstConn)
			}
		})
	}
}

func TestQueryChannelPair(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockDstChain := NewMockChain(ctrl)
	mockSrcProver := NewMockProver(ctrl)
	mockDstProver := NewMockProver(ctrl)
	mockSyncHeaders := NewMockSyncHeaders(ctrl)
	mockSrcQueryContext := NewMockQueryContext(ctrl)
	mockDstQueryContext := NewMockQueryContext(ctrl)

	mockCodec := MakeCodec()

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	srcProvableChain := &ProvableChain{
		Chain:  mockSrcChain,
		Prover: mockSrcProver,
	}

	dstProvableChain := &ProvableChain{
		Chain:  mockDstChain,
		Prover: mockDstProver,
	}

	wantHeight := clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}

	packetInfoList := PacketInfoList{
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{1},
			EventHeight:     clienttypes.Height{},
		},
	}

	packetCommitmentPath := host.PacketCommitmentPath(
		packetInfoList[0].SourcePort,
		packetInfoList[0].SourceChannel,
		packetInfoList[0].Sequence,
	)

	packetAcknowledgementPath := host.PacketAcknowledgementPath(
		packetInfoList[0].DestinationPort,
		packetInfoList[0].DestinationChannel,
		packetInfoList[0].Sequence,
	)

	srcCommitment := chantypes.CommitPacket(
		mockCodec,
		packetInfoList[0],
	)
	dstCommitment := chantypes.CommitAcknowledgement(
		packetInfoList[0].Acknowledgement,
	)

	proof := []byte{1}

	mockSrcChan := &chantypes.QueryChannelResponse{
		Channel: &chantypes.Channel{
			State:    0,
			Ordering: 0,
			Counterparty: chantypes.Counterparty{
				PortId:    "account-sync",
				ChannelId: "channel-0",
			},
			ConnectionHops: nil,
			Version:        "account-sync-1",
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}
	mockDstChan := &chantypes.QueryChannelResponse{
		Channel: &chantypes.Channel{
			State:    0,
			Ordering: 0,
			Counterparty: chantypes.Counterparty{
				PortId:    "account-sync",
				ChannelId: "channel-0",
			},
			ConnectionHops: nil,
			Version:        "account-sync-1",
		},
		Proof: []byte{1},
		ProofHeight: clienttypes.Height{
			RevisionNumber: 0,
			RevisionHeight: 100,
		},
	}

	type args struct {
		srcCtx QueryContext
		dstCtx QueryContext
		src    interface {
			Chain
			StateProver
		}
		dst interface {
			Chain
			StateProver
		}
		prove bool
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		wantSrcChan *chantypes.QueryChannelResponse
		wantDstChan *chantypes.QueryChannelResponse
		wantErr     bool
	}{
		{
			name: "should returns a pair of channel responses.",
			prepareMock: func() {
				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryClientState(gomock.Any()).Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryChannel(gomock.Any()).Return(mockSrcChan, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayAcknowledgements(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryClientState(gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryChannel(gomock.Any()).Return(mockDstChan, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, srcCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockSrcProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(false, nil).AnyTimes()

				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockDstQueryContext, packetAcknowledgementPath, dstCommitment).Return(proof, wantHeight, nil).AnyTimes()
				mockDstProver.EXPECT().CheckRefreshRequired(gomock.Any()).Return(true, nil).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().SetupHeadersForUpdate(srcProvableChain, dstProvableChain).Return(nil, nil).AnyTimes()

			},
			args: args{
				srcCtx: mockSrcQueryContext,
				dstCtx: mockDstQueryContext,
				src:    srcProvableChain,
				dst:    dstProvableChain,
				prove:  false,
			},
			wantSrcChan: &chantypes.QueryChannelResponse{
				Channel: &chantypes.Channel{
					State:    0,
					Ordering: 0,
					Counterparty: chantypes.Counterparty{
						PortId:    "account-sync",
						ChannelId: "channel-0",
					},
					ConnectionHops: nil,
					Version:        "account-sync-1",
				},
				Proof: []byte{1},
				ProofHeight: clienttypes.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
			},
			wantDstChan: &chantypes.QueryChannelResponse{
				Channel: &chantypes.Channel{
					State:    0,
					Ordering: 0,
					Counterparty: chantypes.Counterparty{
						PortId:    "account-sync",
						ChannelId: "channel-0",
					},
					ConnectionHops: nil,
					Version:        "account-sync-1",
				},
				Proof: []byte{1},
				ProofHeight: clienttypes.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			gotSrcChan, gotDstChan, err := QueryChannelPair(tt.args.srcCtx, tt.args.dstCtx, tt.args.src, tt.args.dst, tt.args.prove)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryChannelPair() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotSrcChan, tt.wantSrcChan) {
				if diff := cmp.Diff(gotSrcChan, tt.wantSrcChan); diff != "" {
					t.Errorf("QueryChannelPair() differs: (-got +want)n%s", diff)
				}
				t.Errorf("QueryChannelPair() gotSrcChan = %v, want %v", gotSrcChan, tt.wantSrcChan)
			}
			if !reflect.DeepEqual(gotDstChan, tt.wantDstChan) {
				if diff := cmp.Diff(gotDstChan, tt.wantDstChan); diff != "" {
					t.Errorf("QueryChannelPair() differs: (-got +want)n%s", diff)
				}
				t.Errorf("QueryChannelPair() gotDstChan = %v, want %v", gotDstChan, tt.wantDstChan)
			}
		})
	}
}

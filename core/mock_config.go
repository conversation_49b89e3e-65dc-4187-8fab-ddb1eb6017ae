// Code generated by MockGen. DO NOT EDIT.
// Source: config.go
//
// Generated by this command:
//
//	mockgen -source=config.go -package=core -destination=./mock_config.go
//

// Package core is a generated GoMock package.
package core

import (
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockConfigI is a mock of ConfigI interface.
type MockConfigI struct {
	ctrl     *gomock.Controller
	recorder *MockConfigIMockRecorder
}

// MockConfigIMockRecorder is the mock recorder for MockConfigI.
type MockConfigIMockRecorder struct {
	mock *MockConfigI
}

// NewMockConfigI creates a new mock instance.
func NewMockConfigI(ctrl *gomock.Controller) *MockConfigI {
	mock := &MockConfigI{ctrl: ctrl}
	mock.recorder = &MockConfigIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConfigI) EXPECT() *MockConfigIMockRecorder {
	return m.recorder
}

// UpdateConfigID mocks base method.
func (m *MockConfigI) UpdateConfigID(pathName, chainID string, configID ConfigIDType, id string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateConfigID", pathName, chainID, configID, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateConfigID indicates an expected call of UpdateConfigID.
func (mr *MockConfigIMockRecorder) UpdateConfigID(pathName, chainID, configID, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateConfigID", reflect.TypeOf((*MockConfigI)(nil).UpdateConfigID), pathName, chainID, configID, id)
}

// MockChainConfig is a mock of ChainConfig interface.
type MockChainConfig struct {
	ctrl     *gomock.Controller
	recorder *MockChainConfigMockRecorder
}

// MockChainConfigMockRecorder is the mock recorder for MockChainConfig.
type MockChainConfigMockRecorder struct {
	mock *MockChainConfig
}

// NewMockChainConfig creates a new mock instance.
func NewMockChainConfig(ctrl *gomock.Controller) *MockChainConfig {
	mock := &MockChainConfig{ctrl: ctrl}
	mock.recorder = &MockChainConfigMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChainConfig) EXPECT() *MockChainConfigMockRecorder {
	return m.recorder
}

// Build mocks base method.
func (m *MockChainConfig) Build() (Chain, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Build")
	ret0, _ := ret[0].(Chain)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Build indicates an expected call of Build.
func (mr *MockChainConfigMockRecorder) Build() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Build", reflect.TypeOf((*MockChainConfig)(nil).Build))
}

// ProtoMessage mocks base method.
func (m *MockChainConfig) ProtoMessage() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ProtoMessage")
}

// ProtoMessage indicates an expected call of ProtoMessage.
func (mr *MockChainConfigMockRecorder) ProtoMessage() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProtoMessage", reflect.TypeOf((*MockChainConfig)(nil).ProtoMessage))
}

// Reset mocks base method.
func (m *MockChainConfig) Reset() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Reset")
}

// Reset indicates an expected call of Reset.
func (mr *MockChainConfigMockRecorder) Reset() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reset", reflect.TypeOf((*MockChainConfig)(nil).Reset))
}

// String mocks base method.
func (m *MockChainConfig) String() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "String")
	ret0, _ := ret[0].(string)
	return ret0
}

// String indicates an expected call of String.
func (mr *MockChainConfigMockRecorder) String() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "String", reflect.TypeOf((*MockChainConfig)(nil).String))
}

// Validate mocks base method.
func (m *MockChainConfig) Validate() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Validate")
	ret0, _ := ret[0].(error)
	return ret0
}

// Validate indicates an expected call of Validate.
func (mr *MockChainConfigMockRecorder) Validate() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Validate", reflect.TypeOf((*MockChainConfig)(nil).Validate))
}

// MockProverConfig is a mock of ProverConfig interface.
type MockProverConfig struct {
	ctrl     *gomock.Controller
	recorder *MockProverConfigMockRecorder
}

// MockProverConfigMockRecorder is the mock recorder for MockProverConfig.
type MockProverConfigMockRecorder struct {
	mock *MockProverConfig
}

// NewMockProverConfig creates a new mock instance.
func NewMockProverConfig(ctrl *gomock.Controller) *MockProverConfig {
	mock := &MockProverConfig{ctrl: ctrl}
	mock.recorder = &MockProverConfigMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProverConfig) EXPECT() *MockProverConfigMockRecorder {
	return m.recorder
}

// Build mocks base method.
func (m *MockProverConfig) Build(arg0 Chain) (Prover, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Build", arg0)
	ret0, _ := ret[0].(Prover)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Build indicates an expected call of Build.
func (mr *MockProverConfigMockRecorder) Build(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Build", reflect.TypeOf((*MockProverConfig)(nil).Build), arg0)
}

// ProtoMessage mocks base method.
func (m *MockProverConfig) ProtoMessage() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ProtoMessage")
}

// ProtoMessage indicates an expected call of ProtoMessage.
func (mr *MockProverConfigMockRecorder) ProtoMessage() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProtoMessage", reflect.TypeOf((*MockProverConfig)(nil).ProtoMessage))
}

// Reset mocks base method.
func (m *MockProverConfig) Reset() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Reset")
}

// Reset indicates an expected call of Reset.
func (mr *MockProverConfigMockRecorder) Reset() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reset", reflect.TypeOf((*MockProverConfig)(nil).Reset))
}

// String mocks base method.
func (m *MockProverConfig) String() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "String")
	ret0, _ := ret[0].(string)
	return ret0
}

// String indicates an expected call of String.
func (mr *MockProverConfigMockRecorder) String() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "String", reflect.TypeOf((*MockProverConfig)(nil).String))
}

// Validate mocks base method.
func (m *MockProverConfig) Validate() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Validate")
	ret0, _ := ret[0].(error)
	return ret0
}

// Validate indicates an expected call of Validate.
func (mr *MockProverConfigMockRecorder) Validate() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Validate", reflect.TypeOf((*MockProverConfig)(nil).Validate))
}

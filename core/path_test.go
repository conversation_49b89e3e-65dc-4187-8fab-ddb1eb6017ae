//go:generate mockgen -source=$GOFILE -package=mock_core -destination=./mock_core/mock_$GOFILE -self_package=github.com/decurret-lab/dcbg-dcjpy-relayer/core
//go:generate gotests -w -all $GOFILE
package core

import (
	"reflect"
	"testing"
)

func TestPaths_MustYAML(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"

	accountSyncPaths := Paths{
		"account-sync": {
			Src: &PathEnd{
				ChainID:      srcChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-0",
			},
			Dst: &PathEnd{
				ChainID:      dstChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			Strategy: &StrategyCfg{
				Type:     "naive",
				SrcNoack: false,
				DstNoack: false,
			},
		},
	}
	balanceSyncPaths := Paths{
		"balance-sync": {
			Src: &PathEnd{
				ChainID:      srcChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-0",
			},
			Dst: &PathEnd{
				ChainID:      dstChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			Strategy: &StrategyCfg{
				Type:     "naive",
				SrcNoack: false,
				DstNoack: false,
			},
		},
	}
	tokenTransferPaths := Paths{
		"token-transfer": {
			Src: &PathEnd{
				ChainID:      srcChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-0",
			},
			Dst: &PathEnd{
				ChainID:      dstChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			},
			Strategy: &StrategyCfg{
				Type:     "naive",
				SrcNoack: false,
				DstNoack: false,
			},
		},
	}

	tests := []struct {
		name string
		p    Paths
		want string
	}{
		{
			name: "account-sync",
			p:    accountSyncPaths,
			want: accountSyncPaths.MustYAML(),
		},
		{
			name: "balance-sync",
			p:    balanceSyncPaths,
			want: balanceSyncPaths.MustYAML(),
		},
		{
			name: "token-transfer",
			p:    tokenTransferPaths,
			want: tokenTransferPaths.MustYAML(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.p.MustYAML(); got != tt.want {
				t.Errorf("Paths.MustYAML() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPaths_Get(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"

	accountSyncPaths := Paths{
		"account-sync": {
			Src: &PathEnd{
				ChainID:      srcChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-0",
			},
			Dst: &PathEnd{
				ChainID:      dstChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			Strategy: &StrategyCfg{
				Type:     "naive",
				SrcNoack: false,
				DstNoack: false,
			},
		},
	}
	balanceSyncPaths := Paths{
		"balance-sync": {
			Src: &PathEnd{
				ChainID:      srcChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-0",
			},
			Dst: &PathEnd{
				ChainID:      dstChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			Strategy: &StrategyCfg{
				Type:     "naive",
				SrcNoack: false,
				DstNoack: false,
			},
		},
	}
	tokenTransferPaths := Paths{
		"token-transfer": {
			Src: &PathEnd{
				ChainID:      srcChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-0",
			},
			Dst: &PathEnd{
				ChainID:      dstChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			},
			Strategy: &StrategyCfg{
				Type:     "naive",
				SrcNoack: false,
				DstNoack: false,
			},
		},
	}

	type args struct {
		name string
	}
	tests := []struct {
		name     string
		p        Paths
		args     args
		wantPath *Path
		wantErr  bool
	}{
		{
			name: "account-sync",
			p:    accountSyncPaths,
			args: args{
				name: "account-sync",
			},
			wantPath: &Path{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			wantErr: false,
		},
		{
			name: "balance-sync",
			p:    balanceSyncPaths,
			args: args{
				name: "balance-sync",
			},
			wantPath: &Path{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-1",
					PortID:       "balance-sync",
					Order:        "unordered",
					Version:      "balance-sync-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-1",
					PortID:       "balance-sync",
					Order:        "unordered",
					Version:      "balance-sync-1",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			wantErr: false,
		},
		{
			name: "token-transfer",
			p:    tokenTransferPaths,
			args: args{
				name: "token-transfer",
			},
			wantPath: &Path{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-2",
					PortID:       "token-transfer",
					Order:        "unordered",
					Version:      "token-transfer-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-2",
					PortID:       "token-transfer",
					Order:        "unordered",
					Version:      "token-transfer-1",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotPath, err := tt.p.Get(tt.args.name)
			if (err != nil) != tt.wantErr {
				t.Errorf("Paths.Get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotPath, tt.wantPath) {
				t.Errorf("Paths.Get() = %v, want %v", gotPath, tt.wantPath)
			}
		})
	}
}

func TestPaths_MustGet(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"

	accountSyncPaths := Paths{
		"account-sync": {
			Src: &PathEnd{
				ChainID:      srcChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-0",
			},
			Dst: &PathEnd{
				ChainID:      dstChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			Strategy: &StrategyCfg{
				Type:     "naive",
				SrcNoack: false,
				DstNoack: false,
			},
		},
	}
	balanceSyncPaths := Paths{
		"balance-sync": {
			Src: &PathEnd{
				ChainID:      srcChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-0",
			},
			Dst: &PathEnd{
				ChainID:      dstChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			Strategy: &StrategyCfg{
				Type:     "naive",
				SrcNoack: false,
				DstNoack: false,
			},
		},
	}
	tokenTransferPaths := Paths{
		"token-transfer": {
			Src: &PathEnd{
				ChainID:      srcChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-0",
			},
			Dst: &PathEnd{
				ChainID:      dstChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			},
			Strategy: &StrategyCfg{
				Type:     "naive",
				SrcNoack: false,
				DstNoack: false,
			},
		},
	}

	type args struct {
		name string
	}
	tests := []struct {
		name string
		p    Paths
		args args
		want *Path
	}{
		{
			name: "account-sync",
			p:    accountSyncPaths,
			args: args{
				name: "account-sync",
			},
			want: &Path{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
		},
		{
			name: "balance-sync",
			p:    balanceSyncPaths,
			args: args{
				name: "balance-sync",
			},
			want: &Path{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-1",
					PortID:       "balance-sync",
					Order:        "unordered",
					Version:      "balance-sync-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-1",
					PortID:       "balance-sync",
					Order:        "unordered",
					Version:      "balance-sync-1",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
		},
		{
			name: "token-transfer",
			p:    tokenTransferPaths,
			args: args{
				name: "token-transfer",
			},
			want: &Path{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-2",
					PortID:       "token-transfer",
					Order:        "unordered",
					Version:      "token-transfer-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-2",
					PortID:       "token-transfer",
					Order:        "unordered",
					Version:      "token-transfer-1",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.p.MustGet(tt.args.name); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Paths.MustGet() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPaths_Add(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"

	dummyPaths := Paths{}

	type args struct {
		name string
		path *Path
	}
	tests := []struct {
		name    string
		p       Paths
		args    args
		wantErr bool
	}{
		{
			name: "should adds a path by its name.",
			p:    dummyPaths,
			args: args{
				name: "additional-path",
				path: &Path{
					Src: &PathEnd{
						ChainID:      srcChainID,
						ClientID:     "hb-ibft2-10",
						ConnectionID: "connection-10",
						ChannelID:    "channel-10",
						PortID:       "account-sync",
						Order:        "unordered",
						Version:      "account-sync-10",
					},
					Dst: &PathEnd{
						ChainID:      dstChainID,
						ClientID:     "hb-ibft2-10",
						ConnectionID: "connection-10",
						ChannelID:    "channel-10",
						PortID:       "account-sync",
						Order:        "unordered",
						Version:      "account-sync-10",
					},
					Strategy: &StrategyCfg{
						Type:     "naive",
						SrcNoack: false,
						DstNoack: false,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.p.Add(tt.args.name, tt.args.path); (err != nil) != tt.wantErr {
				t.Errorf("Paths.Add() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPaths_AddForce(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"

	accountSyncPaths := Paths{
		"account-sync": {
			Src: &PathEnd{
				ChainID:      srcChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-0",
			},
			Dst: &PathEnd{
				ChainID:      dstChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			Strategy: &StrategyCfg{
				Type:     "naive",
				SrcNoack: false,
				DstNoack: false,
			},
		},
	}

	type args struct {
		name string
		path *Path
	}
	tests := []struct {
		name    string
		p       Paths
		args    args
		wantErr bool
	}{
		{
			name: "ignores existing paths and overwrites an existing path with that name.",
			p:    accountSyncPaths,
			args: args{
				name: "",
				path: &Path{
					Src: &PathEnd{
						ChainID:      "********",
						ClientID:     "hb-ibft2-1",
						ConnectionID: "connection-1",
						ChannelID:    "channel-1",
						PortID:       "account-sync",
						Order:        "unordered",
						Version:      "account-sync-1",
					},
					Dst: &PathEnd{
						ChainID:      "********",
						ClientID:     "hb-ibft2-1",
						ConnectionID: "connection-1",
						ChannelID:    "channel-1",
						PortID:       "account-sync",
						Order:        "unordered",
						Version:      "account-sync-1",
					},
					Strategy: &StrategyCfg{
						Type:     "naive",
						SrcNoack: true,
						DstNoack: true,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.p.AddForce(tt.args.name, tt.args.path); (err != nil) != tt.wantErr {
				t.Errorf("Paths.AddForce() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPath_MustYAML(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"

	accountSyncPath := Path{
		Src: &PathEnd{
			ChainID:      srcChainID,
			ClientID:     "hb-ibft2-0",
			ConnectionID: "connection-0",
			ChannelID:    "channel-0",
			PortID:       "account-sync",
			Order:        "unordered",
			Version:      "account-sync-0",
		},
		Dst: &PathEnd{
			ChainID:      dstChainID,
			ClientID:     "hb-ibft2-0",
			ConnectionID: "connection-0",
			ChannelID:    "channel-0",
			PortID:       "account-sync",
			Order:        "unordered",
			Version:      "account-sync-1",
		},
		Strategy: &StrategyCfg{
			Type:     "naive",
			SrcNoack: false,
			DstNoack: false,
		},
	}
	balanceSyncPath := Path{
		Src: &PathEnd{
			ChainID:      srcChainID,
			ClientID:     "hb-ibft2-0",
			ConnectionID: "connection-0",
			ChannelID:    "channel-1",
			PortID:       "balance-sync",
			Order:        "unordered",
			Version:      "balance-sync-0",
		},
		Dst: &PathEnd{
			ChainID:      dstChainID,
			ClientID:     "hb-ibft2-0",
			ConnectionID: "connection-0",
			ChannelID:    "channel-1",
			PortID:       "balance-sync",
			Order:        "unordered",
			Version:      "balance-sync-1",
		},
		Strategy: &StrategyCfg{
			Type:     "naive",
			SrcNoack: false,
			DstNoack: false,
		},
	}
	tokenTransferPath := Path{
		Src: &PathEnd{
			ChainID:      srcChainID,
			ClientID:     "hb-ibft2-0",
			ConnectionID: "connection-0",
			ChannelID:    "channel-2",
			PortID:       "token-transfer",
			Order:        "unordered",
			Version:      "token-transfer-0",
		},
		Dst: &PathEnd{
			ChainID:      dstChainID,
			ClientID:     "hb-ibft2-0",
			ConnectionID: "connection-0",
			ChannelID:    "channel-2",
			PortID:       "token-transfer",
			Order:        "unordered",
			Version:      "token-transfer-1",
		},
		Strategy: &StrategyCfg{
			Type:     "naive",
			SrcNoack: false,
			DstNoack: false,
		},
	}

	type fields struct {
		Src      *PathEnd
		Dst      *PathEnd
		Strategy *StrategyCfg
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "account-sync",
			fields: fields{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			want: accountSyncPath.MustYAML(),
		},
		{
			name: "balance-sync",
			fields: fields{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-1",
					PortID:       "balance-sync",
					Order:        "unordered",
					Version:      "balance-sync-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-1",
					PortID:       "balance-sync",
					Order:        "unordered",
					Version:      "balance-sync-1",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			want: balanceSyncPath.MustYAML(),
		},
		{
			name: "token-transfer",
			fields: fields{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-2",
					PortID:       "token-transfer",
					Order:        "unordered",
					Version:      "token-transfer-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-2",
					PortID:       "token-transfer",
					Order:        "unordered",
					Version:      "token-transfer-1",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			want: tokenTransferPath.MustYAML(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Path{
				Src:      tt.fields.Src,
				Dst:      tt.fields.Dst,
				Strategy: tt.fields.Strategy,
			}
			if got := p.MustYAML(); got != tt.want {
				t.Errorf("Path.MustYAML() = \n%#v\n, want %#v", got, tt.want)
			}
		})
	}
}

func TestPaths_PathsFromChains(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"

	testPaths := Paths{
		"account-sync": {
			Src: &PathEnd{
				ChainID:      srcChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-0",
			},
			Dst: &PathEnd{
				ChainID:      dstChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-1",
			},
			Strategy: &StrategyCfg{
				Type:     "naive",
				SrcNoack: false,
				DstNoack: false,
			},
		},
		"balance-sync": {
			Src: &PathEnd{
				ChainID:      srcChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-0",
			},
			Dst: &PathEnd{
				ChainID:      dstChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-1",
				PortID:       "balance-sync",
				Order:        "unordered",
				Version:      "balance-sync-1",
			},
			Strategy: &StrategyCfg{
				Type:     "naive",
				SrcNoack: false,
				DstNoack: false,
			},
		},
		"token-transfer": {
			Src: &PathEnd{
				ChainID:      srcChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-0",
			},
			Dst: &PathEnd{
				ChainID:      dstChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-2",
				PortID:       "token-transfer",
				Order:        "unordered",
				Version:      "token-transfer-1",
			},
			Strategy: &StrategyCfg{
				Type:     "naive",
				SrcNoack: false,
				DstNoack: false,
			},
		},
	}

	type args struct {
		src string
		dst string
	}
	tests := []struct {
		name    string
		p       Paths
		args    args
		want    Paths
		wantErr bool
	}{
		{
			name: "account-sync",
			p:    testPaths,
			args: args{
				src: srcChainID,
				dst: dstChainID,
			},
			want:    testPaths,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.p.PathsFromChains(tt.args.src, tt.args.dst)
			if (err != nil) != tt.wantErr {
				t.Errorf("Paths.PathsFromChains() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Paths.PathsFromChains() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPath_GenSrcClientID(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"

	type fields struct {
		Src      *PathEnd
		Dst      *PathEnd
		Strategy *StrategyCfg
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "should get src generates the specififed identifier.",
			fields: fields{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Path{
				Src:      tt.fields.Src,
				Dst:      tt.fields.Dst,
				Strategy: tt.fields.Strategy,
			}
			p.GenSrcClientID()
		})
	}
}

func TestPath_GenDstClientID(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"

	type fields struct {
		Src      *PathEnd
		Dst      *PathEnd
		Strategy *StrategyCfg
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "should get dst generates the specififed identifier.",
			fields: fields{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Path{
				Src:      tt.fields.Src,
				Dst:      tt.fields.Dst,
				Strategy: tt.fields.Strategy,
			}
			p.GenDstClientID()
		})
	}
}

func TestPath_GenSrcConnID(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"

	type fields struct {
		Src      *PathEnd
		Dst      *PathEnd
		Strategy *StrategyCfg
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "get src generates the specififed identifier",
			fields: fields{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Path{
				Src:      tt.fields.Src,
				Dst:      tt.fields.Dst,
				Strategy: tt.fields.Strategy,
			}
			p.GenSrcConnID()
		})
	}
}

func TestPath_GenDstConnID(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"

	type fields struct {
		Src      *PathEnd
		Dst      *PathEnd
		Strategy *StrategyCfg
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "get dst generates the specififed identifier",
			fields: fields{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Path{
				Src:      tt.fields.Src,
				Dst:      tt.fields.Dst,
				Strategy: tt.fields.Strategy,
			}
			p.GenDstConnID()
		})
	}
}

func TestPath_GenSrcChanID(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"

	type fields struct {
		Src      *PathEnd
		Dst      *PathEnd
		Strategy *StrategyCfg
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "get src generates the specififed identifier",
			fields: fields{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Path{
				Src:      tt.fields.Src,
				Dst:      tt.fields.Dst,
				Strategy: tt.fields.Strategy,
			}
			p.GenSrcChanID()
		})
	}
}

func TestPath_GenDstChanID(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"

	type fields struct {
		Src      *PathEnd
		Dst      *PathEnd
		Strategy *StrategyCfg
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "get dst generates the specififed identifier",
			fields: fields{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Path{
				Src:      tt.fields.Src,
				Dst:      tt.fields.Dst,
				Strategy: tt.fields.Strategy,
			}
			p.GenDstChanID()
		})
	}
}

func TestPath_Ordered(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"

	type fields struct {
		Src      *PathEnd
		Dst      *PathEnd
		Strategy *StrategyCfg
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "should returns true if the path is ordered and false if otherwise.",
			fields: fields{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Path{
				Src:      tt.fields.Src,
				Dst:      tt.fields.Dst,
				Strategy: tt.fields.Strategy,
			}
			if got := p.Ordered(); got != tt.want {
				t.Errorf("Path.Ordered() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPath_Validate(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"

	type fields struct {
		Src      *PathEnd
		Dst      *PathEnd
		Strategy *StrategyCfg
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "should returns true if the path is ordered and false if otherwise.",
			fields: fields{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Path{
				Src:      tt.fields.Src,
				Dst:      tt.fields.Dst,
				Strategy: tt.fields.Strategy,
			}
			if err := p.Validate(); (err != nil) != tt.wantErr {
				t.Errorf("Path.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPath_End(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"

	type fields struct {
		Src      *PathEnd
		Dst      *PathEnd
		Strategy *StrategyCfg
	}
	type args struct {
		chainID string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *PathEnd
	}{
		{
			name: "should returns src the proper end given a chainID.",
			fields: fields{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			args: args{
				chainID: srcChainID,
			},
			want: &PathEnd{
				ChainID:      srcChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-0",
			},
		},
		{
			name: "should returns dst the proper end given a chainID.",
			fields: fields{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			args: args{
				chainID: dstChainID,
			},
			want: &PathEnd{
				ChainID:      dstChainID,
				ClientID:     "hb-ibft2-0",
				ConnectionID: "connection-0",
				ChannelID:    "channel-0",
				PortID:       "account-sync",
				Order:        "unordered",
				Version:      "account-sync-0",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Path{
				Src:      tt.fields.Src,
				Dst:      tt.fields.Dst,
				Strategy: tt.fields.Strategy,
			}
			if got := p.End(tt.args.chainID); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Path.End() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPath_String(t *testing.T) {

	srcChainID := "********"
	dstChainID := "********"

	accountSyncPath := Path{
		Src: &PathEnd{
			ChainID:      srcChainID,
			ClientID:     "hb-ibft2-0",
			ConnectionID: "connection-0",
			ChannelID:    "channel-0",
			PortID:       "account-sync",
			Order:        "unordered",
			Version:      "account-sync-1",
		},
		Dst: &PathEnd{
			ChainID:      dstChainID,
			ClientID:     "hb-ibft2-0",
			ConnectionID: "connection-0",
			ChannelID:    "channel-0",
			PortID:       "account-sync",
			Order:        "unordered",
			Version:      "account-sync-1",
		},
		Strategy: &StrategyCfg{
			Type:     "naive",
			SrcNoack: false,
			DstNoack: false,
		},
	}
	balanceSyncPath := Path{
		Src: &PathEnd{
			ChainID:      srcChainID,
			ClientID:     "hb-ibft2-0",
			ConnectionID: "connection-0",
			ChannelID:    "channel-1",
			PortID:       "balance-sync",
			Order:        "unordered",
			Version:      "balance-sync-1",
		},
		Dst: &PathEnd{
			ChainID:      dstChainID,
			ClientID:     "hb-ibft2-0",
			ConnectionID: "connection-0",
			ChannelID:    "channel-1",
			PortID:       "balance-sync",
			Order:        "unordered",
			Version:      "balance-sync-1",
		},
		Strategy: &StrategyCfg{
			Type:     "naive",
			SrcNoack: false,
			DstNoack: false,
		},
	}
	tokenTransferPath := Path{
		Src: &PathEnd{
			ChainID:      srcChainID,
			ClientID:     "hb-ibft2-0",
			ConnectionID: "connection-0",
			ChannelID:    "channel-2",
			PortID:       "token-transfer",
			Order:        "unordered",
			Version:      "token-transfer-2",
		},
		Dst: &PathEnd{
			ChainID:      dstChainID,
			ClientID:     "hb-ibft2-0",
			ConnectionID: "connection-0",
			ChannelID:    "channel-2",
			PortID:       "token-transfer",
			Order:        "unordered",
			Version:      "token-transfer-2",
		},
		Strategy: &StrategyCfg{
			Type:     "naive",
			SrcNoack: false,
			DstNoack: false,
		},
	}

	type fields struct {
		Src      *PathEnd
		Dst      *PathEnd
		Strategy *StrategyCfg
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "account-sync",
			fields: fields{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			want: accountSyncPath.String(),
		},
		{
			name: "balance-sync",
			fields: fields{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-1",
					PortID:       "balance-sync",
					Order:        "unordered",
					Version:      "balance-sync-1",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-1",
					PortID:       "balance-sync",
					Order:        "unordered",
					Version:      "balance-sync-1",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			want: balanceSyncPath.String(),
		},
		{
			name: "token-transfer",
			fields: fields{
				Src: &PathEnd{
					ChainID:      srcChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-2",
					PortID:       "token-transfer",
					Order:        "unordered",
					Version:      "token-transfer-2",
				},
				Dst: &PathEnd{
					ChainID:      dstChainID,
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-2",
					PortID:       "token-transfer",
					Order:        "unordered",
					Version:      "token-transfer-2",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			want: tokenTransferPath.String(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Path{
				Src:      tt.fields.Src,
				Dst:      tt.fields.Dst,
				Strategy: tt.fields.Strategy,
			}
			if got := p.String(); got != tt.want {
				t.Errorf("Path.String() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGenPath(t *testing.T) {

	paramSrcChainID := "********"
	paramDstChainID := "********"

	type args struct {
		srcChainID string
		dstChainID string
		srcPortID  string
		dstPortID  string
		order      string
		version    string
	}
	tests := []struct {
		name string
		args args
		want *Path
	}{
		{
			name: "should should generates a path with random client, connection and channel identifiers given chainIDs and portIDs.",
			args: args{
				srcChainID: paramSrcChainID,
				dstChainID: paramDstChainID,
				srcPortID:  "account-sync",
				dstPortID:  "account-sync",
				order:      "unordered",
				version:    "account-sync-1",
			},
			want: &Path{
				Src: &PathEnd{
					ChainID:      paramSrcChainID,
					ClientID:     RandLowerCaseLetterString(10),
					ConnectionID: RandLowerCaseLetterString(10),
					ChannelID:    RandLowerCaseLetterString(10),
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Dst: &PathEnd{
					ChainID:      paramDstChainID,
					ClientID:     RandLowerCaseLetterString(10),
					ConnectionID: RandLowerCaseLetterString(10),
					ChannelID:    RandLowerCaseLetterString(10),
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-0",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GenPath(tt.args.srcChainID, tt.args.dstChainID, tt.args.srcPortID, tt.args.dstPortID, tt.args.order, tt.args.version)
			if got == nil {
				t.Errorf("GenPath() = %v, want %v", got, tt.want)
			}
		})
	}
}

//func TestPath_QueryPathStatus(t *testing.T) {
//
//	// Generating a Controller that manages mock calls
//	ctrl := gomock.NewController(t)
//
//	// Generate a mock instance
//	mockSrcChain := NewMockChain(ctrl)
//	mockDstChain := NewMockChain(ctrl)
//	mockSrcProver := NewMockProver(ctrl)
//	mockDstProver := NewMockProver(ctrl)
//	mockQueryContext := NewMockQueryContext(ctrl)
//	mockLightClient := NewMockLightClient(ctrl)
//	mockLightClient.EXPECT().CreateInitialLightClientState(ctrl)
//
//	mockQueryContext.EXPECT().Context().Return(context.TODO()).AnyTimes()
//	mockQueryContext.EXPECT().Height().Return(nil).AnyTimes()
//
//	mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//	mockSrcChain.EXPECT().LatestHeight().Return(nil, nil).AnyTimes()
//	mockSrcChain.EXPECT().QueryClientState(mockQueryContext).Return(nil, nil).AnyTimes()
//
//	mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//	mockDstChain.EXPECT().LatestHeight().Return(nil, nil).AnyTimes()
//	mockDstChain.EXPECT().QueryClientState(mockQueryContext).Return(nil, nil).AnyTimes()
//
//	paramSrcChainID := "********"
//	paramDstChainID := "********"
//
//	type fields struct {
//		Src      *PathEnd
//		Dst      *PathEnd
//		Strategy *StrategyCfg
//	}
//	type args struct {
//		src *ProvableChain
//		dst *ProvableChain
//	}
//	tests := []struct {
//		name   string
//		fields fields
//		args   args
//		want   *PathWithStatus
//	}{
//		// TODO: Add test cases.
//		{
//			name: "",
//			fields: fields{
//				Src: &PathEnd{
//					ChainID:      paramSrcChainID,
//					ClientID:     "hb-ibft2-0",
//					ConnectionID: "connection-0",
//					ChannelID:    "channel-0",
//					PortID:       "account-sync",
//					Order:        "unordered",
//					Version:      "account-sync-1",
//				},
//				Dst: &PathEnd{
//					ChainID:      paramDstChainID,
//					ClientID:     "hb-ibft2-0",
//					ConnectionID: "connection-0",
//					ChannelID:    "channel-0",
//					PortID:       "account-sync",
//					Order:        "unordered",
//					Version:      "account-sync-1",
//				},
//				Strategy: &StrategyCfg{
//					Type:     "naive",
//					SrcNoack: false,
//					DstNoack: false,
//				},
//			},
//			args: args{
//				src: &ProvableChain{
//					Chain:  mockSrcChain,
//					Prover: mockSrcProver,
//				},
//				dst: &ProvableChain{
//					Chain:  mockDstChain,
//					Prover: mockDstProver,
//				},
//			},
//			want: &PathWithStatus{
//				Path: &Path{
//					Src: &PathEnd{
//						ChainID:      paramSrcChainID,
//						ClientID:     "hb-ibft2-0",
//						ConnectionID: "connection-0",
//						ChannelID:    "channel-0",
//						PortID:       "account-sync",
//						Order:        "unordered",
//						Version:      "account-sync-1",
//					},
//					Dst: &PathEnd{
//						ChainID:      paramDstChainID,
//						ClientID:     "hb-ibft2-0",
//						ConnectionID: "connection-0",
//						ChannelID:    "channel-0",
//						PortID:       "account-sync",
//						Order:        "unordered",
//						Version:      "account-sync-1",
//					},
//					Strategy: &StrategyCfg{
//						Type:     "naive",
//						SrcNoack: false,
//						DstNoack: false,
//					},
//				},
//				Status: PathStatus{
//					Chains:     true,
//					Clients:    false,
//					Connection: false,
//					Channel:    false,
//				},
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			p := &Path{
//				Src:      tt.fields.Src,
//				Dst:      tt.fields.Dst,
//				Strategy: tt.fields.Strategy,
//			}
//			if got := p.QueryPathStatus(tt.args.src, tt.args.dst); !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("Path.QueryPathStatus() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func TestPathWithStatus_PrintString(t *testing.T) {

	type fields struct {
		Path   *Path
		Status PathStatus
	}
	type args struct {
		name string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ps := &PathWithStatus{
				Path:   tt.fields.Path,
				Status: tt.fields.Status,
			}
			if got := ps.PrintString(tt.args.name); got != tt.want {
				t.Errorf("PathWithStatus.PrintString() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_checkmark(t *testing.T) {
	type args struct {
		status bool
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "should return check.",
			args: args{
				status: true,
			},
			want: check,
		},
		{
			name: "should return xIcon.",
			args: args{
				status: false,
			},
			want: xIcon,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := checkmark(tt.args.status); got != tt.want {
				t.Errorf("checkmark() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRandLowerCaseLetterString(t *testing.T) {

	type args struct {
		length int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "should returns a lowercase letter string of given length 1",
			args: args{
				length: 1,
			},
			want: RandLowerCaseLetterString(1),
		},
		{
			name: "should returns a lowercase letter string of given length 10",
			args: args{
				length: 10,
			},
			want: RandLowerCaseLetterString(10),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := RandLowerCaseLetterString(tt.args.length)
			if got == "" {
				t.Errorf("RandLowerCaseLetterString() = %v, want %v", got, tt.want)
			}
		})
	}
}

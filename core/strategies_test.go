//go:generate mockgen -source=$GOFILE -package=mock_core -destination=./mock_core/mock_$GOFILE -self_package=github.com/decurret-lab/dcbg-dcjpy-relayer/core
//go:generate gotests -w -all $GOFILE
package core

import (
	"reflect"
	"testing"
)

func TestGetStrategy(t *testing.T) {
	type args struct {
		cfg StrategyCfg
	}
	tests := []struct {
		name    string
		args    args
		want    StrategyI
		wantErr bool
	}{
		{
			name: "should return NaiveStrategy.",
			args: args{
				cfg: StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			want: &NaiveStrategy{
				srcNoAck: false,
				dstNoAck: false,
			},
			wantErr: false,
		},
		{
			name: "should cause an error.",
			args: args{
				cfg: StrategyCfg{
					Type:     "other",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetStrategy(tt.args.cfg)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetStrategy() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetStrategy() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPath_ValidateStrategy(t *testing.T) {
	type fields struct {
		Src      *PathEnd
		Dst      *PathEnd
		Strategy *StrategyCfg
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "should validates that the strategy of path `p` is valid.",
			fields: fields{
				Src: &PathEnd{
					ChainID:      "********",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				Dst: &PathEnd{
					ChainID:      "********",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				Strategy: &StrategyCfg{
					Type:     "naive",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			wantErr: false,
		},
		{
			name: "should cause an error.",
			fields: fields{
				Src: &PathEnd{
					ChainID:      "********",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				Dst: &PathEnd{
					ChainID:      "********",
					ClientID:     "hb-ibft2-0",
					ConnectionID: "connection-0",
					ChannelID:    "channel-0",
					PortID:       "account-sync",
					Order:        "unordered",
					Version:      "account-sync-1",
				},
				Strategy: &StrategyCfg{
					Type:     "other",
					SrcNoack: false,
					DstNoack: false,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Path{
				Src:      tt.fields.Src,
				Dst:      tt.fields.Dst,
				Strategy: tt.fields.Strategy,
			}
			if err := p.ValidateStrategy(); (err != nil) != tt.wantErr {
				t.Errorf("Path.ValidateStrategy() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

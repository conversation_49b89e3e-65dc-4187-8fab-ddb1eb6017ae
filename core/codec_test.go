//go:generate mockgen -source=$GOFILE -package=core -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package core

import (
	"reflect"
	"testing"

	"github.com/cosmos/cosmos-sdk/codec"
)

func TestMakeCodec(t *testing.T) {

	mockCodec := MakeCodec()

	tests := []struct {
		name string
		want codec.ProtoCodecMarshaler
	}{
		{
			name: "should returns a reference to a new ProtoCodec.",
			want: mockCodec,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := MakeCodec(); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("MakeCodec() = %v, want %v", got, tt.want)
			}
		})
	}
}

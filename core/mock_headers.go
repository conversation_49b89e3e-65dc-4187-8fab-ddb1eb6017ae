// Code generated by MockGen. DO NOT EDIT.
// Source: headers.go
//
// Generated by this command:
//
//	mockgen -source=headers.go -package=core -destination=./mock_headers.go
//

// Package core is a generated GoMock package.
package core

import (
	context "context"
	reflect "reflect"
	time "time"

	codec "github.com/cosmos/cosmos-sdk/codec"
	types "github.com/cosmos/cosmos-sdk/types"
	types0 "github.com/cosmos/ibc-go/v7/modules/apps/transfer/types"
	types1 "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	types2 "github.com/cosmos/ibc-go/v7/modules/core/03-connection/types"
	types3 "github.com/cosmos/ibc-go/v7/modules/core/04-channel/types"
	exported "github.com/cosmos/ibc-go/v7/modules/core/exported"
	gomock "go.uber.org/mock/gomock"
)

// MockHeader is a mock of Header interface.
type MockHeader struct {
	ctrl     *gomock.Controller
	recorder *MockHeaderMockRecorder
}

// MockHeaderMockRecorder is the mock recorder for MockHeader.
type MockHeaderMockRecorder struct {
	mock *MockHeader
}

// NewMockHeader creates a new mock instance.
func NewMockHeader(ctrl *gomock.Controller) *MockHeader {
	mock := &MockHeader{ctrl: ctrl}
	mock.recorder = &MockHeaderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockHeader) EXPECT() *MockHeaderMockRecorder {
	return m.recorder
}

// ClientType mocks base method.
func (m *MockHeader) ClientType() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClientType")
	ret0, _ := ret[0].(string)
	return ret0
}

// ClientType indicates an expected call of ClientType.
func (mr *MockHeaderMockRecorder) ClientType() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClientType", reflect.TypeOf((*MockHeader)(nil).ClientType))
}

// GetHeight mocks base method.
func (m *MockHeader) GetHeight() exported.Height {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHeight")
	ret0, _ := ret[0].(exported.Height)
	return ret0
}

// GetHeight indicates an expected call of GetHeight.
func (mr *MockHeaderMockRecorder) GetHeight() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHeight", reflect.TypeOf((*MockHeader)(nil).GetHeight))
}

// ProtoMessage mocks base method.
func (m *MockHeader) ProtoMessage() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ProtoMessage")
}

// ProtoMessage indicates an expected call of ProtoMessage.
func (mr *MockHeaderMockRecorder) ProtoMessage() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProtoMessage", reflect.TypeOf((*MockHeader)(nil).ProtoMessage))
}

// Reset mocks base method.
func (m *MockHeader) Reset() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Reset")
}

// Reset indicates an expected call of Reset.
func (mr *MockHeaderMockRecorder) Reset() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reset", reflect.TypeOf((*MockHeader)(nil).Reset))
}

// String mocks base method.
func (m *MockHeader) String() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "String")
	ret0, _ := ret[0].(string)
	return ret0
}

// String indicates an expected call of String.
func (mr *MockHeaderMockRecorder) String() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "String", reflect.TypeOf((*MockHeader)(nil).String))
}

// ValidateBasic mocks base method.
func (m *MockHeader) ValidateBasic() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateBasic")
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateBasic indicates an expected call of ValidateBasic.
func (mr *MockHeaderMockRecorder) ValidateBasic() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateBasic", reflect.TypeOf((*MockHeader)(nil).ValidateBasic))
}

// MockSyncHeaders is a mock of SyncHeaders interface.
type MockSyncHeaders struct {
	ctrl     *gomock.Controller
	recorder *MockSyncHeadersMockRecorder
}

// MockSyncHeadersMockRecorder is the mock recorder for MockSyncHeaders.
type MockSyncHeadersMockRecorder struct {
	mock *MockSyncHeaders
}

// NewMockSyncHeaders creates a new mock instance.
func NewMockSyncHeaders(ctrl *gomock.Controller) *MockSyncHeaders {
	mock := &MockSyncHeaders{ctrl: ctrl}
	mock.recorder = &MockSyncHeadersMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSyncHeaders) EXPECT() *MockSyncHeadersMockRecorder {
	return m.recorder
}

// GetLatestFinalizedHeader mocks base method.
func (m *MockSyncHeaders) GetLatestFinalizedHeader(chainID string) Header {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestFinalizedHeader", chainID)
	ret0, _ := ret[0].(Header)
	return ret0
}

// GetLatestFinalizedHeader indicates an expected call of GetLatestFinalizedHeader.
func (mr *MockSyncHeadersMockRecorder) GetLatestFinalizedHeader(chainID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestFinalizedHeader", reflect.TypeOf((*MockSyncHeaders)(nil).GetLatestFinalizedHeader), chainID)
}

// GetQueryContext mocks base method.
func (m *MockSyncHeaders) GetQueryContext(chainID string) QueryContext {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQueryContext", chainID)
	ret0, _ := ret[0].(QueryContext)
	return ret0
}

// GetQueryContext indicates an expected call of GetQueryContext.
func (mr *MockSyncHeadersMockRecorder) GetQueryContext(chainID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQueryContext", reflect.TypeOf((*MockSyncHeaders)(nil).GetQueryContext), chainID)
}

// SetupBothHeadersForUpdate mocks base method.
func (m *MockSyncHeaders) SetupBothHeadersForUpdate(src, dst ChainLightClient) ([]Header, []Header, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetupBothHeadersForUpdate", src, dst)
	ret0, _ := ret[0].([]Header)
	ret1, _ := ret[1].([]Header)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// SetupBothHeadersForUpdate indicates an expected call of SetupBothHeadersForUpdate.
func (mr *MockSyncHeadersMockRecorder) SetupBothHeadersForUpdate(src, dst any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetupBothHeadersForUpdate", reflect.TypeOf((*MockSyncHeaders)(nil).SetupBothHeadersForUpdate), src, dst)
}

// SetupHeadersForUpdate mocks base method.
func (m *MockSyncHeaders) SetupHeadersForUpdate(src, dst ChainLightClient) ([]Header, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetupHeadersForUpdate", src, dst)
	ret0, _ := ret[0].([]Header)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetupHeadersForUpdate indicates an expected call of SetupHeadersForUpdate.
func (mr *MockSyncHeadersMockRecorder) SetupHeadersForUpdate(src, dst any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetupHeadersForUpdate", reflect.TypeOf((*MockSyncHeaders)(nil).SetupHeadersForUpdate), src, dst)
}

// Updates mocks base method.
func (m *MockSyncHeaders) Updates(src, dst ChainInfoLightClient) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Updates", src, dst)
	ret0, _ := ret[0].(error)
	return ret0
}

// Updates indicates an expected call of Updates.
func (mr *MockSyncHeadersMockRecorder) Updates(src, dst any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Updates", reflect.TypeOf((*MockSyncHeaders)(nil).Updates), src, dst)
}

// MockChainInfoLightClient is a mock of ChainInfoLightClient interface.
type MockChainInfoLightClient struct {
	ctrl     *gomock.Controller
	recorder *MockChainInfoLightClientMockRecorder
}

// MockChainInfoLightClientMockRecorder is the mock recorder for MockChainInfoLightClient.
type MockChainInfoLightClientMockRecorder struct {
	mock *MockChainInfoLightClient
}

// NewMockChainInfoLightClient creates a new mock instance.
func NewMockChainInfoLightClient(ctrl *gomock.Controller) *MockChainInfoLightClient {
	mock := &MockChainInfoLightClient{ctrl: ctrl}
	mock.recorder = &MockChainInfoLightClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChainInfoLightClient) EXPECT() *MockChainInfoLightClientMockRecorder {
	return m.recorder
}

// AverageBlockTime mocks base method.
func (m *MockChainInfoLightClient) AverageBlockTime() time.Duration {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AverageBlockTime")
	ret0, _ := ret[0].(time.Duration)
	return ret0
}

// AverageBlockTime indicates an expected call of AverageBlockTime.
func (mr *MockChainInfoLightClientMockRecorder) AverageBlockTime() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AverageBlockTime", reflect.TypeOf((*MockChainInfoLightClient)(nil).AverageBlockTime))
}

// ChainID mocks base method.
func (m *MockChainInfoLightClient) ChainID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChainID")
	ret0, _ := ret[0].(string)
	return ret0
}

// ChainID indicates an expected call of ChainID.
func (mr *MockChainInfoLightClientMockRecorder) ChainID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChainID", reflect.TypeOf((*MockChainInfoLightClient)(nil).ChainID))
}

// CheckRefreshRequired mocks base method.
func (m *MockChainInfoLightClient) CheckRefreshRequired(counterparty ChainInfoICS02Querier) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckRefreshRequired", counterparty)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckRefreshRequired indicates an expected call of CheckRefreshRequired.
func (mr *MockChainInfoLightClientMockRecorder) CheckRefreshRequired(counterparty any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckRefreshRequired", reflect.TypeOf((*MockChainInfoLightClient)(nil).CheckRefreshRequired), counterparty)
}

// CreateInitialLightClientState mocks base method.
func (m *MockChainInfoLightClient) CreateInitialLightClientState(height exported.Height) (exported.ClientState, exported.ConsensusState, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInitialLightClientState", height)
	ret0, _ := ret[0].(exported.ClientState)
	ret1, _ := ret[1].(exported.ConsensusState)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CreateInitialLightClientState indicates an expected call of CreateInitialLightClientState.
func (mr *MockChainInfoLightClientMockRecorder) CreateInitialLightClientState(height any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInitialLightClientState", reflect.TypeOf((*MockChainInfoLightClient)(nil).CreateInitialLightClientState), height)
}

// GetLatestFinalizedHeader mocks base method.
func (m *MockChainInfoLightClient) GetLatestFinalizedHeader() (Header, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestFinalizedHeader")
	ret0, _ := ret[0].(Header)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestFinalizedHeader indicates an expected call of GetLatestFinalizedHeader.
func (mr *MockChainInfoLightClientMockRecorder) GetLatestFinalizedHeader() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestFinalizedHeader", reflect.TypeOf((*MockChainInfoLightClient)(nil).GetLatestFinalizedHeader))
}

// LatestHeight mocks base method.
func (m *MockChainInfoLightClient) LatestHeight() (exported.Height, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LatestHeight")
	ret0, _ := ret[0].(exported.Height)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LatestHeight indicates an expected call of LatestHeight.
func (mr *MockChainInfoLightClientMockRecorder) LatestHeight() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LatestHeight", reflect.TypeOf((*MockChainInfoLightClient)(nil).LatestHeight))
}

// SetupHeadersForUpdate mocks base method.
func (m *MockChainInfoLightClient) SetupHeadersForUpdate(counterparty FinalityAwareChain, latestFinalizedHeader Header) ([]Header, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetupHeadersForUpdate", counterparty, latestFinalizedHeader)
	ret0, _ := ret[0].([]Header)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetupHeadersForUpdate indicates an expected call of SetupHeadersForUpdate.
func (mr *MockChainInfoLightClientMockRecorder) SetupHeadersForUpdate(counterparty, latestFinalizedHeader any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetupHeadersForUpdate", reflect.TypeOf((*MockChainInfoLightClient)(nil).SetupHeadersForUpdate), counterparty, latestFinalizedHeader)
}

// Timestamp mocks base method.
func (m *MockChainInfoLightClient) Timestamp(arg0 exported.Height) (time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Timestamp", arg0)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Timestamp indicates an expected call of Timestamp.
func (mr *MockChainInfoLightClientMockRecorder) Timestamp(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Timestamp", reflect.TypeOf((*MockChainInfoLightClient)(nil).Timestamp), arg0)
}

// MockChainLightClient is a mock of ChainLightClient interface.
type MockChainLightClient struct {
	ctrl     *gomock.Controller
	recorder *MockChainLightClientMockRecorder
}

// MockChainLightClientMockRecorder is the mock recorder for MockChainLightClient.
type MockChainLightClientMockRecorder struct {
	mock *MockChainLightClient
}

// NewMockChainLightClient creates a new mock instance.
func NewMockChainLightClient(ctrl *gomock.Controller) *MockChainLightClient {
	mock := &MockChainLightClient{ctrl: ctrl}
	mock.recorder = &MockChainLightClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChainLightClient) EXPECT() *MockChainLightClientMockRecorder {
	return m.recorder
}

// AverageBlockTime mocks base method.
func (m *MockChainLightClient) AverageBlockTime() time.Duration {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AverageBlockTime")
	ret0, _ := ret[0].(time.Duration)
	return ret0
}

// AverageBlockTime indicates an expected call of AverageBlockTime.
func (mr *MockChainLightClientMockRecorder) AverageBlockTime() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AverageBlockTime", reflect.TypeOf((*MockChainLightClient)(nil).AverageBlockTime))
}

// ChainID mocks base method.
func (m *MockChainLightClient) ChainID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChainID")
	ret0, _ := ret[0].(string)
	return ret0
}

// ChainID indicates an expected call of ChainID.
func (mr *MockChainLightClientMockRecorder) ChainID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChainID", reflect.TypeOf((*MockChainLightClient)(nil).ChainID))
}

// CheckRefreshRequired mocks base method.
func (m *MockChainLightClient) CheckRefreshRequired(counterparty ChainInfoICS02Querier) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckRefreshRequired", counterparty)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckRefreshRequired indicates an expected call of CheckRefreshRequired.
func (mr *MockChainLightClientMockRecorder) CheckRefreshRequired(counterparty any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckRefreshRequired", reflect.TypeOf((*MockChainLightClient)(nil).CheckRefreshRequired), counterparty)
}

// Codec mocks base method.
func (m *MockChainLightClient) Codec() codec.ProtoCodecMarshaler {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Codec")
	ret0, _ := ret[0].(codec.ProtoCodecMarshaler)
	return ret0
}

// Codec indicates an expected call of Codec.
func (mr *MockChainLightClientMockRecorder) Codec() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Codec", reflect.TypeOf((*MockChainLightClient)(nil).Codec))
}

// CreateInitialLightClientState mocks base method.
func (m *MockChainLightClient) CreateInitialLightClientState(height exported.Height) (exported.ClientState, exported.ConsensusState, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInitialLightClientState", height)
	ret0, _ := ret[0].(exported.ClientState)
	ret1, _ := ret[1].(exported.ConsensusState)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CreateInitialLightClientState indicates an expected call of CreateInitialLightClientState.
func (mr *MockChainLightClientMockRecorder) CreateInitialLightClientState(height any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInitialLightClientState", reflect.TypeOf((*MockChainLightClient)(nil).CreateInitialLightClientState), height)
}

// GetAddress mocks base method.
func (m *MockChainLightClient) GetAddress() (types.AccAddress, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAddress")
	ret0, _ := ret[0].(types.AccAddress)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAddress indicates an expected call of GetAddress.
func (mr *MockChainLightClientMockRecorder) GetAddress() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAddress", reflect.TypeOf((*MockChainLightClient)(nil).GetAddress))
}

// GetLatestFinalizedHeader mocks base method.
func (m *MockChainLightClient) GetLatestFinalizedHeader() (Header, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestFinalizedHeader")
	ret0, _ := ret[0].(Header)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestFinalizedHeader indicates an expected call of GetLatestFinalizedHeader.
func (mr *MockChainLightClientMockRecorder) GetLatestFinalizedHeader() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestFinalizedHeader", reflect.TypeOf((*MockChainLightClient)(nil).GetLatestFinalizedHeader))
}

// GetMsgResult mocks base method.
func (m *MockChainLightClient) GetMsgResult(id MsgID) (MsgResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMsgResult", id)
	ret0, _ := ret[0].(MsgResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMsgResult indicates an expected call of GetMsgResult.
func (mr *MockChainLightClientMockRecorder) GetMsgResult(id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMsgResult", reflect.TypeOf((*MockChainLightClient)(nil).GetMsgResult), id)
}

// Init mocks base method.
func (m *MockChainLightClient) Init(homePath string, timeout time.Duration, codec codec.ProtoCodecMarshaler, debug bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Init", homePath, timeout, codec, debug)
	ret0, _ := ret[0].(error)
	return ret0
}

// Init indicates an expected call of Init.
func (mr *MockChainLightClientMockRecorder) Init(homePath, timeout, codec, debug any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Init", reflect.TypeOf((*MockChainLightClient)(nil).Init), homePath, timeout, codec, debug)
}

// LatestHeight mocks base method.
func (m *MockChainLightClient) LatestHeight() (exported.Height, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LatestHeight")
	ret0, _ := ret[0].(exported.Height)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LatestHeight indicates an expected call of LatestHeight.
func (mr *MockChainLightClientMockRecorder) LatestHeight() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LatestHeight", reflect.TypeOf((*MockChainLightClient)(nil).LatestHeight))
}

// Path mocks base method.
func (m *MockChainLightClient) Path() *PathEnd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Path")
	ret0, _ := ret[0].(*PathEnd)
	return ret0
}

// Path indicates an expected call of Path.
func (mr *MockChainLightClientMockRecorder) Path() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Path", reflect.TypeOf((*MockChainLightClient)(nil).Path))
}

// QueryBalance mocks base method.
func (m *MockChainLightClient) QueryBalance(ctx QueryContext, address types.AccAddress) (types.Coins, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryBalance", ctx, address)
	ret0, _ := ret[0].(types.Coins)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryBalance indicates an expected call of QueryBalance.
func (mr *MockChainLightClientMockRecorder) QueryBalance(ctx, address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryBalance", reflect.TypeOf((*MockChainLightClient)(nil).QueryBalance), ctx, address)
}

// QueryChannel mocks base method.
func (m *MockChainLightClient) QueryChannel(ctx QueryContext) (*types3.QueryChannelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryChannel", ctx)
	ret0, _ := ret[0].(*types3.QueryChannelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryChannel indicates an expected call of QueryChannel.
func (mr *MockChainLightClientMockRecorder) QueryChannel(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryChannel", reflect.TypeOf((*MockChainLightClient)(nil).QueryChannel), ctx)
}

// QueryClientConsensusState mocks base method.
func (m *MockChainLightClient) QueryClientConsensusState(ctx QueryContext, dstClientConsHeight exported.Height) (*types1.QueryConsensusStateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryClientConsensusState", ctx, dstClientConsHeight)
	ret0, _ := ret[0].(*types1.QueryConsensusStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryClientConsensusState indicates an expected call of QueryClientConsensusState.
func (mr *MockChainLightClientMockRecorder) QueryClientConsensusState(ctx, dstClientConsHeight any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryClientConsensusState", reflect.TypeOf((*MockChainLightClient)(nil).QueryClientConsensusState), ctx, dstClientConsHeight)
}

// QueryClientState mocks base method.
func (m *MockChainLightClient) QueryClientState(ctx QueryContext) (*types1.QueryClientStateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryClientState", ctx)
	ret0, _ := ret[0].(*types1.QueryClientStateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryClientState indicates an expected call of QueryClientState.
func (mr *MockChainLightClientMockRecorder) QueryClientState(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryClientState", reflect.TypeOf((*MockChainLightClient)(nil).QueryClientState), ctx)
}

// QueryConnection mocks base method.
func (m *MockChainLightClient) QueryConnection(ctx QueryContext) (*types2.QueryConnectionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryConnection", ctx)
	ret0, _ := ret[0].(*types2.QueryConnectionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryConnection indicates an expected call of QueryConnection.
func (mr *MockChainLightClientMockRecorder) QueryConnection(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryConnection", reflect.TypeOf((*MockChainLightClient)(nil).QueryConnection), ctx)
}

// QueryDenomTraces mocks base method.
func (m *MockChainLightClient) QueryDenomTraces(ctx QueryContext, offset, limit uint64) (*types0.QueryDenomTracesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryDenomTraces", ctx, offset, limit)
	ret0, _ := ret[0].(*types0.QueryDenomTracesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryDenomTraces indicates an expected call of QueryDenomTraces.
func (mr *MockChainLightClientMockRecorder) QueryDenomTraces(ctx, offset, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryDenomTraces", reflect.TypeOf((*MockChainLightClient)(nil).QueryDenomTraces), ctx, offset, limit)
}

// QueryUnfinalizedRelayAcknowledgements mocks base method.
func (m *MockChainLightClient) QueryUnfinalizedRelayAcknowledgements(ctx QueryContext, counterparty LightClientICS04Querier) (PacketInfoList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnfinalizedRelayAcknowledgements", ctx, counterparty)
	ret0, _ := ret[0].(PacketInfoList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnfinalizedRelayAcknowledgements indicates an expected call of QueryUnfinalizedRelayAcknowledgements.
func (mr *MockChainLightClientMockRecorder) QueryUnfinalizedRelayAcknowledgements(ctx, counterparty any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnfinalizedRelayAcknowledgements", reflect.TypeOf((*MockChainLightClient)(nil).QueryUnfinalizedRelayAcknowledgements), ctx, counterparty)
}

// QueryUnfinalizedRelayPackets mocks base method.
func (m *MockChainLightClient) QueryUnfinalizedRelayPackets(ctx QueryContext, counterparty LightClientICS04Querier) (PacketInfoList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnfinalizedRelayPackets", ctx, counterparty)
	ret0, _ := ret[0].(PacketInfoList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnfinalizedRelayPackets indicates an expected call of QueryUnfinalizedRelayPackets.
func (mr *MockChainLightClientMockRecorder) QueryUnfinalizedRelayPackets(ctx, counterparty any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnfinalizedRelayPackets", reflect.TypeOf((*MockChainLightClient)(nil).QueryUnfinalizedRelayPackets), ctx, counterparty)
}

// QueryUnreceivedAcknowledgements mocks base method.
func (m *MockChainLightClient) QueryUnreceivedAcknowledgements(ctx QueryContext, seqs []uint64) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnreceivedAcknowledgements", ctx, seqs)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnreceivedAcknowledgements indicates an expected call of QueryUnreceivedAcknowledgements.
func (mr *MockChainLightClientMockRecorder) QueryUnreceivedAcknowledgements(ctx, seqs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnreceivedAcknowledgements", reflect.TypeOf((*MockChainLightClient)(nil).QueryUnreceivedAcknowledgements), ctx, seqs)
}

// QueryUnreceivedPackets mocks base method.
func (m *MockChainLightClient) QueryUnreceivedPackets(ctx QueryContext, seqs []uint64) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUnreceivedPackets", ctx, seqs)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUnreceivedPackets indicates an expected call of QueryUnreceivedPackets.
func (mr *MockChainLightClientMockRecorder) QueryUnreceivedPackets(ctx, seqs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUnreceivedPackets", reflect.TypeOf((*MockChainLightClient)(nil).QueryUnreceivedPackets), ctx, seqs)
}

// RegisterMsgEventListener mocks base method.
func (m *MockChainLightClient) RegisterMsgEventListener(arg0 MsgEventListener) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RegisterMsgEventListener", arg0)
}

// RegisterMsgEventListener indicates an expected call of RegisterMsgEventListener.
func (mr *MockChainLightClientMockRecorder) RegisterMsgEventListener(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterMsgEventListener", reflect.TypeOf((*MockChainLightClient)(nil).RegisterMsgEventListener), arg0)
}

// SendMsgs mocks base method.
func (m *MockChainLightClient) SendMsgs(msgs []types.Msg) ([]MsgID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMsgs", msgs)
	ret0, _ := ret[0].([]MsgID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendMsgs indicates an expected call of SendMsgs.
func (mr *MockChainLightClientMockRecorder) SendMsgs(msgs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMsgs", reflect.TypeOf((*MockChainLightClient)(nil).SendMsgs), msgs)
}

// SetRelayInfo mocks base method.
func (m *MockChainLightClient) SetRelayInfo(path *PathEnd, counterparty *ProvableChain, counterpartyPath *PathEnd) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRelayInfo", path, counterparty, counterpartyPath)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetRelayInfo indicates an expected call of SetRelayInfo.
func (mr *MockChainLightClientMockRecorder) SetRelayInfo(path, counterparty, counterpartyPath any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRelayInfo", reflect.TypeOf((*MockChainLightClient)(nil).SetRelayInfo), path, counterparty, counterpartyPath)
}

// SetupForRelay mocks base method.
func (m *MockChainLightClient) SetupForRelay(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetupForRelay", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetupForRelay indicates an expected call of SetupForRelay.
func (mr *MockChainLightClientMockRecorder) SetupForRelay(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetupForRelay", reflect.TypeOf((*MockChainLightClient)(nil).SetupForRelay), ctx)
}

// SetupHeadersForUpdate mocks base method.
func (m *MockChainLightClient) SetupHeadersForUpdate(counterparty FinalityAwareChain, latestFinalizedHeader Header) ([]Header, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetupHeadersForUpdate", counterparty, latestFinalizedHeader)
	ret0, _ := ret[0].([]Header)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetupHeadersForUpdate indicates an expected call of SetupHeadersForUpdate.
func (mr *MockChainLightClientMockRecorder) SetupHeadersForUpdate(counterparty, latestFinalizedHeader any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetupHeadersForUpdate", reflect.TypeOf((*MockChainLightClient)(nil).SetupHeadersForUpdate), counterparty, latestFinalizedHeader)
}

// Timestamp mocks base method.
func (m *MockChainLightClient) Timestamp(arg0 exported.Height) (time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Timestamp", arg0)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Timestamp indicates an expected call of Timestamp.
func (mr *MockChainLightClientMockRecorder) Timestamp(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Timestamp", reflect.TypeOf((*MockChainLightClient)(nil).Timestamp), arg0)
}

//go:generate mockgen -source=$GOFILE -package=core -destination=./mock_$GOFILE
//go:generate gotests -w -all $GOFILE
package core

import (
	"encoding/json"
	"reflect"
	"testing"

	"go.uber.org/mock/gomock"
)

func TestSetCoreConfig(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockConfigI := NewMockConfigI(ctrl)

	type args struct {
		c ConfigI
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "should set config.",
			args: args{
				c: mockConfigI,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			SetCoreConfig(tt.args.c)
		})
	}
}

// TODO ChainConfig と ProverConfig の `proto.Message` に値が設定できずテスト実施できない
//func TestNewChainProverConfig(t *testing.T) {
//
//	// Generating a Controller that manages mock calls
//	ctrl := gomock.NewController(t)
//
//	// Generate a mock instance
//	mockChainId := "chain-1"
//	mockPath :=
//		&PathEnd{
//			ChainID:      "chain-0",
//			ClientID:     "hb-ibft2-0",
//			ConnectionID: "connection-0",
//			ChannelID:    "channel-0",
//			PortID:       "account-sync",
//			Order:        "unordered",
//			Version:      "account-sync-1",
//		}
//
//	mockChain := NewMockChain(ctrl)
//	mockProver := NewMockProver(ctrl)
//
//	mockChainConfig := NewMockChainConfig(ctrl)
//	mockProverConfig := NewMockProverConfig(ctrl)
//	mockCodec := MakeCodec()
//
//	mockChain.EXPECT().ChainID().Return(mockChainId).AnyTimes()
//	mockChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
//	mockChain.EXPECT().Path().Return(mockPath).AnyTimes()
//
//	chainProtoMessage := json.RawMessage(`"chain":{"@type":"/relayer.chains.ethereum.config.ChainConfig","chain_id":"********","eth_chain_id":5151,"rpc_addr":"http://**********:8451","signer":{"@type":"/relayer.chains.ethereum.signers.hd.SignerConfig","mnemonic":"mathrazorcapableexposeworthgrapemetalsunsetmetalsuddenusagescheme","path":"m/44'/60'/0'/0/0"},"ibc_address":"******************************************","initial_send_checkpoint":1,"initial_recv_checkpoint":1,"enable_debug_trace":false,"average_block_time_msec":2000,"max_retry_for_inclusion":3,"gas_estimate_rate":{"numerator":1,"denominator":1},"max_gas_limit":200000000,"tx_type":"auto"}`)
//	mockChainConfig.EXPECT().ProtoMessage().Return(chainProtoMessage).AnyTimes()
//	mockChainConfig.EXPECT().Build().Return(mockChain, nil).AnyTimes()
//	mockChainConfig.EXPECT().Validate().Return(nil).AnyTimes()
//
//	proverProtoMessage := json.RawMessage(`"prover":{"@type":"/relayer.provers.ibft2.config.ProverConfig","trust_level_numerator":1,"trust_level_denominator":3,"trusting_period":1209600}`)
//	mockProverConfig.EXPECT().ProtoMessage().Return(proverProtoMessage).AnyTimes()
//	mockProverConfig.EXPECT().Build(mockChain).Return(mockProver, nil).AnyTimes()
//	mockProverConfig.EXPECT().Validate().Return(nil).AnyTimes()
//
//	wantChain, _ := utils.MarshalJSONAny(mockCodec, mockChainConfig)
//	wantProver, _ := utils.MarshalJSONAny(mockCodec, mockProverConfig)
//
//	type args struct {
//		m      codec.JSONCodec
//		chain  ChainConfig
//		client ProverConfig
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    *ChainProverConfig
//		wantErr bool
//	}{
//		{
//			name: "should returns a new config instance.",
//			args: args{
//				m:      mockCodec,
//				chain:  mockChainConfig,
//				client: mockProverConfig,
//			},
//			want: &ChainProverConfig{
//				Chain:  wantChain,
//				Prover: wantProver,
//				chain:  mockChainConfig,
//				prover: mockProverConfig,
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := NewChainProverConfig(tt.args.m, tt.args.chain, tt.args.client)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("NewChainProverConfig() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("NewChainProverConfig() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

// TODO Core 単体だと MakeCodec の InterfaceRegistry に ethereum 及び provers パッケージが含まれないため型解決ができずエラーになる
//func TestChainProverConfig_Init(t *testing.T) {
//
//	// Generating a Controller that manages mock calls
//	ctrl := gomock.NewController(t)
//
//	// Generate a mock instance
//	mockChainConfig := NewMockChainConfig(ctrl)
//	mockProverConfig := NewMockProverConfig(ctrl)
//	mockCodec := MakeCodec()
//
//	chainProtoMessage := json.RawMessage(`{"@type":"/relayer.chains.ethereum.config.ChainConfig","chain_id":"********","eth_chain_id":5151,"rpc_addr":"http://**********:8451","signer":{"@type":"/relayer.chains.ethereum.signers.hd.SignerConfig","mnemonic":"mathrazorcapableexposeworthgrapemetalsunsetmetalsuddenusagescheme","path":"m/44'/60'/0'/0/0"},"ibc_address":"******************************************","initial_send_checkpoint":1,"initial_recv_checkpoint":1,"enable_debug_trace":false,"average_block_time_msec":2000,"max_retry_for_inclusion":3,"gas_estimate_rate":{"numerator":1,"denominator":1},"max_gas_limit":200000000,"tx_type":"auto"}`)
//	proverProtoMessage := json.RawMessage(`{"@type":"/relayer.provers.ibft2.config.ProverConfig","trust_level_numerator":1,"trust_level_denominator":3,"trusting_period":1209600}`)
//
//	type fields struct {
//		Chain  json.RawMessage
//		Prover json.RawMessage
//		chain  ChainConfig
//		prover ProverConfig
//	}
//	type args struct {
//		m codec.Codec
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//		{
//			name: "should initialises the configuration.",
//			fields: fields{
//				Chain:  chainProtoMessage,
//				Prover: proverProtoMessage,
//				chain:  mockChainConfig,
//				prover: mockProverConfig,
//			},
//			args: args{
//				m: mockCodec,
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			cc := &ChainProverConfig{
//				Chain:  tt.fields.Chain,
//				Prover: tt.fields.Prover,
//				chain:  tt.fields.chain,
//				prover: tt.fields.prover,
//			}
//			if err := cc.Init(tt.args.m); (err != nil) != tt.wantErr {
//				t.Errorf("ChainProverConfig.Init() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}

func TestChainProverConfig_GetChainConfig(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockChainConfig := NewMockChainConfig(ctrl)
	mockProverConfig := NewMockProverConfig(ctrl)

	chainProtoMessage := json.RawMessage(`{"@type":"/relayer.chains.ethereum.config.ChainConfig","chain_id":"********","eth_chain_id":5151,"rpc_addr":"http://**********:8451","signer":{"@type":"/relayer.chains.ethereum.signers.hd.SignerConfig","mnemonic":"mathrazorcapableexposeworthgrapemetalsunsetmetalsuddenusagescheme","path":"m/44'/60'/0'/0/0"},"ibc_address":"******************************************","initial_send_checkpoint":1,"initial_recv_checkpoint":1,"enable_debug_trace":false,"average_block_time_msec":2000,"max_retry_for_inclusion":3,"gas_estimate_rate":{"numerator":1,"denominator":1},"max_gas_limit":200000000,"tx_type":"auto"}`)
	proverProtoMessage := json.RawMessage(`{"@type":"/relayer.provers.ibft2.config.ProverConfig","trust_level_numerator":1,"trust_level_denominator":3,"trusting_period":1209600}`)

	type fields struct {
		Chain  json.RawMessage
		Prover json.RawMessage
		chain  ChainConfig
		prover ProverConfig
	}
	tests := []struct {
		name    string
		fields  fields
		want    ChainConfig
		wantErr bool
	}{
		{
			name: "should returns the cached ChainConfig instance.",
			fields: fields{
				Chain:  chainProtoMessage,
				Prover: proverProtoMessage,
				chain:  mockChainConfig,
				prover: mockProverConfig,
			},
			want:    mockChainConfig,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cc := ChainProverConfig{
				Chain:  tt.fields.Chain,
				Prover: tt.fields.Prover,
				chain:  tt.fields.chain,
				prover: tt.fields.prover,
			}
			got, err := cc.GetChainConfig()
			if (err != nil) != tt.wantErr {
				t.Errorf("ChainProverConfig.GetChainConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ChainProverConfig.GetChainConfig() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChainProverConfig_GetProverConfig(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockChainConfig := NewMockChainConfig(ctrl)
	mockProverConfig := NewMockProverConfig(ctrl)

	chainProtoMessage := json.RawMessage(`{"@type":"/relayer.chains.ethereum.config.ChainConfig","chain_id":"********","eth_chain_id":5151,"rpc_addr":"http://**********:8451","signer":{"@type":"/relayer.chains.ethereum.signers.hd.SignerConfig","mnemonic":"mathrazorcapableexposeworthgrapemetalsunsetmetalsuddenusagescheme","path":"m/44'/60'/0'/0/0"},"ibc_address":"******************************************","initial_send_checkpoint":1,"initial_recv_checkpoint":1,"enable_debug_trace":false,"average_block_time_msec":2000,"max_retry_for_inclusion":3,"gas_estimate_rate":{"numerator":1,"denominator":1},"max_gas_limit":200000000,"tx_type":"auto"}`)
	proverProtoMessage := json.RawMessage(`{"@type":"/relayer.provers.ibft2.config.ProverConfig","trust_level_numerator":1,"trust_level_denominator":3,"trusting_period":1209600}`)

	type fields struct {
		Chain  json.RawMessage
		Prover json.RawMessage
		chain  ChainConfig
		prover ProverConfig
	}
	tests := []struct {
		name    string
		fields  fields
		want    ProverConfig
		wantErr bool
	}{
		{
			name: "should returns the cached ProverConfig instance.",
			fields: fields{
				Chain:  chainProtoMessage,
				Prover: proverProtoMessage,
				chain:  mockChainConfig,
				prover: mockProverConfig,
			},
			want:    mockProverConfig,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cc := ChainProverConfig{
				Chain:  tt.fields.Chain,
				Prover: tt.fields.Prover,
				chain:  tt.fields.chain,
				prover: tt.fields.prover,
			}
			got, err := cc.GetProverConfig()
			if (err != nil) != tt.wantErr {
				t.Errorf("ChainProverConfig.GetProverConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ChainProverConfig.GetProverConfig() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TODO mock_config の Build が進まない
//func TestChainProverConfig_Build(t *testing.T) {
//
//	// Generating a Controller that manages mock calls
//	ctrl := gomock.NewController(t)
//
//	// Generate a mock instance
//	mockChain := NewMockChain(ctrl)
//	mockProver := NewMockProver(ctrl)
//	mockChainConfig := NewMockChainConfig(ctrl)
//	mockProverConfig := NewMockProverConfig(ctrl)
//
//	mockProvableChain := &ProvableChain{
//		Chain:  mockChain,
//		Prover: mockProver,
//	}
//
//	chainId := "chain-1"
//	mockChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//	mockChain.EXPECT().ChainID().Return(chainId).AnyTimes()
//	mockChain.EXPECT().SetRelayInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//
//	mockProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//	mockProver.EXPECT().SetRelayInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//
//	mockChainConfig.EXPECT().Build().Return(nil, nil).AnyTimes()
//	mockProverConfig.EXPECT().Build(mockChainConfig).Return(nil, nil).AnyTimes()
//
//	chainProtoMessage := json.RawMessage(`{"@type":"/relayer.chains.ethereum.config.ChainConfig","chain_id":"********","eth_chain_id":5151,"rpc_addr":"http://**********:8451","signer":{"@type":"/relayer.chains.ethereum.signers.hd.SignerConfig","mnemonic":"mathrazorcapableexposeworthgrapemetalsunsetmetalsuddenusagescheme","path":"m/44'/60'/0'/0/0"},"ibc_address":"******************************************","initial_send_checkpoint":1,"initial_recv_checkpoint":1,"enable_debug_trace":false,"average_block_time_msec":2000,"max_retry_for_inclusion":3,"gas_estimate_rate":{"numerator":1,"denominator":1},"max_gas_limit":200000000,"tx_type":"auto"}`)
//	proverProtoMessage := json.RawMessage(`{"@type":"/relayer.provers.ibft2.config.ProverConfig","trust_level_numerator":1,"trust_level_denominator":3,"trusting_period":1209600}`)
//
//	type fields struct {
//		Chain  json.RawMessage
//		Prover json.RawMessage
//		chain  ChainConfig
//		prover ProverConfig
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		want    *ProvableChain
//		wantErr bool
//	}{
//		{
//			name: "should build and returns a new ProvableChain instance.",
//			fields: fields{
//				Chain:  chainProtoMessage,
//				Prover: proverProtoMessage,
//				chain:  mockChainConfig,
//				prover: mockProverConfig,
//			},
//			want:    mockProvableChain,
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			cc := ChainProverConfig{
//				Chain:  tt.fields.Chain,
//				Prover: tt.fields.Prover,
//				chain:  tt.fields.chain,
//				prover: tt.fields.prover,
//			}
//			got, err := cc.Build()
//			if (err != nil) != tt.wantErr {
//				t.Errorf("ChainProverConfig.Build() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("ChainProverConfig.Build() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

// TODO chain.go の GetMsgResult が pkg/relay/ethereum/chain.go 内の GetMsgResult を見つけられる進まない
//func TestSyncChainConfigsFromEvents(t *testing.T) {
//
//	// Generating a Controller that manages mock calls
//	ctrl := gomock.NewController(t)
//
//	// Generate a mock instance
//	mockSrcChain := NewMockChain(ctrl)
//	mockSrcProver := NewMockProver(ctrl)
//	mockDstChain := NewMockChain(ctrl)
//	mockDstProver := NewMockProver(ctrl)
//	mockSrcMockMsgID := NewMockMsgID(ctrl)
//	mockDstMockMsgID := NewMockMsgID(ctrl)
//	mockMsgResult := NewMockMsgResult(ctrl)
//
//	// set expectations
//	msgIDsSrc := []MsgID{mockSrcMockMsgID}
//	msgIDsDst := []MsgID{mockDstMockMsgID}
//
//	mockSrcMockMsgID.EXPECT().String().Return("1111").AnyTimes()
//	mockDstMockMsgID.EXPECT().String().Return("2222").AnyTimes()
//
//	mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//	mockSrcChain.EXPECT().GetMsgResult(msgIDsSrc).Return(mockMsgResult, nil).AnyTimes()
//	mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//	mockDstChain.EXPECT().GetMsgResult(msgIDsDst).Return(mockMsgResult, nil).AnyTimes()
//
//	// TODO mockMsgResult.Events: EventGenerateClientIdentifier / EventGenerateConnectionIdentifier / EventGenerateChannelIdentifier
//
//	type args struct {
//		pathName  string
//		msgIDsSrc []MsgID
//		msgIDsDst []MsgID
//		src       *ProvableChain
//		dst       *ProvableChain
//	}
//	tests := []struct {
//		name    string
//		args    args
//		wantErr bool
//	}{
//
//		{
//			name: "should synchronize chain configuration from events.",
//			args: args{
//				pathName:  "account-sync",
//				msgIDsSrc: msgIDsSrc,
//				msgIDsDst: msgIDsDst,
//				src: &ProvableChain{
//					Chain:  mockSrcChain,
//					Prover: mockSrcProver,
//				},
//				dst: &ProvableChain{
//					Chain:  mockDstChain,
//					Prover: mockDstProver,
//				},
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			if err := SyncChainConfigsFromEvents(tt.args.pathName, tt.args.msgIDsSrc, tt.args.msgIDsDst, tt.args.src, tt.args.dst); (err != nil) != tt.wantErr {
//				t.Errorf("SyncChainConfigsFromEvents() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}

// TODO chain.go の GetMsgResult が pkg/relay/ethereum/chain.go 内の GetMsgResult を見つけられる進まない
//func TestSyncChainConfigFromEvents(t *testing.T) {
//
//	// Generating a Controller that manages mock calls
//	ctrl := gomock.NewController(t)
//
//	// Generate a mock instance
//	mockSrcChain := NewMockChain(ctrl)
//	mockSrcProver := NewMockProver(ctrl)
//	mockDstChain := NewMockChain(ctrl)
//	mockDstProver := NewMockProver(ctrl)
//	mockSrcMockMsgID := NewMockMsgID(ctrl)
//	mockDstMockMsgID := NewMockMsgID(ctrl)
//	mockMsgResult := NewMockMsgResult(ctrl)
//
//	// set expectations
//	msgIDsSrc := []MsgID{mockSrcMockMsgID}
//	msgIDsDst := []MsgID{mockDstMockMsgID}
//
//	mockSrcMockMsgID.EXPECT().String().Return("1111").AnyTimes()
//	mockDstMockMsgID.EXPECT().String().Return("2222").AnyTimes()
//
//	mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//	mockSrcChain.EXPECT().GetMsgResult(msgIDsSrc).Return(mockMsgResult, nil).AnyTimes()
//	mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//	mockDstChain.EXPECT().GetMsgResult(msgIDsDst).Return(mockMsgResult, nil).AnyTimes()
//
//	// TODO mockMsgResult.Events: EventGenerateClientIdentifier / EventGenerateConnectionIdentifier / EventGenerateChannelIdentifier
//
//	type args struct {
//		pathName string
//		msgIDs   []MsgID
//		chain    *ProvableChain
//	}
//	tests := []struct {
//		name    string
//		args    args
//		wantErr bool
//	}{
//		// TODO: Add test cases.
//		{
//			name: "should synchronize chain configuration from events.",
//			args: args{
//				pathName: "account-sync",
//				msgIDs:   msgIDsSrc,
//				chain: &ProvableChain{
//					Chain:  mockSrcChain,
//					Prover: mockSrcProver,
//				},
//			},
//			wantErr: false,
//		},
//		{
//			name: "should synchronize chain configuration from events.",
//			args: args{
//				pathName: "account-sync",
//				msgIDs:   msgIDsDst,
//				chain: &ProvableChain{
//					Chain:  mockDstChain,
//					Prover: mockDstProver,
//				},
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			if err := SyncChainConfigFromEvents(tt.args.pathName, tt.args.msgIDs, tt.args.chain); (err != nil) != tt.wantErr {
//				t.Errorf("SyncChainConfigFromEvents() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}

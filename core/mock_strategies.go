// Code generated by MockGen. DO NOT EDIT.
// Source: strategies.go
//
// Generated by this command:
//
//	mockgen -source=strategies.go -package=core -destination=./mock_strategies.go
//

// Package core is a generated GoMock package.
package core

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockStrategyI is a mock of StrategyI interface.
type MockStrategyI struct {
	ctrl     *gomock.Controller
	recorder *MockStrategyIMockRecorder
}

// MockStrategyIMockRecorder is the mock recorder for MockStrategyI.
type MockStrategyIMockRecorder struct {
	mock *MockStrategyI
}

// NewMockStrategyI creates a new mock instance.
func NewMockStrategyI(ctrl *gomock.Controller) *MockStrategyI {
	mock := &MockStrategyI{ctrl: ctrl}
	mock.recorder = &MockStrategyIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStrategyI) EXPECT() *MockStrategyIMockRecorder {
	return m.recorder
}

// GetType mocks base method.
func (m *MockStrategyI) GetType() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetType")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetType indicates an expected call of GetType.
func (mr *MockStrategyIMockRecorder) GetType() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetType", reflect.TypeOf((*MockStrategyI)(nil).GetType))
}

// RelayAcknowledgements mocks base method.
func (m *MockStrategyI) RelayAcknowledgements(src, dst *ProvableChain, rp *RelayPackets, sh SyncHeaders, doExecuteAckSrc, doExecuteAckDst bool) (*RelayMsgs, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RelayAcknowledgements", src, dst, rp, sh, doExecuteAckSrc, doExecuteAckDst)
	ret0, _ := ret[0].(*RelayMsgs)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RelayAcknowledgements indicates an expected call of RelayAcknowledgements.
func (mr *MockStrategyIMockRecorder) RelayAcknowledgements(src, dst, rp, sh, doExecuteAckSrc, doExecuteAckDst any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RelayAcknowledgements", reflect.TypeOf((*MockStrategyI)(nil).RelayAcknowledgements), src, dst, rp, sh, doExecuteAckSrc, doExecuteAckDst)
}

// RelayPackets mocks base method.
func (m *MockStrategyI) RelayPackets(src, dst *ProvableChain, rp *RelayPackets, sh SyncHeaders, doExecuteRelaySrc, doExecuteRelayDst bool) (*RelayMsgs, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RelayPackets", src, dst, rp, sh, doExecuteRelaySrc, doExecuteRelayDst)
	ret0, _ := ret[0].(*RelayMsgs)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RelayPackets indicates an expected call of RelayPackets.
func (mr *MockStrategyIMockRecorder) RelayPackets(src, dst, rp, sh, doExecuteRelaySrc, doExecuteRelayDst any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RelayPackets", reflect.TypeOf((*MockStrategyI)(nil).RelayPackets), src, dst, rp, sh, doExecuteRelaySrc, doExecuteRelayDst)
}

// Send mocks base method.
func (m *MockStrategyI) Send(src, dst Chain, msgs *RelayMsgs) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Send", src, dst, msgs)
}

// Send indicates an expected call of Send.
func (mr *MockStrategyIMockRecorder) Send(src, dst, msgs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Send", reflect.TypeOf((*MockStrategyI)(nil).Send), src, dst, msgs)
}

// SetupRelay mocks base method.
func (m *MockStrategyI) SetupRelay(ctx context.Context, src, dst *ProvableChain) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetupRelay", ctx, src, dst)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetupRelay indicates an expected call of SetupRelay.
func (mr *MockStrategyIMockRecorder) SetupRelay(ctx, src, dst any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetupRelay", reflect.TypeOf((*MockStrategyI)(nil).SetupRelay), ctx, src, dst)
}

// UnrelayedAcknowledgements mocks base method.
func (m *MockStrategyI) UnrelayedAcknowledgements(src, dst *ProvableChain, sh SyncHeaders, includeRelayedButUnfinalized bool) (*RelayPackets, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnrelayedAcknowledgements", src, dst, sh, includeRelayedButUnfinalized)
	ret0, _ := ret[0].(*RelayPackets)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnrelayedAcknowledgements indicates an expected call of UnrelayedAcknowledgements.
func (mr *MockStrategyIMockRecorder) UnrelayedAcknowledgements(src, dst, sh, includeRelayedButUnfinalized any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnrelayedAcknowledgements", reflect.TypeOf((*MockStrategyI)(nil).UnrelayedAcknowledgements), src, dst, sh, includeRelayedButUnfinalized)
}

// UnrelayedPackets mocks base method.
func (m *MockStrategyI) UnrelayedPackets(src, dst *ProvableChain, sh SyncHeaders, includeRelayedButUnfinalized bool) (*RelayPackets, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnrelayedPackets", src, dst, sh, includeRelayedButUnfinalized)
	ret0, _ := ret[0].(*RelayPackets)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnrelayedPackets indicates an expected call of UnrelayedPackets.
func (mr *MockStrategyIMockRecorder) UnrelayedPackets(src, dst, sh, includeRelayedButUnfinalized any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnrelayedPackets", reflect.TypeOf((*MockStrategyI)(nil).UnrelayedPackets), src, dst, sh, includeRelayedButUnfinalized)
}

// UpdateClients mocks base method.
func (m *MockStrategyI) UpdateClients(src, dst *ProvableChain, doExecuteRelaySrc, doExecuteRelayDst, doExecuteAckSrc, doExecuteAckDst bool, sh SyncHeaders, doRefresh bool) (*RelayMsgs, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateClients", src, dst, doExecuteRelaySrc, doExecuteRelayDst, doExecuteAckSrc, doExecuteAckDst, sh, doRefresh)
	ret0, _ := ret[0].(*RelayMsgs)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateClients indicates an expected call of UpdateClients.
func (mr *MockStrategyIMockRecorder) UpdateClients(src, dst, doExecuteRelaySrc, doExecuteRelayDst, doExecuteAckSrc, doExecuteAckDst, sh, doRefresh any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateClients", reflect.TypeOf((*MockStrategyI)(nil).UpdateClients), src, dst, doExecuteRelaySrc, doExecuteRelayDst, doExecuteAckSrc, doExecuteAckDst, sh, doRefresh)
}

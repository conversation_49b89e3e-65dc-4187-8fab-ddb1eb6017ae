//go:generate mockgen -source=$GOFILE -package=mock_core -destination=./mock_core/mock_$GOFILE -self_package=github.com/decurret-lab/dcbg-dcjpy-relayer/core
//go:generate gotests -w -all $GOFILE
package core

import (
	"reflect"
	"testing"

	sdk "github.com/cosmos/cosmos-sdk/types"
	"github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	chantypes "github.com/cosmos/ibc-go/v7/modules/core/04-channel/types"
	host "github.com/cosmos/ibc-go/v7/modules/core/24-host"
	"go.uber.org/mock/gomock"

	"github.com/google/go-cmp/cmp"
)

func TestNewRelayMsgs(t *testing.T) {

	tests := []struct {
		name string
		want *RelayMsgs
	}{
		{
			name: "returns an initialized version of relay messages.",
			want: &RelayMsgs{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewRelayMsgs(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewRelayMsgs() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRelayMsgs_Ready(t *testing.T) {

	channelID := "channel-0"
	portID := "account-sync"

	wantHeight := clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}

	packetInfoList := PacketInfoList{
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{1},
			EventHeight:     clienttypes.Height{},
		},
	}

	proof := []byte{1}

	type fields struct {
		Src          []sdk.Msg
		Dst          []sdk.Msg
		MaxTxSize    uint64
		MaxMsgLength uint64
		Last         bool
		Succeeded    bool
		SrcMsgIDs    []MsgID
		DstMsgIDs    []MsgID
	}
	tests := []struct {
		name        string
		prepareMock func()
		fields      fields
		want        bool
	}{
		{
			name:        "Returns false because RelayMsgs is nil.",
			prepareMock: func() {},
			fields: fields{
				Src:          nil,
				Dst:          nil,
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			want: false,
		},
		{
			name:        "Returns false since there are no messages to relay.",
			prepareMock: func() {},
			fields: fields{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			want: false,
		},
		{
			name:        "Returns true because there is a relay message in SRC.",
			prepareMock: func() {},
			fields: fields{
				Src: []sdk.Msg{
					chantypes.NewMsgRecvPacket(
						packetInfoList[0].Packet,
						proof,
						wantHeight,
						"cosmos1qyfkm2y3",
					),
				},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			want: true,
		},
		{
			name:        "Returns true because there is a relay message in Dst.",
			prepareMock: func() {},
			fields: fields{
				Src: []sdk.Msg{},
				Dst: []sdk.Msg{
					chantypes.NewMsgAcknowledgement(
						packetInfoList[0].Packet,
						packetInfoList[0].Acknowledgement,
						proof,
						wantHeight,
						"",
					),
				},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			want: true,
		},
		{
			name:        "Returns true since both SRC and DST have relay messages.",
			prepareMock: func() {},
			fields: fields{
				Src: []sdk.Msg{
					chantypes.NewMsgRecvPacket(
						packetInfoList[0].Packet,
						proof,
						wantHeight,
						"cosmos1qyfkm2y3",
					),
				},
				Dst: []sdk.Msg{
					chantypes.NewMsgAcknowledgement(
						packetInfoList[0].Packet,
						packetInfoList[0].Acknowledgement,
						proof,
						wantHeight,
						"",
					),
				},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			r := &RelayMsgs{
				Src:          tt.fields.Src,
				Dst:          tt.fields.Dst,
				MaxTxSize:    tt.fields.MaxTxSize,
				MaxMsgLength: tt.fields.MaxMsgLength,
				Last:         tt.fields.Last,
				Succeeded:    tt.fields.Succeeded,
				SrcMsgIDs:    tt.fields.SrcMsgIDs,
				DstMsgIDs:    tt.fields.DstMsgIDs,
			}
			if got := r.Ready(); got != tt.want {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("RelayMsgs.Ready() differs: (-got +want)n%s", diff)
				}
				t.Errorf("RelayMsgs.Ready() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRelayMsgs_Success(t *testing.T) {

	channelID := "channel-0"
	portID := "account-sync"

	wantHeight := clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}

	packetInfoList := PacketInfoList{
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{1},
			EventHeight:     clienttypes.Height{},
		},
	}

	proof := []byte{1}

	type fields struct {
		Src          []sdk.Msg
		Dst          []sdk.Msg
		MaxTxSize    uint64
		MaxMsgLength uint64
		Last         bool
		Succeeded    bool
		SrcMsgIDs    []MsgID
		DstMsgIDs    []MsgID
	}
	tests := []struct {
		name        string
		prepareMock func()
		fields      fields
		want        bool
	}{
		{
			name:        "Returns false because RelayMsgs is nil.",
			prepareMock: func() {},
			fields: fields{
				Src:          nil,
				Dst:          nil,
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			want: false,
		},
		{
			name:        "Returns false since there are no messages to relay.",
			prepareMock: func() {},
			fields: fields{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			want: false,
		},
		{
			name:        "Returns true because there is a relay message in SRC.",
			prepareMock: func() {},
			fields: fields{
				Src: []sdk.Msg{
					chantypes.NewMsgRecvPacket(
						packetInfoList[0].Packet,
						proof,
						wantHeight,
						"cosmos1qyfkm2y3",
					),
				},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    true,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			want: true,
		},
		{
			name:        "Returns true because there is a relay message in Dst.",
			prepareMock: func() {},
			fields: fields{
				Src: []sdk.Msg{},
				Dst: []sdk.Msg{
					chantypes.NewMsgAcknowledgement(
						packetInfoList[0].Packet,
						packetInfoList[0].Acknowledgement,
						proof,
						wantHeight,
						"",
					),
				},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    true,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			want: true,
		},
		{
			name:        "Returns true since both SRC and DST have relay messages.",
			prepareMock: func() {},
			fields: fields{
				Src: []sdk.Msg{
					chantypes.NewMsgRecvPacket(
						packetInfoList[0].Packet,
						proof,
						wantHeight,
						"cosmos1qyfkm2y3",
					),
				},
				Dst: []sdk.Msg{
					chantypes.NewMsgAcknowledgement(
						packetInfoList[0].Packet,
						packetInfoList[0].Acknowledgement,
						proof,
						wantHeight,
						"",
					),
				},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    true,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			r := &RelayMsgs{
				Src:          tt.fields.Src,
				Dst:          tt.fields.Dst,
				MaxTxSize:    tt.fields.MaxTxSize,
				MaxMsgLength: tt.fields.MaxMsgLength,
				Last:         tt.fields.Last,
				Succeeded:    tt.fields.Succeeded,
				SrcMsgIDs:    tt.fields.SrcMsgIDs,
				DstMsgIDs:    tt.fields.DstMsgIDs,
			}
			if got := r.Success(); got != tt.want {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("RelayMsgs.Success() differs: (-got +want)n%s", diff)
				}
				t.Errorf("RelayMsgs.Success() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRelayMsgs_IsMaxTx(t *testing.T) {

	type fields struct {
		Src          []sdk.Msg
		Dst          []sdk.Msg
		MaxTxSize    uint64
		MaxMsgLength uint64
		Last         bool
		Succeeded    bool
		SrcMsgIDs    []MsgID
		DstMsgIDs    []MsgID
	}
	type args struct {
		msgLen uint64
		txSize uint64
	}
	tests := []struct {
		name        string
		prepareMock func()
		fields      fields
		args        args
		want        bool
	}{
		{
			name:        "Returns false because RelayMsgs is nil.",
			prepareMock: func() {},
			fields: fields{
				Src:          nil,
				Dst:          nil,
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			args: args{
				msgLen: 0,
				txSize: 0,
			},
			want: false,
		},
		{
			name:        "Returns false r.MaxMsgLength != 0",
			prepareMock: func() {},
			fields: fields{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			args: args{
				msgLen: 2,
				txSize: 0,
			},
			want: false,
		},
		{
			name:        "Returns false !(msgLen > r.MaxMsgLength).",
			prepareMock: func() {},
			fields: fields{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 1,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			args: args{
				msgLen: 1,
				txSize: 0,
			},
			want: false,
		},
		{
			name:        "Returns false r.MaxTxSize != 0",
			prepareMock: func() {},
			fields: fields{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			args: args{
				msgLen: 0,
				txSize: 2,
			},
			want: false,
		},
		{
			name:        "Returns false !(txSize > r.MaxTxSize).",
			prepareMock: func() {},
			fields: fields{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    1,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			args: args{
				msgLen: 0,
				txSize: 1,
			},
			want: false,
		},
		{
			name:        "Returns true r.MaxMsgLength != 0 && msgLen > r.MaxMsgLength",
			prepareMock: func() {},
			fields: fields{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 1,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			args: args{
				msgLen: 2,
				txSize: 0,
			},
			want: true,
		},
		{
			name:        "Returns true txSize > r.MaxTxSize.",
			prepareMock: func() {},
			fields: fields{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    1,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			args: args{
				msgLen: 0,
				txSize: 2,
			},
			want: true,
		},
		{
			name:        "Returns true (r.MaxMsgLength != 0 && msgLen > r.MaxMsgLength) || (r.MaxTxSize != 0 && txSize > r.MaxTxSize).",
			prepareMock: func() {},
			fields: fields{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    1,
				MaxMsgLength: 1,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			args: args{
				msgLen: 2,
				txSize: 2,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			r := &RelayMsgs{
				Src:          tt.fields.Src,
				Dst:          tt.fields.Dst,
				MaxTxSize:    tt.fields.MaxTxSize,
				MaxMsgLength: tt.fields.MaxMsgLength,
				Last:         tt.fields.Last,
				Succeeded:    tt.fields.Succeeded,
				SrcMsgIDs:    tt.fields.SrcMsgIDs,
				DstMsgIDs:    tt.fields.DstMsgIDs,
			}
			if got := r.IsMaxTx(tt.args.msgLen, tt.args.txSize); got != tt.want {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("RelayMsgs.IsMaxTx() differs: (-got +want)n%s", diff)
				}
				t.Errorf("RelayMsgs.IsMaxTx() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TODO MsgID のテスト値が設定できないためテスト不可
//func TestRelayMsgs_Send(t *testing.T) {
//
//	// Generating a Controller that manages mock calls
//	ctrl := gomock.NewController(t)
//
//	// Generate a mock instance
//	mockSrcChain := NewMockChain(ctrl)
//	mockDstChain := NewMockChain(ctrl)
//	mockCodec := MakeCodec()
//	//mockSrcMockMsgID := NewMockMsgID(ctrl)
//	//mockDstMockMsgID := NewMockMsgID(ctrl)
//
//	//mockSrcProver := NewMockProver(ctrl)
//	//mockDstProver := NewMockProver(ctrl)
//	//mockSyncHeaders := NewMockSyncHeaders(ctrl)
//	//mockSrcQueryContext := NewMockQueryContext(ctrl)
//	//mockDstQueryContext := NewMockQueryContext(ctrl)
//
//	srcChainID := "********"
//	dstChainID := "********"
//	clientID := "hb-ibft2-0"
//	connectionID := "connection-0"
//	channelID := "channel-0"
//	portID := "account-sync"
//	order := "unordered"
//	version := "account-sync-0"
//
//	srcPath := &PathEnd{
//		ChainID:      srcChainID,
//		ClientID:     clientID,
//		ConnectionID: connectionID,
//		ChannelID:    channelID,
//		PortID:       portID,
//		Order:        order,
//		Version:      version,
//	}
//
//	dstPath := &PathEnd{
//		ChainID:      dstChainID,
//		ClientID:     clientID,
//		ConnectionID: connectionID,
//		ChannelID:    channelID,
//		PortID:       portID,
//		Order:        order,
//		Version:      version,
//	}
//
//	wantHeight := clienttypes.Height{
//		RevisionNumber: 0,
//		RevisionHeight: 100,
//	}
//
//	packetInfoList := PacketInfoList{
//		&PacketInfo{
//			Packet: chantypes.Packet{
//				Sequence:           1,
//				SourcePort:         portID,
//				SourceChannel:      channelID,
//				DestinationPort:    portID,
//				DestinationChannel: channelID,
//				Data:               []byte{1},
//				TimeoutHeight: types.Height{
//					RevisionNumber: 0,
//					RevisionHeight: 100,
//				},
//				TimeoutTimestamp: 0,
//			},
//			Acknowledgement: []byte{1},
//			EventHeight:     clienttypes.Height{},
//		},
//	}
//
//	proof := []byte{1}
//
//	mockSrcMsgIDs := &[]MsgID{}
//	mockDstMsgIDs := &[]MsgID{}
//
//	type fields struct {
//		Src          []sdk.Msg
//		Dst          []sdk.Msg
//		MaxTxSize    uint64
//		MaxMsgLength uint64
//		Last         bool
//		Succeeded    bool
//		SrcMsgIDs    []MsgID
//		DstMsgIDs    []MsgID
//	}
//	type args struct {
//		src Chain
//		dst Chain
//	}
//	tests := []struct {
//		name        string
//		prepareMock func()
//		fields      fields
//		args        args
//	}{
//		{
//			name: "sends the messages with appropriate output",
//			prepareMock: func() {
//
//				//mockSrcMockMsgID.EXPECT().ProtoMessage().Return().AnyTimes()
//				//mockSrcMockMsgID.EXPECT().Is_MsgID().Return().AnyTimes()
//				//mockSrcMockMsgID.EXPECT().Reset().Return().AnyTimes()
//				//mockDstMockMsgID.EXPECT().ProtoMessage().Return().AnyTimes()
//				//mockDstMockMsgID.EXPECT().Is_MsgID().Return().AnyTimes()
//				//mockDstMockMsgID.EXPECT().Reset().Return().AnyTimes()
//
//				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
//				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
//				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
//				mockSrcChain.EXPECT().SendMsgs(gomock.Any()).Return(mockSrcMsgIDs, nil).AnyTimes()
//
//				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
//				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
//				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
//				mockDstChain.EXPECT().SendMsgs(gomock.Any()).Return(mockDstMsgIDs, nil).AnyTimes()
//			},
//			fields: fields{
//				Src: []sdk.Msg{
//					chantypes.NewMsgRecvPacket(
//						packetInfoList[0].Packet,
//						proof,
//						wantHeight,
//						"cosmos1qyfkm2y3",
//					),
//				},
//				Dst:          []sdk.Msg{},
//				MaxTxSize:    0,
//				MaxMsgLength: 0,
//				Last:         false,
//				Succeeded:    false,
//				SrcMsgIDs:    []MsgID{},
//				DstMsgIDs:    []MsgID{},
//			},
//			args: args{
//				src: mockSrcChain,
//				dst: mockDstChain,
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			tt.prepareMock()
//			r := &RelayMsgs{
//				Src:          tt.fields.Src,
//				Dst:          tt.fields.Dst,
//				MaxTxSize:    tt.fields.MaxTxSize,
//				MaxMsgLength: tt.fields.MaxMsgLength,
//				Last:         tt.fields.Last,
//				Succeeded:    tt.fields.Succeeded,
//				SrcMsgIDs:    tt.fields.SrcMsgIDs,
//				DstMsgIDs:    tt.fields.DstMsgIDs,
//			}
//			r.Send(tt.args.src, tt.args.dst)
//		})
//	}
//}

func TestRelayMsgs_Merge(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockDstChain := NewMockChain(ctrl)
	mockSrcProver := NewMockProver(ctrl)
	mockDstProver := NewMockProver(ctrl)
	mockSyncHeaders := NewMockSyncHeaders(ctrl)
	mockSrcQueryContext := NewMockQueryContext(ctrl)
	mockDstQueryContext := NewMockQueryContext(ctrl)
	mockCodec := MakeCodec()

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	wantHeight := clienttypes.Height{
		RevisionNumber: 0,
		RevisionHeight: 100,
	}

	packetInfoList := PacketInfoList{
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{},
			EventHeight:     clienttypes.Height{},
		},
	}

	packetCommitmentPath := host.PacketCommitmentPath(
		packetInfoList[0].SourcePort,
		packetInfoList[0].SourceChannel,
		packetInfoList[0].Sequence,
	)

	commitment := chantypes.CommitPacket(
		mockCodec,
		packetInfoList[0],
	)

	proof := []byte{1}

	type fields struct {
		Src          []sdk.Msg
		Dst          []sdk.Msg
		MaxTxSize    uint64
		MaxMsgLength uint64
		Last         bool
		Succeeded    bool
		SrcMsgIDs    []MsgID
		DstMsgIDs    []MsgID
	}
	type args struct {
		other *RelayMsgs
	}
	tests := []struct {
		name        string
		prepareMock func()
		fields      fields
		args        args
	}{
		{
			name: "Src merges the argument into the receiver",
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, commitment).Return(proof, wantHeight, nil).AnyTimes()

				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, commitment).Return(proof, wantHeight, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			fields: fields{
				Src:          []sdk.Msg{},
				Dst:          nil,
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			args: args{
				other: &RelayMsgs{
					Src: []sdk.Msg{
						chantypes.NewMsgRecvPacket(
							packetInfoList[0].Packet,
							proof,
							wantHeight,
							"cosmos1qyfkm2y3",
						),
					},
					Dst:          nil,
					MaxTxSize:    0,
					MaxMsgLength: 0,
					Last:         false,
					Succeeded:    false,
					SrcMsgIDs:    nil,
					DstMsgIDs:    nil,
				},
			},
		},
		{
			name: "Dst merges the argument into the receiver",
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, commitment).Return(proof, wantHeight, nil).AnyTimes()

				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, commitment).Return(proof, wantHeight, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			fields: fields{
				Src:          nil,
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			args: args{
				other: &RelayMsgs{
					Src: nil,
					Dst: []sdk.Msg{
						chantypes.NewMsgAcknowledgement(
							packetInfoList[0].Packet,
							packetInfoList[0].Acknowledgement,
							proof,
							wantHeight,
							"",
						),
					},
					MaxTxSize:    0,
					MaxMsgLength: 0,
					Last:         false,
					Succeeded:    false,
					SrcMsgIDs:    nil,
					DstMsgIDs:    nil,
				},
			},
		},
		{
			name: "Both merges the argument into the receiver",
			prepareMock: func() {
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
				mockSrcChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockSrcChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockSrcChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(packetInfoList, nil).AnyTimes()
				mockSrcChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().Codec().Return(mockCodec).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
				mockDstChain.EXPECT().LatestHeight().Return(wantHeight, nil).AnyTimes()
				mockDstChain.EXPECT().GetAddress().Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnfinalizedRelayPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
				mockDstChain.EXPECT().QueryUnreceivedPackets(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, commitment).Return(proof, wantHeight, nil).AnyTimes()

				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().SetupForRelay(gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().ProveState(mockSrcQueryContext, packetCommitmentPath, commitment).Return(proof, wantHeight, nil).AnyTimes()

				mockSrcQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockSrcQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()
				mockDstQueryContext.EXPECT().Context().Return(nil).AnyTimes()
				mockDstQueryContext.EXPECT().Height().Return(wantHeight).AnyTimes()

				mockSyncHeaders.EXPECT().GetQueryContext(srcPath.ChainID).Return(mockSrcQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(srcPath.ChainID).Return(nil).AnyTimes()
				mockSyncHeaders.EXPECT().GetQueryContext(dstPath.ChainID).Return(mockDstQueryContext).AnyTimes()
				mockSyncHeaders.EXPECT().GetLatestFinalizedHeader(dstPath.ChainID).Return(nil).AnyTimes()
			},
			fields: fields{
				Src:          []sdk.Msg{},
				Dst:          []sdk.Msg{},
				MaxTxSize:    0,
				MaxMsgLength: 0,
				Last:         false,
				Succeeded:    false,
				SrcMsgIDs:    nil,
				DstMsgIDs:    nil,
			},
			args: args{
				other: &RelayMsgs{
					Src: []sdk.Msg{
						chantypes.NewMsgRecvPacket(
							packetInfoList[0].Packet,
							proof,
							wantHeight,
							"cosmos1qyfkm2y3",
						),
					},
					Dst: []sdk.Msg{
						chantypes.NewMsgAcknowledgement(
							packetInfoList[0].Packet,
							packetInfoList[0].Acknowledgement,
							proof,
							wantHeight,
							"",
						),
					}, MaxTxSize: 0,
					MaxMsgLength: 0,
					Last:         false,
					Succeeded:    false,
					SrcMsgIDs:    nil,
					DstMsgIDs:    nil,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			r := &RelayMsgs{
				Src:          tt.fields.Src,
				Dst:          tt.fields.Dst,
				MaxTxSize:    tt.fields.MaxTxSize,
				MaxMsgLength: tt.fields.MaxMsgLength,
				Last:         tt.fields.Last,
				Succeeded:    tt.fields.Succeeded,
				SrcMsgIDs:    tt.fields.SrcMsgIDs,
				DstMsgIDs:    tt.fields.DstMsgIDs,
			}
			r.Merge(tt.args.other)
		})
	}
}

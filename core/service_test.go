//go:generate mockgen -source=$GOFILE -package=mock_core -destination=./mock_core/mock_$GOFILE -self_package=github.com/decurret-lab/dcbg-dcjpy-relayer/core
//go:generate gotests -w -all $GOFILE
package core

import (
	"context"
	"reflect"
	"testing"
	"time"

	sdk "github.com/cosmos/cosmos-sdk/types"
	"github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	clienttypes "github.com/cosmos/ibc-go/v7/modules/core/02-client/types"
	chantypes "github.com/cosmos/ibc-go/v7/modules/core/04-channel/types"
	"github.com/google/go-cmp/cmp"
	"go.uber.org/mock/gomock"
)

// TODO UTは実施できるが retry.Do によりUTが終了できない
//func TestStartService(t *testing.T) {
//
//	// Generating a Controller that manages mock calls
//	ctrl := gomock.NewController(t)
//
//	// Generate a mock instance
//	mockSrcChain := NewMockChain(ctrl)
//	mockDstChain := NewMockChain(ctrl)
//	mockSrcProver := NewMockProver(ctrl)
//	mockDstProver := NewMockProver(ctrl)
//	mockHeaders := NewMockHeader(ctrl)
//	mockStrategyI := NewMockStrategyI(ctrl)
//
//	srcChainID := "********"
//	dstChainID := "********"
//	clientID := "hb-ibft2-0"
//	connectionID := "connection-0"
//	channelID := "channel-0"
//	portID := "account-sync"
//	order := "unordered"
//	version := "account-sync-0"
//
//	srcPath := &PathEnd{
//		ChainID:      srcChainID,
//		ClientID:     clientID,
//		ConnectionID: connectionID,
//		ChannelID:    channelID,
//		PortID:       portID,
//		Order:        order,
//		Version:      version,
//	}
//	dstPath := &PathEnd{
//		ChainID:      dstChainID,
//		ClientID:     clientID,
//		ConnectionID: connectionID,
//		ChannelID:    channelID,
//		PortID:       portID,
//		Order:        order,
//		Version:      version,
//	}
//
//	mockPacketInfoList := PacketInfoList{
//		&PacketInfo{
//			Packet: chantypes.Packet{
//				Sequence:           1,
//				SourcePort:         portID,
//				SourceChannel:      channelID,
//				DestinationPort:    portID,
//				DestinationChannel: channelID,
//				Data:               []byte{1},
//				TimeoutHeight: types.Height{
//					RevisionNumber: 0,
//					RevisionHeight: 100,
//				},
//				TimeoutTimestamp: 0,
//			},
//			Acknowledgement: []byte{1},
//			EventHeight:     clienttypes.Height{},
//		},
//		&PacketInfo{
//			Packet: chantypes.Packet{
//				Sequence:           2,
//				SourcePort:         portID,
//				SourceChannel:      channelID,
//				DestinationPort:    portID,
//				DestinationChannel: channelID,
//				Data:               []byte{2},
//				TimeoutHeight: types.Height{
//					RevisionNumber: 0,
//					RevisionHeight: 100,
//				},
//				TimeoutTimestamp: 0,
//			},
//			Acknowledgement: []byte{2},
//			EventHeight:     clienttypes.Height{},
//		},
//		&PacketInfo{
//			Packet: chantypes.Packet{
//				Sequence:           3,
//				SourcePort:         portID,
//				SourceChannel:      channelID,
//				DestinationPort:    portID,
//				DestinationChannel: channelID,
//				Data:               []byte{3},
//				TimeoutHeight: types.Height{
//					RevisionNumber: 0,
//					RevisionHeight: 100,
//				},
//				TimeoutTimestamp: 0,
//			},
//			Acknowledgement: []byte{3},
//			EventHeight:     clienttypes.Height{},
//		},
//	}
//
//	mockSrcRelayPackets := &RelayPackets{
//		Src: mockPacketInfoList,
//		Dst: PacketInfoList{},
//	}
//
//	type args struct {
//		ctx           context.Context
//		st            StrategyI
//		src           *ProvableChain
//		dst           *ProvableChain
//		relayInterval time.Duration
//	}
//	tests := []struct {
//		name        string
//		prepareMock func()
//		args        args
//		wantErr     bool
//	}{
//		{
//			name: "should starts a relay service.",
//			prepareMock: func() {
//				mockRelayMsgs := &RelayMsgs{
//					Src:          []sdk.Msg{},
//					Dst:          []sdk.Msg{},
//					MaxTxSize:    0,
//					MaxMsgLength: 0,
//					Last:         false,
//					Succeeded:    false,
//					SrcMsgIDs:    nil,
//					DstMsgIDs:    nil,
//				}
//				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
//				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
//
//				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
//				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
//
//				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//				mockSrcProver.EXPECT().GetLatestFinalizedHeader().Return(mockHeaders, nil).AnyTimes()
//				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//				mockDstProver.EXPECT().GetLatestFinalizedHeader().Return(mockHeaders, nil).AnyTimes()
//
//				mockStrategyI.EXPECT().UnrelayedPackets(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockSrcRelayPackets, nil).AnyTimes()
//				mockStrategyI.EXPECT().UnrelayedAcknowledgements(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockSrcRelayPackets, nil).AnyTimes()
//				mockStrategyI.EXPECT().UpdateClients(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
//				mockStrategyI.EXPECT().RelayPackets(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
//				mockStrategyI.EXPECT().RelayAcknowledgements(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
//				mockStrategyI.EXPECT().Send(gomock.Any(), gomock.Any(), gomock.Any()).Return().AnyTimes()
//
//			},
//			args: args{
//				ctx: context.TODO(),
//				st:  mockStrategyI,
//				src: &ProvableChain{
//					Chain:  mockSrcChain,
//					Prover: mockSrcProver,
//				},
//				dst: &ProvableChain{
//					Chain:  mockDstChain,
//					Prover: mockDstProver,
//				},
//				relayInterval: 2,
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			tt.prepareMock()
//			if err := StartService(tt.args.ctx, tt.args.st, tt.args.src, tt.args.dst, tt.args.relayInterval); (err != nil) != tt.wantErr {
//				t.Errorf("StartService() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//		t.Deadline()
//	}
//}

func TestNewRelayService(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockDstChain := NewMockChain(ctrl)
	mockSrcProver := NewMockProver(ctrl)
	mockDstProver := NewMockProver(ctrl)
	mockHeaders := NewMockHeader(ctrl)
	mockSyncHeaders := NewMockSyncHeaders(ctrl)
	mockStrategyI := NewMockStrategyI(ctrl)

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	mockPacketInfoList := PacketInfoList{
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{1},
			EventHeight:     clienttypes.Height{},
		},
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           2,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{2},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{2},
			EventHeight:     clienttypes.Height{},
		},
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           3,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{3},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{3},
			EventHeight:     clienttypes.Height{},
		},
	}

	mockSrcRelayPackets := &RelayPackets{
		Src: mockPacketInfoList,
		Dst: PacketInfoList{},
	}

	//mockDstRelayPackets := &RelayPackets{
	//	Src: PacketInfoList{},
	//	Dst: mockPacketInfoList,
	//}

	type args struct {
		st       StrategyI
		src      *ProvableChain
		dst      *ProvableChain
		sh       SyncHeaders
		interval time.Duration
	}
	tests := []struct {
		name        string
		prepareMock func()
		args        args
		want        *RelayService
	}{
		{
			name: "should returns a new service.",
			prepareMock: func() {
				mockRelayMsgs := &RelayMsgs{
					Src:          []sdk.Msg{},
					Dst:          []sdk.Msg{},
					MaxTxSize:    0,
					MaxMsgLength: 0,
					Last:         false,
					Succeeded:    false,
					SrcMsgIDs:    nil,
					DstMsgIDs:    nil,
				}
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().GetLatestFinalizedHeader().Return(mockHeaders, nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().GetLatestFinalizedHeader().Return(mockHeaders, nil).AnyTimes()

				mockStrategyI.EXPECT().UnrelayedPackets(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockSrcRelayPackets, nil).AnyTimes()
				mockStrategyI.EXPECT().UnrelayedAcknowledgements(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockSrcRelayPackets, nil).AnyTimes()
				mockStrategyI.EXPECT().UpdateClients(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
				mockStrategyI.EXPECT().RelayPackets(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
				mockStrategyI.EXPECT().RelayAcknowledgements(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
				mockStrategyI.EXPECT().Send(gomock.Any(), gomock.Any(), gomock.Any()).Return().AnyTimes()
			},
			args: args{
				st: mockStrategyI,
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				sh:       mockSyncHeaders,
				interval: 0,
			},
			want: &RelayService{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				st:       mockStrategyI,
				sh:       mockSyncHeaders,
				interval: 0,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			if got := NewRelayService(tt.args.st, tt.args.src, tt.args.dst, tt.args.sh, tt.args.interval); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewRelayService() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TODO UTは実施できるが retry.Do によりUTが終了できない
//func TestRelayService_Start(t *testing.T) {
//
//	// Generating a Controller that manages mock calls
//	ctrl := gomock.NewController(t)
//
//	// Generate a mock instance
//	mockSrcChain := NewMockChain(ctrl)
//	mockDstChain := NewMockChain(ctrl)
//	mockSrcProver := NewMockProver(ctrl)
//	mockDstProver := NewMockProver(ctrl)
//	mockHeaders := NewMockHeader(ctrl)
//	mockSyncHeaders := NewMockSyncHeaders(ctrl)
//	mockStrategyI := NewMockStrategyI(ctrl)
//
//	srcChainID := "********"
//	dstChainID := "********"
//	clientID := "hb-ibft2-0"
//	connectionID := "connection-0"
//	channelID := "channel-0"
//	portID := "account-sync"
//	order := "unordered"
//	version := "account-sync-0"
//
//	srcPath := &PathEnd{
//		ChainID:      srcChainID,
//		ClientID:     clientID,
//		ConnectionID: connectionID,
//		ChannelID:    channelID,
//		PortID:       portID,
//		Order:        order,
//		Version:      version,
//	}
//	dstPath := &PathEnd{
//		ChainID:      dstChainID,
//		ClientID:     clientID,
//		ConnectionID: connectionID,
//		ChannelID:    channelID,
//		PortID:       portID,
//		Order:        order,
//		Version:      version,
//	}
//
//	mockPacketInfoList := PacketInfoList{
//		&PacketInfo{
//			Packet: chantypes.Packet{
//				Sequence:           1,
//				SourcePort:         portID,
//				SourceChannel:      channelID,
//				DestinationPort:    portID,
//				DestinationChannel: channelID,
//				Data:               []byte{1},
//				TimeoutHeight: types.Height{
//					RevisionNumber: 0,
//					RevisionHeight: 100,
//				},
//				TimeoutTimestamp: 0,
//			},
//			Acknowledgement: []byte{1},
//			EventHeight:     clienttypes.Height{},
//		},
//		&PacketInfo{
//			Packet: chantypes.Packet{
//				Sequence:           2,
//				SourcePort:         portID,
//				SourceChannel:      channelID,
//				DestinationPort:    portID,
//				DestinationChannel: channelID,
//				Data:               []byte{2},
//				TimeoutHeight: types.Height{
//					RevisionNumber: 0,
//					RevisionHeight: 100,
//				},
//				TimeoutTimestamp: 0,
//			},
//			Acknowledgement: []byte{2},
//			EventHeight:     clienttypes.Height{},
//		},
//		&PacketInfo{
//			Packet: chantypes.Packet{
//				Sequence:           3,
//				SourcePort:         portID,
//				SourceChannel:      channelID,
//				DestinationPort:    portID,
//				DestinationChannel: channelID,
//				Data:               []byte{3},
//				TimeoutHeight: types.Height{
//					RevisionNumber: 0,
//					RevisionHeight: 100,
//				},
//				TimeoutTimestamp: 0,
//			},
//			Acknowledgement: []byte{3},
//			EventHeight:     clienttypes.Height{},
//		},
//	}
//
//	mockSrcRelayPackets := &RelayPackets{
//		Src: mockPacketInfoList,
//		Dst: PacketInfoList{},
//	}
//
//	type fields struct {
//		src      *ProvableChain
//		dst      *ProvableChain
//		st       StrategyI
//		sh       SyncHeaders
//		interval time.Duration
//	}
//	type args struct {
//		ctx context.Context
//	}
//	tests := []struct {
//		name        string
//		prepareMock func()
//		fields      fields
//		args        args
//		wantErr     bool
//	}{
//		{
//			name: "should starts a relay service.",
//			prepareMock: func() {
//				mockRelayMsgs := &RelayMsgs{
//					Src:          []sdk.Msg{},
//					Dst:          []sdk.Msg{},
//					MaxTxSize:    0,
//					MaxMsgLength: 0,
//					Last:         false,
//					Succeeded:    false,
//					SrcMsgIDs:    nil,
//					DstMsgIDs:    nil,
//				}
//				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
//				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()
//
//				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
//				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()
//
//				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//				mockSrcProver.EXPECT().GetLatestFinalizedHeader().Return(mockHeaders, nil).AnyTimes()
//				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//				mockDstProver.EXPECT().GetLatestFinalizedHeader().Return(mockHeaders, nil).AnyTimes()
//
//				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//
//				mockStrategyI.EXPECT().UnrelayedPackets(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockSrcRelayPackets, nil).AnyTimes()
//				mockStrategyI.EXPECT().UnrelayedAcknowledgements(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockSrcRelayPackets, nil).AnyTimes()
//				mockStrategyI.EXPECT().UpdateClients(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
//				mockStrategyI.EXPECT().RelayPackets(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
//				mockStrategyI.EXPECT().RelayAcknowledgements(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
//				mockStrategyI.EXPECT().Send(gomock.Any(), gomock.Any(), gomock.Any()).Return().AnyTimes()
//
//			},
//			fields: fields{
//				src: &ProvableChain{
//					Chain:  mockSrcChain,
//					Prover: mockSrcProver,
//				},
//				dst: &ProvableChain{
//					Chain:  mockDstChain,
//					Prover: mockDstProver,
//				},
//				st:       mockStrategyI,
//				sh:       mockSyncHeaders,
//				interval: 2,
//			},
//			args: args{
//				ctx: context.TODO(),
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			tt.prepareMock()
//			srv := &RelayService{
//				src:      tt.fields.src,
//				dst:      tt.fields.dst,
//				st:       tt.fields.st,
//				sh:       tt.fields.sh,
//				interval: tt.fields.interval,
//			}
//			if err := srv.Start(tt.args.ctx); (err != nil) != tt.wantErr {
//				t.Errorf("RelayService.Start() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}

func TestRelayService_Serve(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockDstChain := NewMockChain(ctrl)
	mockSrcProver := NewMockProver(ctrl)
	mockDstProver := NewMockProver(ctrl)
	mockHeaders := NewMockHeader(ctrl)
	mockSyncHeaders := NewMockSyncHeaders(ctrl)
	mockStrategyI := NewMockStrategyI(ctrl)

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	mockPacketInfoList := PacketInfoList{
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{1},
			EventHeight:     clienttypes.Height{},
		},
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           2,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{2},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{2},
			EventHeight:     clienttypes.Height{},
		},
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           3,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{3},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{3},
			EventHeight:     clienttypes.Height{},
		},
	}

	mockSrcRelayPackets := &RelayPackets{
		Src: mockPacketInfoList,
		Dst: PacketInfoList{},
	}

	type fields struct {
		src      *ProvableChain
		dst      *ProvableChain
		st       StrategyI
		sh       SyncHeaders
		interval time.Duration
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name        string
		prepareMock func()
		fields      fields
		args        args
		wantErr     bool
	}{
		{
			name: "should performs packet-relay.",
			prepareMock: func() {
				mockRelayMsgs := &RelayMsgs{
					Src:          []sdk.Msg{},
					Dst:          []sdk.Msg{},
					MaxTxSize:    0,
					MaxMsgLength: 0,
					Last:         false,
					Succeeded:    false,
					SrcMsgIDs:    nil,
					DstMsgIDs:    nil,
				}
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().GetLatestFinalizedHeader().Return(mockHeaders, nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().GetLatestFinalizedHeader().Return(mockHeaders, nil).AnyTimes()

				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				mockStrategyI.EXPECT().UnrelayedPackets(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockSrcRelayPackets, nil).AnyTimes()
				mockStrategyI.EXPECT().UnrelayedAcknowledgements(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockSrcRelayPackets, nil).AnyTimes()
				mockStrategyI.EXPECT().UpdateClients(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
				mockStrategyI.EXPECT().RelayPackets(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
				mockStrategyI.EXPECT().RelayAcknowledgements(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
				mockStrategyI.EXPECT().Send(gomock.Any(), gomock.Any(), gomock.Any()).Return().AnyTimes()
			},
			fields: fields{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				st:       mockStrategyI,
				sh:       mockSyncHeaders,
				interval: 0,
			},
			args: args{
				ctx: context.TODO(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			srv := &RelayService{
				src:      tt.fields.src,
				dst:      tt.fields.dst,
				st:       tt.fields.st,
				sh:       tt.fields.sh,
				interval: tt.fields.interval,
			}
			if err := srv.Serve(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("RelayService.Serve() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRelayService_shouldExecuteRelay(t *testing.T) {

	// Generating a Controller that manages mock calls
	ctrl := gomock.NewController(t)

	// Generate a mock instance
	mockSrcChain := NewMockChain(ctrl)
	mockDstChain := NewMockChain(ctrl)
	mockSrcProver := NewMockProver(ctrl)
	mockDstProver := NewMockProver(ctrl)
	mockHeaders := NewMockHeader(ctrl)
	mockSyncHeaders := NewMockSyncHeaders(ctrl)
	mockStrategyI := NewMockStrategyI(ctrl)

	srcChainID := "********"
	dstChainID := "********"
	clientID := "hb-ibft2-0"
	connectionID := "connection-0"
	channelID := "channel-0"
	portID := "account-sync"
	order := "unordered"
	version := "account-sync-0"

	srcPath := &PathEnd{
		ChainID:      srcChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}
	dstPath := &PathEnd{
		ChainID:      dstChainID,
		ClientID:     clientID,
		ConnectionID: connectionID,
		ChannelID:    channelID,
		PortID:       portID,
		Order:        order,
		Version:      version,
	}

	mockPacketInfoList := PacketInfoList{
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           1,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{1},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{1},
			EventHeight:     clienttypes.Height{},
		},
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           2,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{2},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{2},
			EventHeight:     clienttypes.Height{},
		},
		&PacketInfo{
			Packet: chantypes.Packet{
				Sequence:           3,
				SourcePort:         portID,
				SourceChannel:      channelID,
				DestinationPort:    portID,
				DestinationChannel: channelID,
				Data:               []byte{3},
				TimeoutHeight: types.Height{
					RevisionNumber: 0,
					RevisionHeight: 100,
				},
				TimeoutTimestamp: 0,
			},
			Acknowledgement: []byte{3},
			EventHeight:     clienttypes.Height{},
		},
	}

	mockSrcRelayPackets := &RelayPackets{
		Src: mockPacketInfoList,
		Dst: PacketInfoList{},
	}

	type fields struct {
		src      *ProvableChain
		dst      *ProvableChain
		st       StrategyI
		sh       SyncHeaders
		interval time.Duration
	}
	type args struct {
		seqs *RelayPackets
	}
	tests := []struct {
		name        string
		prepareMock func()
		fields      fields
		args        args
		want        bool
		want1       bool
	}{
		{
			name: "Return true even if RelayPackets is nil.",
			prepareMock: func() {
				mockRelayMsgs := &RelayMsgs{
					Src:          []sdk.Msg{},
					Dst:          []sdk.Msg{},
					MaxTxSize:    0,
					MaxMsgLength: 0,
					Last:         false,
					Succeeded:    false,
					SrcMsgIDs:    nil,
					DstMsgIDs:    nil,
				}
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().GetLatestFinalizedHeader().Return(mockHeaders, nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().GetLatestFinalizedHeader().Return(mockHeaders, nil).AnyTimes()

				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				mockStrategyI.EXPECT().UnrelayedPackets(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockSrcRelayPackets, nil).AnyTimes()
				mockStrategyI.EXPECT().UnrelayedAcknowledgements(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockSrcRelayPackets, nil).AnyTimes()
				mockStrategyI.EXPECT().UpdateClients(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
				mockStrategyI.EXPECT().RelayPackets(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
				mockStrategyI.EXPECT().RelayAcknowledgements(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
				mockStrategyI.EXPECT().Send(gomock.Any(), gomock.Any(), gomock.Any()).Return().AnyTimes()

			},
			fields: fields{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				st:       mockStrategyI,
				sh:       mockSyncHeaders,
				interval: 0,
			},
			args: args{
				seqs: &RelayPackets{
					Src: nil,
					Dst: nil,
				},
			},
			want:  true,
			want1: true,
		},
		{
			name: "Returns true if there is a target in SRC of RelayPackets.",
			prepareMock: func() {
				mockRelayMsgs := &RelayMsgs{
					Src:          []sdk.Msg{},
					Dst:          []sdk.Msg{},
					MaxTxSize:    0,
					MaxMsgLength: 0,
					Last:         false,
					Succeeded:    false,
					SrcMsgIDs:    nil,
					DstMsgIDs:    nil,
				}
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().GetLatestFinalizedHeader().Return(mockHeaders, nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().GetLatestFinalizedHeader().Return(mockHeaders, nil).AnyTimes()

				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				mockStrategyI.EXPECT().UnrelayedPackets(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockSrcRelayPackets, nil).AnyTimes()
				mockStrategyI.EXPECT().UnrelayedAcknowledgements(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockSrcRelayPackets, nil).AnyTimes()
				mockStrategyI.EXPECT().UpdateClients(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
				mockStrategyI.EXPECT().RelayPackets(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
				mockStrategyI.EXPECT().RelayAcknowledgements(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
				mockStrategyI.EXPECT().Send(gomock.Any(), gomock.Any(), gomock.Any()).Return().AnyTimes()

			},
			fields: fields{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				st:       mockStrategyI,
				sh:       mockSyncHeaders,
				interval: 0,
			},
			args: args{
				seqs: &RelayPackets{
					Src: mockPacketInfoList,
					Dst: nil,
				},
			},
			want:  true,
			want1: true,
		},
		{
			name: "Returns true if there is a target in DST of RelayPackets.",
			prepareMock: func() {
				mockRelayMsgs := &RelayMsgs{
					Src:          []sdk.Msg{},
					Dst:          []sdk.Msg{},
					MaxTxSize:    0,
					MaxMsgLength: 0,
					Last:         false,
					Succeeded:    false,
					SrcMsgIDs:    nil,
					DstMsgIDs:    nil,
				}
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().GetLatestFinalizedHeader().Return(mockHeaders, nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().GetLatestFinalizedHeader().Return(mockHeaders, nil).AnyTimes()

				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				mockStrategyI.EXPECT().UnrelayedPackets(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockSrcRelayPackets, nil).AnyTimes()
				mockStrategyI.EXPECT().UnrelayedAcknowledgements(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockSrcRelayPackets, nil).AnyTimes()
				mockStrategyI.EXPECT().UpdateClients(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
				mockStrategyI.EXPECT().RelayPackets(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
				mockStrategyI.EXPECT().RelayAcknowledgements(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
				mockStrategyI.EXPECT().Send(gomock.Any(), gomock.Any(), gomock.Any()).Return().AnyTimes()

			},
			fields: fields{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				st:       mockStrategyI,
				sh:       mockSyncHeaders,
				interval: 0,
			},
			args: args{
				seqs: &RelayPackets{
					Src: nil,
					Dst: mockPacketInfoList,
				},
			},
			want:  true,
			want1: true,
		},
		{
			name: "Returns true if there is a target in SRC and DST of RelayPackets.",
			prepareMock: func() {
				mockRelayMsgs := &RelayMsgs{
					Src:          []sdk.Msg{},
					Dst:          []sdk.Msg{},
					MaxTxSize:    0,
					MaxMsgLength: 0,
					Last:         false,
					Succeeded:    false,
					SrcMsgIDs:    nil,
					DstMsgIDs:    nil,
				}
				mockSrcChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcChain.EXPECT().ChainID().Return(srcChainID).AnyTimes()
				mockSrcChain.EXPECT().Path().Return(srcPath).AnyTimes()

				mockDstChain.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstChain.EXPECT().ChainID().Return(dstChainID).AnyTimes()
				mockDstChain.EXPECT().Path().Return(dstPath).AnyTimes()

				mockSrcProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockSrcProver.EXPECT().GetLatestFinalizedHeader().Return(mockHeaders, nil).AnyTimes()
				mockDstProver.EXPECT().Init(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockDstProver.EXPECT().GetLatestFinalizedHeader().Return(mockHeaders, nil).AnyTimes()

				mockSyncHeaders.EXPECT().Updates(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				mockStrategyI.EXPECT().UnrelayedPackets(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockSrcRelayPackets, nil).AnyTimes()
				mockStrategyI.EXPECT().UnrelayedAcknowledgements(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockSrcRelayPackets, nil).AnyTimes()
				mockStrategyI.EXPECT().UpdateClients(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
				mockStrategyI.EXPECT().RelayPackets(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
				mockStrategyI.EXPECT().RelayAcknowledgements(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(mockRelayMsgs, nil).AnyTimes()
				mockStrategyI.EXPECT().Send(gomock.Any(), gomock.Any(), gomock.Any()).Return().AnyTimes()

			},
			fields: fields{
				src: &ProvableChain{
					Chain:  mockSrcChain,
					Prover: mockSrcProver,
				},
				dst: &ProvableChain{
					Chain:  mockDstChain,
					Prover: mockDstProver,
				},
				st:       mockStrategyI,
				sh:       mockSyncHeaders,
				interval: 0,
			},
			args: args{
				seqs: &RelayPackets{
					Src: mockPacketInfoList,
					Dst: mockPacketInfoList,
				},
			},
			want:  true,
			want1: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.prepareMock()
			srv := &RelayService{
				src:      tt.fields.src,
				dst:      tt.fields.dst,
				st:       tt.fields.st,
				sh:       tt.fields.sh,
				interval: tt.fields.interval,
			}
			got, got1 := srv.shouldExecuteRelay(tt.args.seqs)
			if got != tt.want {
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("RelayService.shouldExecuteRelay() differs: (-got +want)n%s", diff)
				}
				t.Errorf("RelayService.shouldExecuteRelay() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				if diff := cmp.Diff(got1, tt.want1); diff != "" {
					t.Errorf("RelayService.shouldExecuteRelay() differs: (-got1 +want1)n%s", diff)
				}
				t.Errorf("RelayService.shouldExecuteRelay() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

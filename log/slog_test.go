package log

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"testing"
	"time"
)

func TestDcjpyLogger_InitLogger(t *testing.T) {
	var buf bytes.Buffer
	type args struct {
		logLevel string
		format   string
		output   io.Writer
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "can be initialized with debug, text",
			args: args{
				logLevel: "Debug",
				format:   "text",
				output:   &buf,
			},
		},
		{
			name: "can be initialized with debug, json",
			args: args{
				logLevel: "Debug",
				format:   "json",
				output:   &buf,
			},
		},
		{
			name: "can be initialized with info, text",
			args: args{
				logLevel: "Info",
				format:   "text",
				output:   &buf,
			},
		},
		{
			name: "can be initialized with info, json",
			args: args{
				logLevel: "Info",
				format:   "json",
				output:   &buf,
			},
		},
		{
			name: "can be initialized with warn, text",
			args: args{
				logLevel: "Warn",
				format:   "text",
				output:   &buf,
			},
		},
		{
			name: "can be initialized with warn, json",
			args: args{
				logLevel: "Warn",
				format:   "json",
				output:   &buf,
			},
		},
		{
			name: "can be initialized with error, text",
			args: args{
				logLevel: "Error",
				format:   "text",
				output:   &buf,
			},
		},
		{
			name: "can be initialized with error, json",
			args: args{
				logLevel: "Error",
				format:   "json",
				output:   &buf,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer func() {
				buf.Reset()
			}()

			err := InitLogger(tt.args.logLevel, tt.args.format, tt.args.output)
			if err != nil {
				t.Errorf("got = %v", err)
			}
		})
	}

	errorTests := []struct {
		name string
		args args
	}{
		{
			name: "should return an error when logLevel is invalid",
			args: args{
				logLevel: "none",
				format:   "text",
				output:   &buf,
			},
		},
		{
			name: "should return an error when format is invalid",
			args: args{
				logLevel: "Info",
				format:   "none",
				output:   &buf,
			},
		},
	}
	for _, tt := range errorTests {
		t.Run(tt.name, func(t *testing.T) {
			defer func() {
				buf.Reset()
			}()

			err := InitLogger(tt.args.logLevel, tt.args.format, tt.args.output)
			if err == nil {
				t.Errorf("did not return an error")
			}
		})
	}

	t.Run("should be json format", func(t *testing.T) {
		err := InitLogger("Info", "json", &buf)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		logger := GetLogger()
		logger.Info("test message")

		var js json.RawMessage
		err = json.Unmarshal(buf.Bytes(), &js)
		if err != nil {
			t.Errorf("log format is not json")
		}
	})

	t.Run("should not be json format", func(t *testing.T) {
		err := InitLogger("Info", "text", &buf)
		if err != nil {
			t.Errorf("got = %v", err)
		}
		logger := GetLogger()
		logger.Info("test message")

		var js json.RawMessage
		err = json.Unmarshal(buf.Bytes(), &js)
		if err == nil {
			t.Errorf("log format is json")
		}
	})
}

func TestDcjpyLogger_Info(t *testing.T) {
	tests := []struct {
		name        string
		logLevel    string
		shouldPrint bool
	}{
		{
			name:        "should print message when loglevel is debug",
			logLevel:    "Debug",
			shouldPrint: true,
		},
		{
			name:        "should print message when loglevel is info",
			logLevel:    "Info",
			shouldPrint: true,
		},
		{
			name:        "should not print message when loglevel is warn",
			logLevel:    "Warn",
			shouldPrint: false,
		},
		{
			name:        "should not print message when loglevel is error",
			logLevel:    "Error",
			shouldPrint: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var buf bytes.Buffer
			err := InitLogger(tt.logLevel, "text", &buf)
			if err != nil {
				t.Errorf("got = %v", err)
			}

			logger := GetLogger()
			message := "test message"
			logger.Info(message)

			out := buf.String()
			isPrinted := strings.Contains(out, message)
			if !(isPrinted == tt.shouldPrint) {
				t.Errorf("printed = %v, shouldPrint = %v", isPrinted, tt.shouldPrint)
			}
		})
	}
}

func TestDcjpyLogger_Warn(t *testing.T) {
	tests := []struct {
		name        string
		logLevel    string
		shouldPrint bool
	}{
		{
			name:        "should print message when loglevel is debug",
			logLevel:    "Debug",
			shouldPrint: true,
		},
		{
			name:        "should print message when loglevel is info",
			logLevel:    "Info",
			shouldPrint: true,
		},
		{
			name:        "should print message when loglevel is warn",
			logLevel:    "Warn",
			shouldPrint: true,
		},
		{
			name:        "should not print message when loglevel is error",
			logLevel:    "Error",
			shouldPrint: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var buf bytes.Buffer
			err := InitLogger(tt.logLevel, "text", &buf)
			if err != nil {
				t.Errorf("got = %v", err)
			}

			logger := GetLogger()
			message := "test message"
			logger.Warn(message)

			out := buf.String()
			isPrinted := strings.Contains(out, message)
			if !(isPrinted == tt.shouldPrint) {
				t.Errorf("printed = %v, shouldPrint = %v", isPrinted, tt.shouldPrint)
			}
		})
	}
}

func TestDcjpyLogger_Error(t *testing.T) {
	tests := []struct {
		name        string
		logLevel    string
		shouldPrint bool
	}{
		{
			name:        "should print message when loglevel is debug",
			logLevel:    "Debug",
			shouldPrint: true,
		},
		{
			name:        "should print message when loglevel is info",
			logLevel:    "Info",
			shouldPrint: true,
		},
		{
			name:        "should print message when loglevel is warn",
			logLevel:    "Warn",
			shouldPrint: true,
		},
		{
			name:        "should print message when loglevel is error",
			logLevel:    "Error",
			shouldPrint: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var buf bytes.Buffer
			err := InitLogger(tt.logLevel, "text", &buf)
			if err != nil {
				t.Errorf("got = %v", err)
			}

			logger := GetLogger()
			message := "test message"
			e := fmt.Errorf("error message")
			logger.Error(message, e)

			out := buf.String()
			isPrinted := strings.Contains(out, message) && strings.Contains(out, e.Error())
			if !(isPrinted == tt.shouldPrint) {
				t.Errorf("printed = %v, shouldPrint = %v", isPrinted, tt.shouldPrint)
			}
		})
	}
}

func TestDcjpyLogger_Fatal(t *testing.T) {
	tests := []struct {
		name        string
		logLevel    string
		shouldPrint bool
	}{
		{
			name:        "should print message when loglevel is debug",
			logLevel:    "Debug",
			shouldPrint: true,
		},
		{
			name:        "should print message when loglevel is info",
			logLevel:    "Info",
			shouldPrint: true,
		},
		{
			name:        "should print message when loglevel is warn",
			logLevel:    "Warn",
			shouldPrint: true,
		},
		{
			name:        "should print message when loglevel is error",
			logLevel:    "Error",
			shouldPrint: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer func() {
				if err := recover(); err == nil {
					t.Errorf("did not panic")
				}
			}()

			var buf bytes.Buffer
			err := InitLogger(tt.logLevel, "text", &buf)
			if err != nil {
				t.Errorf("got = %v", err)
			}

			logger := GetLogger()
			message := "test message"
			e := fmt.Errorf("error message")
			logger.Fatal(message, e)

			out := buf.String()
			isPrinted := strings.Contains(out, message) && strings.Contains(out, e.Error())
			if !(isPrinted == tt.shouldPrint) {
				t.Errorf("printed = %v, shouldPrint = %v", isPrinted, tt.shouldPrint)
			}
		})
	}
}

func TestDcjpyLogger_WithChain(t *testing.T) {
	tests := []struct {
		name    string
		chainId string
	}{
		{
			name:    "should print chain_id",
			chainId: "1111",
		},
		{
			name:    "should print chain_id",
			chainId: "1234",
		},
	}

	for _, tt := range tests {
		var buf bytes.Buffer
		defer func() {
			buf.Reset()
		}()
		err := InitLogger("Info", "text", &buf)
		if err != nil {
			t.Errorf("got = %v", err)
		}

		logger := GetLogger()

		t.Run(tt.name, func(t *testing.T) {
			logger = logger.WithChain(tt.chainId)
			message := "test message"
			logger.Info(message)

			out := buf.String()
			if !strings.Contains(out, message) || !strings.Contains(out, fmt.Sprintf("chain_id=%s", tt.chainId)) {
				t.Errorf("got = %v. should print %v, chain_id=%v", out, message, tt.chainId)
			}
		})
	}
}

func TestDcjpyLogger_WithChainPair(t *testing.T) {
	tests := []struct {
		name       string
		srcChainID string
		dstChainID string
	}{
		{
			name:       "should print src.chain_id, dst.chain_id",
			srcChainID: "1111",
			dstChainID: "1234",
		},
		{
			name:       "should print src.chain_id, dst.chain_id",
			srcChainID: "1234",
			dstChainID: "1111",
		},
	}

	for _, tt := range tests {
		var buf bytes.Buffer
		defer func() {
			buf.Reset()
		}()
		err := InitLogger("Info", "text", &buf)
		if err != nil {
			t.Errorf("got = %v", err)
		}

		logger := GetLogger()

		t.Run(tt.name, func(t *testing.T) {
			logger = logger.WithChainPair(tt.srcChainID, tt.dstChainID)
			message := "test message"
			logger.Info(message)

			out := buf.String()
			if !strings.Contains(out, message) ||
				!strings.Contains(out, fmt.Sprintf("src.chain_id=%s", tt.srcChainID)) ||
				!strings.Contains(out, fmt.Sprintf("dst.chain_id=%s", tt.dstChainID)) {
				t.Errorf("got = %v. should print %v, src.chain_id=%v, dst.chain_id=%v", out, message, tt.srcChainID, tt.dstChainID)
			}
		})
	}
}

func TestDcjpyLogger_WithClientPair(t *testing.T) {
	tests := []struct {
		name        string
		srcChainID  string
		srcClientID string
		dstChainID  string
		dstClientID string
	}{
		{
			name:        "should print src.chain_id, src.client_id, dst.chain_id, dst.client_id",
			srcChainID:  "1111",
			srcClientID: "2222",
			dstChainID:  "1234",
			dstClientID: "4321",
		},
		{
			name:        "should print src.chain_id, src.client_id, dst.chain_id, dst.client_id",
			srcChainID:  "1234",
			srcClientID: "4321",
			dstChainID:  "1111",
			dstClientID: "2222",
		},
	}

	for _, tt := range tests {
		var buf bytes.Buffer
		defer func() {
			buf.Reset()
		}()
		err := InitLogger("Info", "text", &buf)
		if err != nil {
			t.Errorf("got = %v", err)
		}

		logger := GetLogger()

		t.Run(tt.name, func(t *testing.T) {
			logger = logger.WithClientPair(tt.srcChainID, tt.srcClientID, tt.dstChainID, tt.dstClientID)
			message := "test message"
			logger.Info(message)

			out := buf.String()
			if !strings.Contains(out, message) ||
				!strings.Contains(out, fmt.Sprintf("src.chain_id=%s", tt.srcChainID)) ||
				!strings.Contains(out, fmt.Sprintf("src.client_id=%s", tt.srcClientID)) ||
				!strings.Contains(out, fmt.Sprintf("dst.chain_id=%s", tt.dstChainID)) ||
				!strings.Contains(out, fmt.Sprintf("dst.client_id=%s", tt.dstClientID)) {
				t.Errorf("got = %v, should print %v and src.chain_id=%v and dst.chain_id=%v", out, message, tt.srcChainID, tt.dstChainID)
			}
		})
	}
}

func TestDcjpyLogger_WithChannel(t *testing.T) {
	tests := []struct {
		name      string
		chainId   string
		portId    string
		channelId string
	}{
		{
			name:      "should print chain_id, port_id, channel_id",
			chainId:   "1111",
			portId:    "2222",
			channelId: "3333",
		},
		{
			name:      "should print chain_id, port_id, channel_id",
			chainId:   "1212",
			portId:    "2323",
			channelId: "3434",
		},
	}

	for _, tt := range tests {
		var buf bytes.Buffer
		defer func() {
			buf.Reset()
		}()
		err := InitLogger("Info", "text", &buf)
		if err != nil {
			t.Errorf("got = %v", err)
		}

		logger := GetLogger()

		t.Run(tt.name, func(t *testing.T) {
			logger = logger.WithChannel(tt.chainId, tt.portId, tt.channelId)
			message := "test message"
			logger.Info(message)

			out := buf.String()
			if !strings.Contains(out, message) ||
				!strings.Contains(out, fmt.Sprintf("chain_id=%s", tt.chainId)) ||
				!strings.Contains(out, fmt.Sprintf("port_id=%s", tt.portId)) ||
				!strings.Contains(out, fmt.Sprintf("channel_id=%s", tt.channelId)) {
				t.Errorf("got = %v. should print %v, chain_id=%v, port_id=%v, channel_id=%v", out, message, tt.chainId, tt.portId, tt.channelId)
			}
		})
	}
}

func TestDcjpyLogger_WithChannelPair(t *testing.T) {
	tests := []struct {
		name         string
		srcChainID   string
		srcPortID    string
		srcChannelID string
		dstChainID   string
		dstPortID    string
		dstChannelID string
	}{
		{
			name:         "should print src.chain_id, src.port_id, src.channel_id, dst.chain_id, dst.port_id, dst.channel_id",
			srcChainID:   "1111",
			srcPortID:    "2222",
			srcChannelID: "3333",
			dstChainID:   "4444",
			dstPortID:    "5555",
			dstChannelID: "6666",
		},
		{
			name:         "should print src.chain_id, src.port_id, src.channel_id, dst.chain_id, dst.port_id, dst.channel_id",
			srcChainID:   "4444",
			srcPortID:    "5555",
			srcChannelID: "6666",
			dstChainID:   "1111",
			dstPortID:    "2222",
			dstChannelID: "3333",
		},
	}

	for _, tt := range tests {
		var buf bytes.Buffer
		defer func() {
			buf.Reset()
		}()
		err := InitLogger("Info", "text", &buf)
		if err != nil {
			t.Errorf("got = %v", err)
		}

		logger := GetLogger()

		t.Run(tt.name, func(t *testing.T) {
			logger = logger.WithChannelPair(tt.srcChainID, tt.srcPortID, tt.srcChannelID, tt.dstChainID, tt.dstPortID, tt.dstChannelID)
			message := "test message"
			logger.Info(message)

			out := buf.String()
			if !strings.Contains(out, message) ||
				!strings.Contains(out, fmt.Sprintf("src.chain_id=%s", tt.srcChainID)) ||
				!strings.Contains(out, fmt.Sprintf("src.port_id=%s", tt.srcPortID)) ||
				!strings.Contains(out, fmt.Sprintf("src.channel_id=%s", tt.srcChannelID)) ||
				!strings.Contains(out, fmt.Sprintf("dst.chain_id=%s", tt.dstChainID)) ||
				!strings.Contains(out, fmt.Sprintf("dst.port_id=%s", tt.dstPortID)) ||
				!strings.Contains(out, fmt.Sprintf("dst.channel_id=%s", tt.dstChannelID)) {
				t.Errorf("got = %v. should print %v, src.chain_id=%v, src.port_id=%v, src.channel_id=%v, dst.chain_id=%v, dst.port_id=%v, dst.channel_id=%v", out, message, tt.srcChainID, tt.srcPortID, tt.srcChannelID, tt.dstChainID, tt.dstPortID, tt.dstChannelID)
			}
		})
	}
}

func TestDcjpyLogger_WithConnectionPair(t *testing.T) {
	tests := []struct {
		name            string
		srcChainID      string
		srcClientID     string
		srcConnectionID string
		dstChainID      string
		dstClientID     string
		dstConnectionID string
	}{
		{
			name:            "should print src.chain_id, src.port_id, src.channel_id, dst.chain_id, dst.port_id, dst.channel_id",
			srcChainID:      "1111",
			srcClientID:     "2222",
			srcConnectionID: "3333",
			dstChainID:      "4444",
			dstClientID:     "5555",
			dstConnectionID: "6666",
		},
		{
			name:            "should print src.chain_id, src.port_id, src.channel_id, dst.chain_id, dst.port_id, dst.channel_id",
			srcChainID:      "4444",
			srcClientID:     "5555",
			srcConnectionID: "6666",
			dstChainID:      "1111",
			dstClientID:     "2222",
			dstConnectionID: "3333",
		},
	}

	for _, tt := range tests {
		var buf bytes.Buffer
		defer func() {
			buf.Reset()
		}()
		err := InitLogger("Info", "text", &buf)
		if err != nil {
			t.Errorf("got = %v", err)
		}

		logger := GetLogger()

		t.Run(tt.name, func(t *testing.T) {
			logger = logger.WithConnectionPair(tt.srcChainID, tt.srcClientID, tt.srcConnectionID, tt.dstChainID, tt.dstClientID, tt.dstConnectionID)
			message := "test message"
			logger.Info(message)

			out := buf.String()
			if !strings.Contains(out, message) ||
				!strings.Contains(out, fmt.Sprintf("src.chain_id=%s", tt.srcChainID)) ||
				!strings.Contains(out, fmt.Sprintf("src.client_id=%s", tt.srcClientID)) ||
				!strings.Contains(out, fmt.Sprintf("src.connection_id=%s", tt.srcConnectionID)) ||
				!strings.Contains(out, fmt.Sprintf("dst.chain_id=%s", tt.dstChainID)) ||
				!strings.Contains(out, fmt.Sprintf("dst.client_id=%s", tt.dstClientID)) ||
				!strings.Contains(out, fmt.Sprintf("dst.connection_id=%s", tt.dstConnectionID)) {
				t.Errorf("got = %v. should print %v, src.chain_id=%v, src.client_id=%v, src.connection_id=%v, dst.chain_id=%v, dst.client_id=%v, dst.connection_id=%v", out, message, tt.srcChainID, tt.srcClientID, tt.srcConnectionID, tt.dstChainID, tt.dstClientID, tt.dstConnectionID)
			}
		})
	}
}

func TestDcjpyLogger_WithModule(t *testing.T) {
	tests := []struct {
		name   string
		module string
	}{
		{
			name:   "should print module",
			module: "module1",
		},
		{
			name:   "should print module",
			module: "module2",
		},
	}

	for _, tt := range tests {
		var buf bytes.Buffer
		defer func() {
			buf.Reset()
		}()
		err := InitLogger("Info", "text", &buf)
		if err != nil {
			t.Errorf("got = %v", err)
		}

		logger := GetLogger()

		t.Run(tt.name, func(t *testing.T) {
			logger = logger.WithModule(tt.module)
			message := "test message"
			logger.Info(message)

			out := buf.String()
			if !strings.Contains(out, message) ||
				!strings.Contains(out, fmt.Sprintf("module=%s", tt.module)) {
				t.Errorf("got = %v. should print %v, module=%v", out, message, tt.module)
			}
		})
	}
}

func TestDcjpyLogger_TimeTrack(t *testing.T) {
	message := "message111"

	tests := []struct {
		testName  string
		start     time.Time
		name      string
		otherArgs string
	}{
		{
			testName:  "should print time track",
			start:     time.Now(),
			name:      "moduleA",
			otherArgs: message,
		},
		{
			testName:  "should print time track",
			start:     time.Now(),
			name:      "moduleB",
			otherArgs: message,
		},
	}

	for _, tt := range tests {
		var buf bytes.Buffer
		err := InitLogger("Debug", "text", &buf)
		if err != nil {
			t.Errorf("got = %v", err)
		}

		defer func() {
			buf.Reset()
		}()

		logger := GetLogger()

		t.Run(tt.testName, func(t *testing.T) {
			logger.TimeTrack(tt.start, tt.name, tt.otherArgs)

			out := buf.String()
			if !strings.Contains(out, "time track") ||
				!strings.Contains(out, message) ||
				!strings.Contains(out, fmt.Sprintf("name=%s", tt.name)) ||
				!strings.Contains(out, "elapsed") {
				t.Errorf("got = %v. should print 'time track','elapsed', %v, name=%v", out, message, tt.name)
			}
		})
	}

	t.Run("should not print when logLevel is info", func(t *testing.T) {
		var buf bytes.Buffer
		err := InitLogger("Info", "text", &buf)
		if err != nil {
			t.Errorf("got = %v", err)
		}

		logger := GetLogger()

		logger.TimeTrack(time.Now(), "moduleA")

		out := buf.String()
		if out != "" {
			t.Errorf("got = %v. should not print", out)
		}
	})
}

func Test_isContainWarnStr(t *testing.T) {
	type args struct {
		err string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "not match str.",
			args: args{
				err: "error message.",
			},
			want: false,
		},
		{
			name: "match packet commitment not found.",
			args: args{
				err: "Execution reverted: packet commitment not found",
			},
			want: true,
		},
		{
			name: "match packet receipt already exists.",
			args: args{
				err: "Execution reverted: packet receipt already exists",
			},
			want: true,
		},
		{
			name: "match Known transaction.",
			args: args{
				err: "Known transaction",
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isContainWarnErrors(tt.args.err); got != tt.want {
				t.Errorf("isContainWarnErrors() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDcjpyLogger_ErrorStr(t *testing.T) {
	type args struct {
		msg       string
		err       error
		otherArgs []any
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "not match",
			args: args{
				msg:       "failed to get latest finalized header",
				err:       fmt.Errorf("not found"),
				otherArgs: nil,
			},
		},
		{
			name: "match packet commitment not found.",
			args: args{
				msg:       "failed to estimate gas",
				err:       fmt.Errorf("Execution reverted: packet commitment not found\n(1) attached stack trace\n  -- stack trace:\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/relay/ethereum.(*Chain).SendMsgs\n  | \t/opt/relayer/pkg/relay/ethereum/tx.go:53\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/core.(*RelayMsgs).Send\n  | \t/opt/relayer/core/relayMsgs.go:151\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/core.(*NaiveStrategy).Send\n  | \t/opt/relayer/core/naive-strategy.go:564\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/core.(*RelayService).Serve\n  | \t/opt/relayer/core/service.go:136\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/core.(*RelayService).Start.func1\n  | \t/opt/relayer/core/service.go:66\n  | github.com/avast/retry-go.Do\n  | \t/go/pkg/mod/github.com/avast/retry-go@v3.0.0+incompatible/retry.go:127\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/core.(*RelayService).Start\n  | \t/opt/relayer/core/service.go:61\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/core.StartService\n  | \t/opt/relayer/core/service.go:30\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/cmd.startCmd.func1\n  | \t/opt/relayer/cmd/service.go:65\n  | github.com/spf13/cobra.(*Command).execute\n  | \t/go/pkg/mod/github.com/spf13/cobra@v1.7.0/command.go:940\n  | github.com/spf13/cobra.(*Command).ExecuteC\n  | \t/go/pkg/mod/github.com/spf13/cobra@v1.7.0/command.go:1068\n  | github.com/spf13/cobra.(*Command).Execute\n  | \t/go/pkg/mod/github.com/spf13/cobra@v1.7.0/command.go:992\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/cmd.Execute\n  | \t/opt/relayer/cmd/root.go:104\n  | main.main\n  | \t/opt/relayer/main.go:18\n  | runtime.main\n  | \t/usr/local/go/src/runtime/proc.go:267\n  | runtime.goexit\n  | \t/usr/local/go/src/runtime/asm_arm64.s:1197\nWraps: (2) Execution reverted: packet commitment not found\nError types: (1) *withstack.withStack (2) *rpc.jsonError"),
				otherArgs: nil,
			},
		},
		{
			name: "match packet receipt already exists.",
			args: args{
				msg:       "failed to estimate gas",
				err:       fmt.Errorf("Execution reverted: packet receipt already exists\n(1) attached stack trace\n  -- stack trace:\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/relay/ethereum.(*Chain).SendMsgs\n  | \t/opt/relayer/pkg/relay/ethereum/tx.go:53\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/core.(*RelayMsgs).Send\n  | \t/opt/relayer/core/relayMsgs.go:102\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/core.(*NaiveStrategy).Send\n  | \t/opt/relayer/core/naive-strategy.go:564\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/core.(*RelayService).Serve\n  | \t/opt/relayer/core/service.go:136\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/core.(*RelayService).Start.func1\n  | \t/opt/relayer/core/service.go:66\n  | github.com/avast/retry-go.Do\n  | \t/go/pkg/mod/github.com/avast/retry-go@v3.0.0+incompatible/retry.go:127\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/core.(*RelayService).Start\n  | \t/opt/relayer/core/service.go:61\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/core.StartService\n  | \t/opt/relayer/core/service.go:30\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/cmd.startCmd.func1\n  | \t/opt/relayer/cmd/service.go:65\n  | github.com/spf13/cobra.(*Command).execute\n  | \t/go/pkg/mod/github.com/spf13/cobra@v1.7.0/command.go:940\n  | github.com/spf13/cobra.(*Command).ExecuteC\n  | \t/go/pkg/mod/github.com/spf13/cobra@v1.7.0/command.go:1068\n  | github.com/spf13/cobra.(*Command).Execute\n  | \t/go/pkg/mod/github.com/spf13/cobra@v1.7.0/command.go:992\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/cmd.Execute\n  | \t/opt/relayer/cmd/root.go:104\n  | main.main\n  | \t/opt/relayer/main.go:18\n  | runtime.main\n  | \t/usr/local/go/src/runtime/proc.go:267\n  | runtime.goexit\n  | \t/usr/local/go/src/runtime/asm_arm64.s:1197\nWraps: (2) Execution reverted: packet receipt already exists\nError types: (1) *withstack.withStack (2) *rpc.jsonError"),
				otherArgs: nil,
			},
		},
		{
			name: "match Known transaction.",
			args: args{
				msg:       "failed to send msg / NoSend: false",
				err:       fmt.Errorf("Known transaction\n(1) attached stack trace\n  -- stack trace:\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/pkg/relay/ethereum.(*Chain).SendMsgs\n  | \t/opt/relayer/pkg/relay/ethereum/tx.go:66\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/core.(*RelayMsgs).Send\n  | \t/opt/relayer/core/relayMsgs.go:102\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/core.(*NaiveStrategy).Send\n  | \t/opt/relayer/core/naive-strategy.go:564\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/core.(*RelayService).Serve\n  | \t/opt/relayer/core/service.go:136\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/core.(*RelayService).Start.func1\n  | \t/opt/relayer/core/service.go:66\n  | github.com/avast/retry-go.Do\n  | \t/go/pkg/mod/github.com/avast/retry-go@v3.0.0+incompatible/retry.go:127\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/core.(*RelayService).Start\n  | \t/opt/relayer/core/service.go:61\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/core.StartService\n  | \t/opt/relayer/core/service.go:30\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/cmd.startCmd.func1\n  | \t/opt/relayer/cmd/service.go:65\n  | github.com/spf13/cobra.(*Command).execute\n  | \t/go/pkg/mod/github.com/spf13/cobra@v1.7.0/command.go:940\n  | github.com/spf13/cobra.(*Command).ExecuteC\n  | \t/go/pkg/mod/github.com/spf13/cobra@v1.7.0/command.go:1068\n  | github.com/spf13/cobra.(*Command).Execute\n  | \t/go/pkg/mod/github.com/spf13/cobra@v1.7.0/command.go:992\n  | github.com/decurret-lab/dcbg-dcjpy-relayer/cmd.Execute\n  | \t/opt/relayer/cmd/root.go:104\n  | main.main\n  | \t/opt/relayer/main.go:18\n  | runtime.main\n  | \t/usr/local/go/src/runtime/proc.go:267\n  | runtime.goexit\n  | \t/usr/local/go/src/runtime/asm_arm64.s:1197\nWraps: (2) Known transaction\nError types: (1) *withstack.withStack (2) *rpc.jsonError"),
				otherArgs: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var buf bytes.Buffer
			err := InitLogger("Error", "text", &buf)
			if err != nil {
				t.Errorf("got = %v", err)
			}
			rl := GetLogger()
			rl.Error(tt.args.msg, tt.args.err, tt.args.otherArgs...)
		})
	}
}

//go:generate gotests -w -all $GOFILE
package log

import (
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/cockroachdb/errors/withstack"
	"golang.org/x/exp/slog"
)

type DcjpyLogger struct {
	*slog.Logger
}

var dcjpyLogger *DcjpyLogger

func InitLogger(logLevel, format string, output io.Writer) error {
	// level
	var slogLevel slog.Level
	if err := slogLevel.UnmarshalText([]byte(logLevel)); err != nil {
		return fmt.Errorf("failed to unmarshal level: %v", err)
	}
	handlerOpts := &slog.HandlerOptions{
		Level:     slogLevel,
		AddSource: true,
	}

	var slogLogger *slog.Logger
	// format
	switch format {
	case "text":
		slogLogger = slog.New(slog.NewTextHandler(
			output,
			handlerOpts,
		))
	case "json":
		slogLogger = slog.New(slog.NewJSONHandler(
			output,
			handlerOpts,
		))
	default:
		return errors.New("invalid log format")
	}

	// set global logger
	dcjpyLogger = &DcjpyLogger{
		slogLogger,
	}
	return nil
}

func GetLogger() *DcjpyLogger {
	return dcjpyLogger
}

func (rl *DcjpyLogger) Info(msg string, otherArgs ...any) {
	var args []any
	args = append(args, otherArgs...)
	rl.Logger.Info(msg, args...)
}

func (rl *DcjpyLogger) Warn(msg string, otherArgs ...any) {
	var args []any
	args = append(args, otherArgs...)
	rl.Logger.Warn(msg, args...)
}

func (rl *DcjpyLogger) Error(msg string, err error, otherArgs ...any) {
	err = withstack.WithStackDepth(err, 1)
	var args []any
	args = append(args, "error", err)

	if !isContainWarnErrors(err.Error()) {
		args = append(args, "stack", fmt.Sprintf("%+v", err))
		args = append(args, otherArgs...)
		rl.Logger.Error(msg, args...)
	} else {
		rl.Logger.Warn(msg, args...)
	}
}

func (rl *DcjpyLogger) Fatal(msg string, err error, otherArgs ...any) {
	rl.Error(msg, err, otherArgs...)
	panic(msg)
}

func (rl *DcjpyLogger) WithChain(
	chainID string,
) *DcjpyLogger {
	return &DcjpyLogger{
		rl.Logger.With(
			"chain_id", chainID,
		),
	}
}

func (rl *DcjpyLogger) WithChainPair(
	srcChainID string,
	dstChainID string,
) *DcjpyLogger {
	return &DcjpyLogger{
		rl.Logger.With(
			slog.Group("src",
				"chain_id", srcChainID,
			),
			slog.Group("dst",
				"chain_id", dstChainID,
			),
		),
	}
}

func (rl *DcjpyLogger) WithClientPair(
	srcChainID, srcClientID string,
	dstChainID, dstClientID string,
) *DcjpyLogger {
	return &DcjpyLogger{
		rl.Logger.With(
			slog.Group("src",
				"chain_id", srcChainID,
				"client_id", srcClientID,
			),
			slog.Group("dst",
				"chain_id", dstChainID,
				"client_id", dstClientID,
			),
		),
	}
}

func (rl *DcjpyLogger) WithChannel(
	chainID, portID, channelID string,
) *DcjpyLogger {
	return &DcjpyLogger{
		rl.Logger.With(
			"chain_id", chainID,
			"port_id", portID,
			"channel_id", channelID,
		),
	}
}

func (rl *DcjpyLogger) WithChannelPair(
	srcChainID, srcPortID, srcChannelID string,
	dstChainID, dstPortID, dstChannelID string,
) *DcjpyLogger {
	return &DcjpyLogger{
		rl.Logger.With(
			slog.Group("src",
				"chain_id", srcChainID,
				"port_id", srcPortID,
				"channel_id", srcChannelID,
			),
			slog.Group("dst",
				"chain_id", dstChainID,
				"port_id", dstPortID,
				"channel_id", dstChannelID,
			),
		),
	}
}

func (rl *DcjpyLogger) WithConnectionPair(
	srcChainID, srcClientID, srcConnectionID string,
	dstChainID, dstClientID, dstConnectionID string,
) *DcjpyLogger {
	return &DcjpyLogger{
		rl.Logger.With(
			slog.Group("src",
				"chain_id", srcChainID,
				"client_id", srcClientID,
				"connection_id", srcConnectionID,
			),
			slog.Group("dst",
				"chain_id", dstChainID,
				"client_id", dstClientID,
				"connection_id", dstConnectionID,
			),
		),
	}
}
func (rl *DcjpyLogger) WithModule(
	moduleName string,
) *DcjpyLogger {
	return &DcjpyLogger{
		rl.Logger.With(
			"module", moduleName,
		),
	}
}

func (rl *DcjpyLogger) TimeTrack(start time.Time, name string, otherArgs ...any) {
	elapsed := time.Since(start)
	allArgs := append([]any{"name", name, "elapsed", elapsed.Nanoseconds()}, otherArgs...)
	rl.Logger.Debug("time track", allArgs...)
}

func isContainWarnErrors(err string) bool {

	warnErrors := [...]string{
		"packet commitment not found",
		"packet receipt already exists",
		"Known transaction",
	}

	for _, warnStr := range warnErrors {
		if strings.Contains(err, warnStr) {
			return true
		}
	}
	return false
}
